# Gorbagana Grand Prix 🏎️

A fast-paced multiplayer token-powered racing game built for the Gorbagana testnet bounty.

## 🎮 Game Overview

Gorbagana Grand Prix is a competitive multiplayer racing game where players use GORB tokens to:
- Enter races (entry fee)
- Purchase power-ups during races
- Compete for token prizes

### Key Features

- **Real-time Multiplayer**: Up to 8 players per race
- **Token-Powered Gameplay**: Use GORB tokens strategically for power-ups
- **Instant Finality**: Built on <PERSON>rbagan<PERSON>'s lightning-fast testnet
- **Backpack Wallet Integration**: Seamless wallet connectivity
- **Prize Distribution**: Winners earn token rewards

### Power-ups

- **Speed Boost** (10 GORB): 50% speed increase for 5 seconds
- **Shield** (15 GORB): Immunity to obstacles for 8 seconds
- **Shortcut** (25 GORB): Skip 20% of the track instantly
- **Double Tokens** (20 GORB): 2x token rewards for 10 seconds

## 🏗️ Technical Architecture

### Frontend
- **Next.js 14** with TypeScript
- **Tailwind CSS** for styling
- **Solana Wallet Adapter** for wallet integration
- **Socket.io** for real-time multiplayer

### Blockchain Integration
- **Solana/Gorbagana testnet** for token transactions
- **Backpack wallet** support (required)
- **Native test tokens** for gameplay

### Game Engine
- Custom racing engine with real-time physics
- Token-based power-up system
- Multiplayer synchronization
- Prize distribution logic

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Backpack wallet browser extension
- Gorbagana testnet tokens

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd gorbagana-racing
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Getting Testnet Tokens

To get GORB testnet tokens for playing:
1. Join the [Gorbagana Telegram community](https://t.me/gorbagana_portal)
2. Request testnet tokens with a brief description of your testing plans
3. Connect your Backpack wallet to receive tokens

## 🎯 How to Play

1. **Connect Wallet**: Connect your Backpack wallet containing GORB tokens
2. **Join Race**: Pay the entry fee (50 GORB) to join a race lobby
3. **Wait for Players**: Races start when 2-8 players have joined
4. **Race & Use Power-ups**: Use tokens strategically during the race for advantages
5. **Win Prizes**:
   - 1st place: 70% of prize pool
   - 2nd place: 20% of prize pool
   - 3rd place: 10% of prize pool

## 🏆 Bounty Requirements Checklist

- [x] **Multiplayer mini-game**: Real-time racing with up to 8 players
- [x] **Gorbagana testnet integration**: Built for Gorbagana infrastructure
- [x] **Native test tokens usage**: GORB tokens for entry fees and power-ups
- [x] **Backpack wallet support**: Required wallet integration
- [x] **Working prototype**: Live demo available
- [x] **README documentation**: Comprehensive setup and gameplay instructions
- [ ] **Social media promotion**: Tweet with #GorbaganaTestnet hashtag

## 🛠️ Development Status

### Completed ✅
- [x] Project setup with Next.js and TypeScript
- [x] Wallet integration with Backpack support
- [x] Game engine architecture
- [x] UI/UX design and layout
- [x] Token-powered gameplay mechanics
- [x] Type definitions and interfaces

### In Progress 🚧
- [ ] Real-time multiplayer implementation
- [ ] Blockchain transaction handling
- [ ] Race lobby and matchmaking
- [ ] Power-up purchase system
- [ ] Prize distribution logic

### Planned 📋
- [ ] Testing and debugging
- [ ] Deployment to live URL
- [ ] Performance optimization
- [ ] Social media content creation

## 🔧 Project Structure

```
gorbagana-racing/
├── src/
│   ├── app/                 # Next.js app router
│   ├── components/          # React components
│   ├── lib/                 # Utility libraries
│   │   ├── wallet.ts        # Wallet configuration
│   │   └── game-engine.ts   # Core game logic
│   └── types/               # TypeScript definitions
├── public/                  # Static assets
└── README.md               # This file
```

## 🌐 Deployment

The game will be deployed to a live URL for the bounty submission. The deployment will include:
- Production-ready build
- Gorbagana testnet integration
- Real-time multiplayer functionality
- Full wallet integration

## 📱 Social Media

Upon completion, the project will be promoted with:
- Demo video showcasing gameplay
- Screenshots of key features
- Tweet with required hashtags: #GorbaganaTestnet
- Mentions: @Gorbagana_chain, @sarv_shaktimaan, @lex_node

## 🤝 Contributing

This project is built for the Gorbagana testnet bounty. For questions or feedback:
- Join the [Gorbagana Telegram](https://t.me/gorbagana_portal)
- Follow development progress in this repository

## 📄 License

Built for the Gorbagana testnet bounty competition.

---

**Built with ❤️ for the Gorbagana community**
