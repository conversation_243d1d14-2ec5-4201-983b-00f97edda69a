{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/decorators/base.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAgBH;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,CAClB,GAAW,EACX,IAAmE,EACnE,UAA8B,EAC9B,EAAE;IACF,yEAAyE;IACzE,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;IAC/B,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;IAC7B;IACE,kEAAkE;IAClE,iDAAiD;IAChD,OAAiD,CAAC,QAAQ;QAC3D,OAAO,IAAI,KAAK,QAAQ,EACxB,CAAC;QACD,yEAAyE;QACzE,kEAAkE;QAClE,gEAAgE;QAEhE,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAC/C,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Generates a public interface type that removes private and protected fields.\n * This allows accepting otherwise incompatible versions of the type (e.g. from\n * multiple copies of the same package in `node_modules`).\n */\nexport type Interface<T> = {\n  [K in keyof T]: T[K];\n};\n\nexport type Constructor<T> = {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  new (...args: any[]): T;\n};\n\n/**\n * Wraps up a few best practices when returning a property descriptor from a\n * decorator.\n *\n * Marks the defined property as configurable, and enumerable, and handles\n * the case where we have a busted Reflect.decorate zombiefill (e.g. in Angular\n * apps).\n *\n * @internal\n */\nexport const desc = (\n  obj: object,\n  name: PropertyKey | ClassAccessorDecoratorContext<unknown, unknown>,\n  descriptor: PropertyDescriptor\n) => {\n  // For backwards compatibility, we keep them configurable and enumerable.\n  descriptor.configurable = true;\n  descriptor.enumerable = true;\n  if (\n    // We check for Reflect.decorate each time, in case the zombiefill\n    // is applied via lazy loading some Angular code.\n    (Reflect as typeof Reflect & {decorate?: unknown}).decorate &&\n    typeof name !== 'object'\n  ) {\n    // If we're called as a legacy decorator, and Reflect.decorate is present\n    // then we have no guarantees that the returned descriptor will be\n    // defined on the class, so we must apply it directly ourselves.\n\n    Object.defineProperty(obj, name, descriptor);\n  }\n  return descriptor;\n};\n"]}