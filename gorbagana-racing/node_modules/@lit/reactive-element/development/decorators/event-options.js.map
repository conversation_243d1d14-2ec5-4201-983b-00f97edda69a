{"version": 3, "file": "event-options.js", "sourceRoot": "", "sources": ["../../src/decorators/event-options.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AA6BH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,UAAU,YAAY,CAC1B,OAAgC;IAEhC,8DAA8D;IAC9D,OAAO,CAAC,CACN,YAAe,EACf,aAA8D,EAC9D,EAAE;QACF,MAAM,MAAM,GACV,OAAO,YAAY,KAAK,UAAU;YAChC,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,YAAY,CAAC,aAAsC,CAAC,CAAC;QAC3D,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACjC,CAAC,CAA0B,CAAC;AAC9B,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport type {ReactiveElement} from '../reactive-element.js';\nimport type {Interface} from './base.js';\n\nexport type EventOptionsDecorator = {\n  // legacy\n  (\n    proto: Interface<ReactiveElement>,\n    name: PropertyKey\n    // Note TypeScript requires the return type to be `void|any`\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ): void | any;\n\n  // standard\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  <C, V extends (this: C, ...args: any) => any>(\n    value: V,\n    _context: ClassMethodDecoratorContext<C, V>\n  ): void;\n};\n\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function eventOptions(\n  options: AddEventListenerOptions\n): EventOptionsDecorator {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (<C, V extends (this: C, ...args: any) => any>(\n    protoOrValue: V,\n    nameOrContext: PropertyKey | ClassMethodDecoratorContext<C, V>\n  ) => {\n    const method =\n      typeof protoOrValue === 'function'\n        ? protoOrValue\n        : protoOrValue[nameOrContext as keyof ReactiveElement];\n    Object.assign(method, options);\n  }) as EventOptionsDecorator;\n}\n"]}