{"name": "@lit/reactive-element", "version": "2.1.0", "publishConfig": {"access": "public"}, "description": "A simple low level base class for creating fast, lightweight web components", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/lit/lit.git", "directory": "packages/reactive-element"}, "author": "Google LLC", "homepage": "https://lit.dev/", "main": "reactive-element.js", "module": "reactive-element.js", "type": "module", "exports": {".": {"types": "./development/reactive-element.d.ts", "browser": {"development": "./development/reactive-element.js", "default": "./reactive-element.js"}, "node": {"development": "./node/development/reactive-element.js", "default": "./node/reactive-element.js"}, "development": "./development/reactive-element.js", "default": "./reactive-element.js"}, "./css-tag.js": {"types": "./development/css-tag.d.ts", "browser": {"development": "./development/css-tag.js", "default": "./css-tag.js"}, "node": {"development": "./node/development/css-tag.js", "default": "./node/css-tag.js"}, "development": "./development/css-tag.js", "default": "./css-tag.js"}, "./decorators.js": {"types": "./development/decorators.d.ts", "browser": {"development": "./development/decorators.js", "default": "./decorators.js"}, "node": {"development": "./node/development/decorators.js", "default": "./node/decorators.js"}, "development": "./development/decorators.js", "default": "./decorators.js"}, "./decorators/base.js": {"types": "./development/decorators/base.d.ts", "browser": {"development": "./development/decorators/base.js", "default": "./decorators/base.js"}, "node": {"development": "./node/development/decorators/base.js", "default": "./node/decorators/base.js"}, "development": "./development/decorators/base.js", "default": "./decorators/base.js"}, "./decorators/custom-element.js": {"types": "./development/decorators/custom-element.d.ts", "browser": {"development": "./development/decorators/custom-element.js", "default": "./decorators/custom-element.js"}, "node": {"development": "./node/development/decorators/custom-element.js", "default": "./node/decorators/custom-element.js"}, "development": "./development/decorators/custom-element.js", "default": "./decorators/custom-element.js"}, "./decorators/event-options.js": {"types": "./development/decorators/event-options.d.ts", "browser": {"development": "./development/decorators/event-options.js", "default": "./decorators/event-options.js"}, "node": {"development": "./node/development/decorators/event-options.js", "default": "./node/decorators/event-options.js"}, "development": "./development/decorators/event-options.js", "default": "./decorators/event-options.js"}, "./decorators/property.js": {"types": "./development/decorators/property.d.ts", "browser": {"development": "./development/decorators/property.js", "default": "./decorators/property.js"}, "node": {"development": "./node/development/decorators/property.js", "default": "./node/decorators/property.js"}, "development": "./development/decorators/property.js", "default": "./decorators/property.js"}, "./decorators/query-all.js": {"types": "./development/decorators/query-all.d.ts", "browser": {"development": "./development/decorators/query-all.js", "default": "./decorators/query-all.js"}, "node": {"development": "./node/development/decorators/query-all.js", "default": "./node/decorators/query-all.js"}, "development": "./development/decorators/query-all.js", "default": "./decorators/query-all.js"}, "./decorators/query-assigned-elements.js": {"types": "./development/decorators/query-assigned-elements.d.ts", "browser": {"development": "./development/decorators/query-assigned-elements.js", "default": "./decorators/query-assigned-elements.js"}, "node": {"development": "./node/development/decorators/query-assigned-elements.js", "default": "./node/decorators/query-assigned-elements.js"}, "development": "./development/decorators/query-assigned-elements.js", "default": "./decorators/query-assigned-elements.js"}, "./decorators/query-assigned-nodes.js": {"types": "./development/decorators/query-assigned-nodes.d.ts", "browser": {"development": "./development/decorators/query-assigned-nodes.js", "default": "./decorators/query-assigned-nodes.js"}, "node": {"development": "./node/development/decorators/query-assigned-nodes.js", "default": "./node/decorators/query-assigned-nodes.js"}, "development": "./development/decorators/query-assigned-nodes.js", "default": "./decorators/query-assigned-nodes.js"}, "./decorators/query-async.js": {"types": "./development/decorators/query-async.d.ts", "browser": {"development": "./development/decorators/query-async.js", "default": "./decorators/query-async.js"}, "node": {"development": "./node/development/decorators/query-async.js", "default": "./node/decorators/query-async.js"}, "development": "./development/decorators/query-async.js", "default": "./decorators/query-async.js"}, "./decorators/query.js": {"types": "./development/decorators/query.d.ts", "browser": {"development": "./development/decorators/query.js", "default": "./decorators/query.js"}, "node": {"development": "./node/development/decorators/query.js", "default": "./node/decorators/query.js"}, "development": "./development/decorators/query.js", "default": "./decorators/query.js"}, "./decorators/state.js": {"types": "./development/decorators/state.d.ts", "browser": {"development": "./development/decorators/state.js", "default": "./decorators/state.js"}, "node": {"development": "./node/development/decorators/state.js", "default": "./node/decorators/state.js"}, "development": "./development/decorators/state.js", "default": "./decorators/state.js"}, "./polyfill-support.js": {"types": "./development/polyfill-support.d.ts", "browser": {"development": "./development/polyfill-support.js", "default": "./polyfill-support.js"}, "node": {"development": "./node/development/polyfill-support.js", "default": "./node/polyfill-support.js"}, "development": "./development/polyfill-support.js", "default": "./polyfill-support.js"}, "./reactive-controller.js": {"types": "./development/reactive-controller.d.ts", "browser": {"development": "./development/reactive-controller.js", "default": "./reactive-controller.js"}, "node": {"development": "./node/development/reactive-controller.js", "default": "./node/reactive-controller.js"}, "development": "./development/reactive-controller.js", "default": "./reactive-controller.js"}}, "scripts": {"build": "wireit", "build:ts": "wireit", "build:ts:std-decorators-tests": "wireit", "build:ts:types": "wireit", "build:rollup": "wireit", "build:babel": "wireit", "build:esbuild": "wireit", "check-version": "wireit", "checksize": "wireit", "prepublishOnly": "npm run check-version", "test": "wireit", "test:dev": "wireit", "test:prod": "wireit", "test:node": "wireit", "test:node-dev": "wireit", "test:node-dom-shim": "wireit", "test:node-dom-shim-dev": "wireit"}, "wireit": {"build": {"dependencies": ["build:rollup", "build:ts", "build:ts:std-decorators-tests", "build:ts:types"]}, "build:ts": {"#comment": "Note this also builds polyfill-support via a TypeScript project reference.", "command": "tsc --build --pretty", "clean": "if-file-deleted", "dependencies": ["../labs/testing:build:ts:utils", "../labs/ssr-dom-shim:build:ts"], "files": ["src/**/*.ts", "tsconfig.json", "tsconfig.polyfill-support.json"], "output": ["development/**/*.{js,js.map,d.ts,d.ts.map}", "!development/test/decorators-babel", "!development/test/std-decorators", "tsconfig.tsbuildinfo", "tsconfig.polyfill-support.tsbuildinfo"]}, "build:ts:types": {"command": "treemirror development . \"**/*.d.ts{,.map}\"", "dependencies": ["../internal-scripts:build", "build:ts"], "files": [], "output": ["*.d.ts{,.map}", "decorators/*.d.ts{,.map}", "legacy-decorators/*.d.ts{,.map}", "std-decorators/*.d.ts{,.map}"]}, "build:ts:std-decorators-tests": {"#comment": "This is a separate script from build:ts because it needs a tsconfig without experimentalDecorators.", "command": "tsc --pretty --project tsconfig.std-decorators-tests.json", "clean": "if-file-deleted", "dependencies": ["build:ts"], "files": ["src/test/decorators-modern/**/*.ts", "tsconfig.std-decorators-tests.json"], "output": ["development/test/std-decorators", "tsconfig.std-decorators-tests.tsbuildinfo"]}, "build:rollup": {"command": "rollup -c", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js", "src/test/*_test.html", "src/test/polyfill-support/*_test.html"], "output": ["css-tag.js{,.map}", "decorators.js{,.map}", "polyfill-support.js{,.map}", "reactive-controller.js{,.map}", "reactive-element.js{,.map}", "decorators/*.js{,.map}", "test/*_test.html", "development/test/*_test.html", "test/polyfill-support/*_test.html", "development/test/polyfill-support/*_test.html", "node/"]}, "build:babel": {"command": "babel --extensions .ts src/test/decorators-modern --out-dir development/test/decorators-babel", "files": [".babelrc", "src/test/decorators-modern/**/*.ts"], "output": ["development/test/decorators-babel"]}, "build:esbuild": {"command": "esbuild src/test/decorators-modern/**/*.ts --outdir=development/test/decorators-esbuild --target=es2022", "files": ["src/test/decorators-modern/**/*.ts"], "output": ["development/test/decorators-esbuild"]}, "checksize": {"command": "rollup -c --environment=CHECKSIZE", "dependencies": ["build:ts"], "files": ["rollup.config.js", "../../rollup-common.js"], "output": []}, "check-version": {"command": "node scripts/check-version-tracker.js", "files": ["scripts/check-version-tracker.js", "package.json", "src/reactive-element.ts"], "output": []}, "test": {"dependencies": ["test:dev", "test:prod", "test:node", "test:node-dev", "test:node-dom-shim", "test:node-dom-shim-dev", "check-version"]}, "test:dev": {"command": "MODE=dev node ../tests/run-web-tests.js \"development/test/**/*_test.js\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "build:babel", "build:esbuild", "build:ts:std-decorators-tests", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:prod": {"command": "MODE=prod node ../tests/run-web-tests.js \"development/**/*_test.(js|html)\" --config ../tests/web-test-runner.config.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "build:rollup", "../tests:build"], "env": {"BROWSERS": {"external": true}}, "files": [], "output": []}, "test:node": {"command": "node development/test/node-imports.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "build:rollup"], "files": [], "output": []}, "test:node-dev": {"command": "node --conditions=development development/test/node-imports.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "build:rollup"], "files": [], "output": []}, "test:node-dom-shim": {"command": "node development/test/node-dom-shim.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "build:rollup"], "files": [], "output": []}, "test:node-dom-shim-dev": {"command": "node --conditions=development development/test/node-dom-shim.js", "dependencies": ["build:ts", "build:ts:std-decorators-tests", "build:rollup"], "files": [], "output": []}}, "files": ["/css-tag.{d.ts,d.ts.map,js,js.map}", "/decorators.{d.ts,d.ts.map,js,js.map}", "/polyfill-support.{d.ts,d.ts.map,js,js.map}", "/reactive-controller.{d.ts,d.ts.map,js,js.map}", "/reactive-element.{d.ts,d.ts.map,js,js.map}", "/decorators/", "/development/", "!/development/test/", "/node/"], "dependencies": {"@lit-labs/ssr-dom-shim": "^1.2.0"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.22.11", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.22.10", "@babel/plugin-transform-class-static-block": "^7.22.11", "@babel/plugin-transform-typescript": "^7.22.11", "@lit-internal/scripts": "^1.0.1", "@webcomponents/shadycss": "^1.8.0", "@webcomponents/template": "^1.4.4", "@webcomponents/webcomponentsjs": "^2.8.0", "esbuild": "^0.21.3"}, "typings": "reactive-element.d.ts", "directories": {"test": "test"}}