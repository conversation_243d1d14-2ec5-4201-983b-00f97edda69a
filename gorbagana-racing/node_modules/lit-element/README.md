# LitElement 3.0

A simple base class for creating fast, lightweight web components.

[![Build Status](https://github.com/lit/lit/workflows/Tests/badge.svg)](https://github.com/lit/lit/actions?query=workflow%3ATests)
[![Published on npm](https://img.shields.io/npm/v/lit-element.svg?logo=npm)](https://www.npmjs.com/package/lit-element)
[![Join our Discord](https://img.shields.io/badge/discord-join%20chat-5865F2.svg?logo=discord&logoColor=fff)](https://lit.dev/discord/)
[![Mentioned in Awesome Lit](https://awesome.re/mentioned-badge.svg)](https://github.com/web-padawan/awesome-lit)

LitElement is the base class that powers the [Lit](https://lit.dev) library for building fast web components. Most users should import `LitElement` from the [`lit`](https://www.npmjs.com/package/lit) package rather than installing and importing from the `lit-element` package directly.

## About this release

This is a pre-release of Lit 3.0, the next major version of Lit.

Lit 3.0 has very few breaking changes from Lit 2.0:

- Drops support for IE11
- Published as ES2021
- Removes a couple of deprecated Lit 1.x APIs

Lit 3.0 should require no changes to upgrade from Lit 2.0 for the vast majority of users. Once the full release is published, most apps and libraries will be able to extend their npm version ranges to include both 2.x and 3.x, like `"^2.7.0 || ^3.0.0"`.

Lit 2.x and 3.0 are _interoperable_: templates, base classes, directives, decorators, etc., from one version of Lit will work with those from another.

Please file any issues you find on our [issue tracker](https://github.com/lit/lit/issues).

## Documentation

Full documentation is available at [lit.dev](https://lit.dev).

## Contributing

Please see [CONTRIBUTING.md](../../CONTRIBUTING.md).
