{"version": 3, "file": "polyfill-support.js", "sourceRoot": "", "sources": ["../src/polyfill-support.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;AAEH;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,2CAA2C,CAAC;AACnD,OAAO,8BAA8B,CAAC;AAmBtC,sEAAsE;AACtE,WAAW;AACX,kCAAkC;AAClC,IAAI,QAAQ,GAAG,IAAI,CAAC;AAEpB,IAAM,eAAe,GAAG,UAAC,EAA+C;QAA9C,UAAU,gBAAA;IAClC,yEAAyE;IACzE,gEAAgE;IAChE,uDAAuD;IACvD,IACE,MAAM,CAAC,QAAQ,KAAK,SAAS;QAC7B,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC5D,CAAC;QACD,OAAO;IACT,CAAC;IAED,eAAe;IACf,0DAA0D;IAC1D,4CAA4C;IAC5C,KAAK;IAGH,UACD,CAAC,sBAAsB,GAAG,IAAI,CAAC;IAEhC;;OAEG;IACH,IAAM,eAAe,GAAG,UAAU,CAAC,SAAS,CAAC;IAC7C,IAAM,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;IAC1D,eAAe,CAAC,gBAAgB,GAAG;QACjC,0EAA0E;QAC1E,wEAAwE;QACxE,iBAAiB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1C,OAAO,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,IAAI,QAAQ,EAAE,CAAC;IACb,MAAA,UAAU,CAAC,gCAAgC,oCAA3C,UAAU,CAAC,gCAAgC,GAAK,eAAe,EAAC;AAClE,CAAC;KAAM,CAAC;IACN,MAAA,UAAU,CAAC,yBAAyB,oCAApC,UAAU,CAAC,yBAAyB,GAAK,eAAe,EAAC;AAC3D,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * LitElement patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill, but\n * support for Shadow DOM like css scoping is opt-in. This module uses ShadyCSS\n * to scope styles defined via the `static styles` property and styles included\n * in the render method. There are some limitations to be aware of:\n * * only styles that are included in the first render of a component are scoped.\n * * In addition, support for the deprecated `@apply` feature of ShadyCSS is\n * only provided for styles included in the template and not styles provided\n * via the static styles property.\n * * Lit parts cannot be used in styles included in the template.\n *\n * @packageDocumentation\n */\n\nimport '@lit/reactive-element/polyfill-support.js';\nimport 'lit-html/polyfill-support.js';\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\ninterface PatchableLitElementConstructor {\n  _$handlesPrepareStyles?: boolean;\n}\n\ninterface PatchableLitElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableLitElement;\n  constructor: PatchableLitElementConstructor;\n  createRenderRoot(): Element | ShadowRoot;\n  renderOptions: RenderOptions;\n}\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\nconst polyfillSupport = ({LitElement}: {LitElement: PatchableLitElement}) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making LitElement compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  (\n    LitElement as unknown as PatchableLitElementConstructor\n  )._$handlesPrepareStyles = true;\n\n  /**\n   * Patch to apply adoptedStyleSheets via ShadyCSS\n   */\n  const litElementProto = LitElement.prototype;\n  const createRenderRoot = litElementProto.createRenderRoot;\n  litElementProto.createRenderRoot = function (this: PatchableLitElement) {\n    // Pass the scope to render options so that it gets to lit-html for proper\n    // scoping via ShadyCSS. This is needed under Shady and also Shadow DOM,\n    // due to @apply.\n    this.renderOptions.scope = this.localName;\n    return createRenderRoot.call(this);\n  };\n};\n\nif (DEV_MODE) {\n  globalThis.litElementPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.litElementPolyfillSupport ??= polyfillSupport;\n}\n"]}