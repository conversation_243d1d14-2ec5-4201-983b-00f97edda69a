{"version": 3, "file": "polyfill-support.js", "sources": ["../reactive-element/src/polyfill-support.ts", "../lit-html/src/polyfill-support.ts", "src/polyfill-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * ReactiveElement patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill, but\n * support for Shadow DOM like css scoping is opt-in. This module uses ShadyCSS\n * to scope styles defined via the `static styles` property.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\nconst SCOPED = '__scoped';\n\ntype CSSResults = Array<{cssText: string} | CSSStyleSheet>;\n\ninterface PatchableReactiveElementConstructor {\n  [SCOPED]: boolean;\n  elementStyles: CSSResults;\n  shadowRootOptions: ShadowRootInit;\n  _$handlesPrepareStyles?: boolean;\n}\n\ninterface PatchableReactiveElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableReactiveElement;\n  constructor: PatchableReactiveElementConstructor;\n  connectedCallback(): void;\n  hasUpdated: boolean;\n  _$didUpdate(changedProperties: unknown): void;\n  createRenderRoot(): Element | ShadowRoot;\n  renderOptions: RenderOptions;\n}\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\nconst polyfillSupport = ({\n  ReactiveElement,\n}: {\n  ReactiveElement: PatchableReactiveElement;\n}) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making ReactiveElement compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const elementProto = ReactiveElement.prototype;\n\n  // In noPatch mode, patch the ReactiveElement prototype so that no\n  // ReactiveElements must be wrapped.\n  if (\n    window.ShadyDOM &&\n    window.ShadyDOM.inUse &&\n    window.ShadyDOM.noPatch === true\n  ) {\n    window.ShadyDOM.patchElementProto(elementProto);\n  }\n\n  /**\n   * Patch to apply adoptedStyleSheets via ShadyCSS\n   */\n  const createRenderRoot = elementProto.createRenderRoot;\n  elementProto.createRenderRoot = function (this: PatchableReactiveElement) {\n    // Pass the scope to render options so that it gets to lit-html for proper\n    // scoping via ShadyCSS.\n    const name = this.localName;\n    // If using native Shadow DOM must adoptStyles normally,\n    // otherwise do nothing.\n    if (window.ShadyCSS!.nativeShadow) {\n      return createRenderRoot.call(this);\n    } else {\n      if (!this.constructor.hasOwnProperty(SCOPED)) {\n        (this.constructor as PatchableReactiveElementConstructor)[SCOPED] =\n          true;\n        // Use ShadyCSS's `prepareAdoptedCssText` to shim adoptedStyleSheets.\n        const css = (\n          this.constructor as PatchableReactiveElementConstructor\n        ).elementStyles.map((v) =>\n          v instanceof CSSStyleSheet\n            ? Array.from(v.cssRules).reduce(\n                (a: string, r: CSSRule) => (a += r.cssText),\n                ''\n              )\n            : v.cssText\n        );\n        window.ShadyCSS?.ScopingShim?.prepareAdoptedCssText(css, name);\n        if (this.constructor._$handlesPrepareStyles === undefined) {\n          window.ShadyCSS!.prepareTemplateStyles(\n            document.createElement('template'),\n            name\n          );\n        }\n      }\n      return (\n        this.shadowRoot ??\n        this.attachShadow(\n          (this.constructor as PatchableReactiveElementConstructor)\n            .shadowRootOptions\n        )\n      );\n    }\n  };\n\n  /**\n   * Patch connectedCallback to apply ShadyCSS custom properties shimming.\n   */\n  const connectedCallback = elementProto.connectedCallback;\n  elementProto.connectedCallback = function (this: PatchableReactiveElement) {\n    connectedCallback.call(this);\n    // Note, must do first update separately so that we're ensured\n    // that rendering has completed before calling this.\n    if (this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n  };\n\n  /**\n   * Patch update to apply ShadyCSS custom properties shimming for first\n   * update.\n   */\n  const didUpdate = elementProto._$didUpdate;\n  elementProto._$didUpdate = function (\n    this: PatchableReactiveElement,\n    changedProperties: unknown\n  ) {\n    // Note, must do first update here so rendering has completed before\n    // calling this and styles are correct by updated/firstUpdated.\n    if (!this.hasUpdated) {\n      window.ShadyCSS!.styleElement(this);\n    }\n    didUpdate.call(this, changedProperties);\n  };\n};\n\nif (DEV_MODE) {\n  globalThis.reactiveElementPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.reactiveElementPolyfillSupport ??= polyfillSupport;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * lit-html patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill.\n * Scoping classes are added to DOM nodes to facilitate CSS scoping that\n * simulates the style scoping Shadow DOM provides. ShadyDOM does this scoping\n * to all elements added to the DOM. This module provides an important\n * optimization for this process by pre-scoping lit-html template\n * DOM. This means ShadyDOM does not have to scope each instance of the\n * template DOM. Instead, each template is scoped only once.\n *\n * Creating scoped CSS is not covered by this module. It is, however, integrated\n * into the lit-element and @lit/reactive-element packages. See the ShadyCSS docs\n * for how to apply scoping to CSS:\n * https://github.com/webcomponents/polyfills/tree/master/packages/shadycss#usage.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\ninterface ShadyTemplateResult {\n  strings: TemplateStringsArray;\n  // This property needs to remain unminified.\n  ['_$litType$']?: string;\n}\n\n// Note, this is a dummy type as the full type here is big.\ninterface Directive {\n  __directive?: Directive;\n}\n\ninterface DirectiveParent {\n  _$parent?: DirectiveParent;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\ninterface PatchableChildPartConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableChildPart;\n}\n\ninterface PatchableChildPart {\n  __directive?: Directive;\n  _$committedValue: unknown;\n  _$startNode: ChildNode;\n  _$endNode: ChildNode | null;\n  options: RenderOptions;\n  _$setValue(value: unknown, directiveParent: DirectiveParent): void;\n  _$getTemplate(result: ShadyTemplateResult): HTMLTemplateElement;\n}\n\ninterface PatchableTemplate {\n  el: HTMLTemplateElement;\n}\n\ninterface PatchableTemplateConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableTemplate;\n  createElement(html: string, options?: RenderOptions): HTMLTemplateElement;\n}\n\ninterface PatchableTemplateInstance {\n  _$template: PatchableTemplate;\n}\n\n// Scopes that have had styling prepared. Note, must only be done once per\n// scope.\nconst styledScopes = new Set<string>();\n// Map of css per scope. This is collected during first scope render, used when\n// styling is prepared, and then discarded.\nconst scopeCssStore = new Map<string, string[]>();\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\n/**\n * lit-html patches. These properties cannot be renamed.\n * * ChildPart.prototype._$getTemplate\n * * ChildPart.prototype._$setValue\n */\nconst polyfillSupport: NonNullable<typeof litHtmlPolyfillSupport> = (\n  Template: PatchableTemplateConstructor,\n  ChildPart: PatchableChildPartConstructor\n) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making lit-html compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const wrap =\n    ENABLE_SHADYDOM_NOPATCH &&\n    window.ShadyDOM?.inUse &&\n    window.ShadyDOM?.noPatch === true\n      ? window.ShadyDOM!.wrap\n      : (node: Node) => node;\n\n  const needsPrepareStyles = (name: string | undefined) =>\n    name !== undefined && !styledScopes.has(name);\n\n  const cssForScope = (name: string) => {\n    let scopeCss = scopeCssStore.get(name);\n    if (scopeCss === undefined) {\n      scopeCssStore.set(name, (scopeCss = []));\n    }\n    return scopeCss;\n  };\n\n  const prepareStyles = (name: string, template: HTMLTemplateElement) => {\n    // Get styles\n    const scopeCss = cssForScope(name);\n    const hasScopeCss = scopeCss.length !== 0;\n    if (hasScopeCss) {\n      const style = document.createElement('style');\n      style.textContent = scopeCss.join('\\n');\n      // Note, it's important to add the style to the *end* of the template so\n      // it doesn't mess up part indices.\n      template.content.appendChild(style);\n    }\n    // Mark this scope as styled.\n    styledScopes.add(name);\n    // Remove stored data since it's no longer needed.\n    scopeCssStore.delete(name);\n    // ShadyCSS removes scopes and removes the style under ShadyDOM and leaves\n    // it under native Shadow DOM\n    window.ShadyCSS!.prepareTemplateStyles(template, name);\n    // Note, under native Shadow DOM, the style is added to the beginning of the\n    // template. It must be moved to the *end* of the template so it doesn't\n    // mess up part indices.\n    if (hasScopeCss && window.ShadyCSS!.nativeShadow) {\n      // If there were styles but the CSS text was empty, ShadyCSS will\n      // eliminate the style altogether, so the style here could be null\n      const style = template.content.querySelector('style');\n      if (style !== null) {\n        template.content.appendChild(style);\n      }\n    }\n  };\n\n  const scopedTemplateCache = new Map<\n    string | undefined,\n    Map<TemplateStringsArray, PatchableTemplate>\n  >();\n\n  /**\n   * Override to extract style elements from the template\n   * and store all style.textContent in the shady scope data.\n   * Note, it's ok to patch Template since it's only used via ChildPart.\n   */\n  const originalCreateElement = Template.createElement;\n  Template.createElement = function (html: string, options?: RenderOptions) {\n    const element = originalCreateElement.call(Template, html, options);\n    const scope = options?.scope;\n    if (scope !== undefined) {\n      if (!window.ShadyCSS!.nativeShadow) {\n        window.ShadyCSS!.prepareTemplateDom(element, scope);\n      }\n      // Process styles only if this scope is being prepared. Otherwise,\n      // leave styles as is for back compat with Lit1.\n      if (needsPrepareStyles(scope)) {\n        const scopeCss = cssForScope(scope);\n        // Remove styles and store textContent.\n        const styles = element.content.querySelectorAll(\n          'style'\n        ) as NodeListOf<HTMLStyleElement>;\n        // Store the css in this template in the scope css and remove the <style>\n        // from the template _before_ the node-walk captures part indices\n        scopeCss.push(\n          ...Array.from(styles).map((style) => {\n            style.parentNode?.removeChild(style);\n            return style.textContent!;\n          })\n        );\n      }\n    }\n    return element;\n  };\n\n  const renderContainer = document.createDocumentFragment();\n  const renderContainerMarker = document.createComment('');\n\n  const childPartProto = ChildPart.prototype;\n  /**\n   * Patch to apply gathered css via ShadyCSS. This is done only once per scope.\n   */\n  const setValue = childPartProto._$setValue;\n  childPartProto._$setValue = function (\n    this: PatchableChildPart,\n    value: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    const container = wrap(this._$startNode).parentNode!;\n    const scope = this.options?.scope;\n    if (container instanceof ShadowRoot && needsPrepareStyles(scope)) {\n      // Note, @apply requires outer => inner scope rendering on initial\n      // scope renders to apply property values correctly. Style preparation\n      // is tied to rendering into `shadowRoot`'s and this is typically done by\n      // custom elements. If this is done in `connectedCallback`, as is typical,\n      // the code below ensures the right order since content is rendered\n      // into a fragment first so the hosting element can prepare styles first.\n      // If rendering is done in the constructor, this won't work, but that's\n      // not supported in ShadyDOM anyway.\n      const startNode = this._$startNode;\n      const endNode = this._$endNode;\n\n      // Temporarily move this part into the renderContainer.\n      renderContainer.appendChild(renderContainerMarker);\n      this._$startNode = renderContainerMarker;\n      this._$endNode = null;\n\n      // Note, any nested template results render here and their styles will\n      // be extracted and collected.\n      setValue.call(this, value, directiveParent);\n\n      // Get the template for this result or create a dummy one if a result\n      // is not being rendered.\n      // This property needs to remain unminified.\n      const template = (value as ShadyTemplateResult)?.['_$litType$']\n        ? (this._$committedValue as PatchableTemplateInstance)._$template.el\n        : document.createElement('template');\n      prepareStyles(scope!, template);\n\n      // Note, this is the temporary startNode.\n      renderContainer.removeChild(renderContainerMarker);\n      // When using native Shadow DOM, include prepared style in shadowRoot.\n      if (window.ShadyCSS?.nativeShadow) {\n        const style = template.content.querySelector('style');\n        if (style !== null) {\n          renderContainer.appendChild(style.cloneNode(true));\n        }\n      }\n      container.insertBefore(renderContainer, endNode);\n      // Move part back to original container.\n      this._$startNode = startNode;\n      this._$endNode = endNode;\n    } else {\n      setValue.call(this, value, directiveParent);\n    }\n  };\n\n  /**\n   * Patch ChildPart._$getTemplate to look up templates in a cache bucketed\n   * by element name.\n   */\n  childPartProto._$getTemplate = function (\n    this: PatchableChildPart,\n    result: ShadyTemplateResult\n  ) {\n    const scope = this.options?.scope;\n    let templateCache = scopedTemplateCache.get(scope);\n    if (templateCache === undefined) {\n      scopedTemplateCache.set(scope, (templateCache = new Map()));\n    }\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(\n        result.strings,\n        (template = new Template(result, this.options))\n      );\n    }\n    return template;\n  };\n};\n\nif (ENABLE_SHADYDOM_NOPATCH) {\n  polyfillSupport.noPatchSupported = ENABLE_SHADYDOM_NOPATCH;\n}\n\nif (DEV_MODE) {\n  globalThis.litHtmlPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.litHtmlPolyfillSupport ??= polyfillSupport;\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * LitElement patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill, but\n * support for Shadow DOM like css scoping is opt-in. This module uses ShadyCSS\n * to scope styles defined via the `static styles` property and styles included\n * in the render method. There are some limitations to be aware of:\n * * only styles that are included in the first render of a component are scoped.\n * * In addition, support for the deprecated `@apply` feature of ShadyCSS is\n * only provided for styles included in the template and not styles provided\n * via the static styles property.\n * * Lit parts cannot be used in styles included in the template.\n *\n * @packageDocumentation\n */\n\nimport '@lit/reactive-element/polyfill-support.js';\nimport 'lit-html/polyfill-support.js';\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\ninterface PatchableLitElementConstructor {\n  _$handlesPrepareStyles?: boolean;\n}\n\ninterface PatchableLitElement extends HTMLElement {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableLitElement;\n  constructor: PatchableLitElementConstructor;\n  createRenderRoot(): Element | ShadowRoot;\n  renderOptions: RenderOptions;\n}\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\nconst polyfillSupport = ({LitElement}: {LitElement: PatchableLitElement}) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making LitElement compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  (\n    LitElement as unknown as PatchableLitElementConstructor\n  )._$handlesPrepareStyles = true;\n\n  /**\n   * Patch to apply adoptedStyleSheets via ShadyCSS\n   */\n  const litElementProto = LitElement.prototype;\n  const createRenderRoot = litElementProto.createRenderRoot;\n  litElementProto.createRenderRoot = function (this: PatchableLitElement) {\n    // Pass the scope to render options so that it gets to lit-html for proper\n    // scoping via ShadyCSS. This is needed under Shady and also Shadow DOM,\n    // due to @apply.\n    this.renderOptions.scope = this.localName;\n    return createRenderRoot.call(this);\n  };\n};\n\nif (DEV_MODE) {\n  globalThis.litElementPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.litElementPolyfillSupport ??= polyfillSupport;\n}\n"], "names": ["SCOPED", "_b", "globalThis", "reactiveElementPolyfillSupport", "_a", "ReactiveElement", "undefined", "window", "ShadyCSS", "nativeShadow", "App<PERSON><PERSON><PERSON>", "elementProto", "prototype", "ShadyDOM", "inUse", "noPatch", "patchElementProto", "createRenderRoot", "name", "this", "localName", "call", "constructor", "hasOwnProperty", "css", "elementStyles", "map", "v", "CSSStyleSheet", "Array", "from", "cssRules", "reduce", "a", "r", "cssText", "<PERSON><PERSON><PERSON><PERSON>", "prepareAdoptedCssText", "_$handlesPrepareStyles", "prepareTemplateStyles", "document", "createElement", "_c", "shadowRoot", "attachShadow", "shadowRootOptions", "connectedCallback", "hasUpdated", "styleElement", "didUpdate", "_$didUpdate", "changedProperties", "styledScopes", "Set", "scopeCssStore", "Map", "litHtmlPolyfillSupport", "Template", "<PERSON><PERSON><PERSON>", "needsPrepareStyles", "has", "cssForScope", "scopeCss", "get", "set", "scopedTemplateCache", "originalCreateElement", "html", "options", "element", "scope", "prepareTemplateDom", "styles", "content", "querySelectorAll", "push", "apply", "style", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "renderContainer", "createDocumentFragment", "renderContainerMarker", "createComment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setValue", "_$setValue", "value", "directiveParent", "container", "_$startNode", "ShadowRoot", "startNode", "endNode", "_$endNode", "append<PERSON><PERSON><PERSON>", "template", "_$committedValue", "_$template", "el", "hasScopeCss", "length", "join", "add", "delete", "querySelector", "prepareStyles", "cloneNode", "insertBefore", "_$getTemplate", "result", "templateCache", "strings", "litElementPolyfillSupport", "LitElement", "litElementProto", "renderOptions"], "mappings": ";;;;;eAyBMA,EAAS,WA0I4B,QAAzCC,EAAAC,WAAWC,sCAA8B,IAAAF,IAAzCC,WAAWC,+BA/GW,SAACC,GACvB,IAAAC,EAAeD,EAAAC,gBAOf,QACsBC,IAApBC,OAAOC,YACND,OAAOC,SAASC,cAAiBF,OAAOC,SAASE,WAFpD,CAYA,IAAMC,EAAeN,EAAgBO,UAKnCL,OAAOM,UACPN,OAAOM,SAASC,QACY,IAA5BP,OAAOM,SAASE,SAEhBR,OAAOM,SAASG,kBAAkBL,GAMpC,IAAMM,EAAmBN,EAAaM,iBACtCN,EAAaM,iBAAmB,qBAGxBC,EAAOC,KAAKC,UAGlB,GAAIb,OAAOC,SAAUC,aACnB,OAAOQ,EAAiBI,KAAKF,MAE7B,IAAKA,KAAKG,YAAYC,eAAevB,GAAS,CAC3CmB,KAAKG,YAAoDtB,IACxD,EAEF,IAAMwB,EACJL,KAAKG,YACLG,cAAcC,KAAI,SAACC,GACnB,OAAAA,aAAaC,cACTC,MAAMC,KAAKH,EAAEI,UAAUC,QACrB,SAACC,EAAWC,GAAe,OAACD,EAAKC,EAAEC,OAAQ,GAC3C,IAEFR,EAAEQ,OALN,IAO4B,QAA9BlC,EAAiB,QAAjBG,EAAAG,OAAOC,gBAAU,IAAAJ,OAAA,EAAAA,EAAAgC,mBAAa,IAAAnC,GAAAA,EAAAoC,sBAAsBb,EAAKN,QACTZ,IAA5Ca,KAAKG,YAAYgB,MACnB/B,OAAOC,SAAU+B,sBACfC,SAASC,cAAc,YACvBvB,EAGL,CACD,OACiB,QAAfwB,EAAAvB,KAAKwB,kBAAU,IAAAD,EAAAA,EACfvB,KAAKyB,aACFzB,KAAKG,YACHuB,kBAIX,EAKA,IAAMC,EAAoBnC,EAAamC,kBACvCnC,EAAamC,kBAAoB,WAC/BA,EAAkBzB,KAAKF,MAGnBA,KAAK4B,YACPxC,OAAOC,SAAUwC,aAAa7B,KAElC,EAMA,IAAM8B,EAAYtC,EAAauC,KAC/BvC,EAAauC,KAAc,SAEzBC,GAIKhC,KAAK4B,YACRxC,OAAOC,SAAUwC,aAAa7B,MAEhC8B,EAAU5B,KAAKF,KAAMgC,EACvB,CA5FC,CA6FH,GC5EA,MAAMC,EAAe,IAAIC,IAGnBC,EAAgB,IAAIC,IAsNS,QAAjCtD,EAAAC,WAAWsD,8BAAsB,IAAAvD,IAAjCC,WAAWsD,uBAxMuD,SAClEC,EACAC,GAKA,QACsBpD,IAApBC,OAAOC,YACND,OAAOC,SAASC,cAAiBF,OAAOC,SAASE,WAFpD,CAYA,IAOMiD,EAAqB,SAACzC,GAC1B,YAASZ,IAATY,IAAuBkC,EAAaQ,IAAI1C,EAAxC,EAEI2C,EAAc,SAAC3C,GACnB,IAAI4C,EAAWR,EAAcS,IAAI7C,GAIjC,YAHiBZ,IAAbwD,GACFR,EAAcU,IAAI9C,EAAO4C,EAAW,IAE/BA,CACT,EAiCMG,EAAsB,IAAIV,IAU1BW,EAAwBT,EAAShB,cACvCgB,EAAShB,cAAgB,SAAU0B,EAAcC,GAC/C,IAAMC,EAAUH,EAAsB7C,KAAKoC,EAAUU,EAAMC,GACrDE,EAAQF,aAAA,EAAAA,EAASE,MACvB,QAAchE,IAAVgE,IACG/D,OAAOC,SAAUC,cACpBF,OAAOC,SAAU+D,mBAAmBF,EAASC,GAI3CX,EAAmBW,IAAQ,CAC7B,IAAMR,EAAWD,EAAYS,GAEvBE,EAASH,EAAQI,QAAQC,iBAC7B,SAIFZ,EAASa,KAATC,MAAAd,EACKjC,MAAMC,KAAK0C,GAAQ9C,KAAI,SAACmD,SAEzB,OADgB,QAAhBzE,EAAAyE,EAAMC,kBAAU,IAAA1E,GAAAA,EAAE2E,YAAYF,GACvBA,EAAMG,WACd,IAEJ,CAEH,OAAOX,CACT,EAEA,IAAMY,EAAkBzC,SAAS0C,yBAC3BC,EAAwB3C,SAAS4C,cAAc,IAE/CC,EAAiB3B,EAAU9C,UAI3B0E,EAAWD,EAAeE,KAChCF,EAAeE,KAAa,SAE1BC,EACAC,gBAAA,IAAAA,IAAAA,EAAuCtE,MAEvC,IAAMuE,EAAiBvE,KAAKwE,KAAab,WACnCR,EAAoB,QAAZlE,EAAAe,KAAKiD,eAAO,IAAAhE,OAAA,EAAAA,EAAEkE,MAC5B,GAAIoB,aAAqBE,YAAcjC,EAAmBW,GAAQ,CAShE,IAAMuB,EAAY1E,KAAKwE,KACjBG,EAAU3E,KAAK4E,KAGrBd,EAAgBe,YAAYb,GAC5BhE,KAAKwE,KAAcR,EACnBhE,KAAK4E,KAAY,KAIjBT,EAASjE,KAAKF,KAAMqE,EAAOC,GAK3B,IAAMQ,GAAYT,aAAA,EAAAA,EAA4C,YACzDrE,KAAK+E,KAA+CC,KAAWC,GAChE5D,SAASC,cAAc,YAM3B,GArHkB,SAACvB,EAAc+E,GAEnC,IAsBQpB,EAtBFf,EAAWD,EAAY3C,GACvBmF,EAAkC,IAApBvC,EAASwC,OACzBD,KACIxB,EAAQrC,SAASC,cAAc,UAC/BuC,YAAclB,EAASyC,KAAK,MAGlCN,EAASxB,QAAQuB,YAAYnB,IAG/BzB,EAAaoD,IAAItF,GAEjBoC,EAAcmD,OAAOvF,GAGrBX,OAAOC,SAAU+B,sBAAsB0D,EAAU/E,GAI7CmF,GAAe9F,OAAOC,SAAUC,cAIpB,QADRoE,EAAQoB,EAASxB,QAAQiC,cAAc,WAE3CT,EAASxB,QAAQuB,YAAYnB,EAGnC,CAmFI8B,CAAcrC,EAAQ2B,GAGtBhB,EAAgBF,YAAYI,GAEP,UAAjB5E,OAAOC,gBAAU,IAAAP,OAAA,EAAAA,EAAAQ,aAAc,CACjC,IAAMoE,EAAQoB,EAASxB,QAAQiC,cAAc,SAC/B,OAAV7B,GACFI,EAAgBe,YAAYnB,EAAM+B,WAAU,GAE/C,CACDlB,EAAUmB,aAAa5B,EAAiBa,GAExC3E,KAAKwE,KAAcE,EACnB1E,KAAK4E,KAAYD,CAClB,MACCR,EAASjE,KAAKF,KAAMqE,EAAOC,EAE/B,EAMAJ,EAAeyB,KAAgB,SAE7BC,SAEMzC,EAAoB,QAAZlE,EAAAe,KAAKiD,eAAO,IAAAhE,OAAA,EAAAA,EAAEkE,MACxB0C,EAAgB/C,EAAoBF,IAAIO,QACtBhE,IAAlB0G,GACF/C,EAAoBD,IAAIM,EAAQ0C,EAAgB,IAAIzD,KAEtD,IAAI0C,EAAWe,EAAcjD,IAAIgD,EAAOE,SAOxC,YANiB3G,IAAb2F,GACFe,EAAchD,IACZ+C,EAAOE,QACNhB,EAAW,IAAIxC,EAASsD,EAAQ5F,KAAKiD,UAGnC6B,CACT,CAlLC,CAmLH,GC5MsC,QAApChG,EAAAC,WAAWgH,iCAAyB,IAAAjH,IAApCC,WAAWgH,0BArCW,SAAC9G,GAAC,IAAA+G,EAAU/G,EAAA+G,WAIlC,QACsB7G,IAApBC,OAAOC,YACND,OAAOC,SAASC,cAAiBF,OAAOC,SAASE,WAFpD,CAaEyG,EACA7E,MAAyB,EAK3B,IAAM8E,EAAkBD,EAAWvG,UAC7BK,EAAmBmG,EAAgBnG,iBACzCmG,EAAgBnG,iBAAmB,WAKjC,OADAE,KAAKkG,cAAc/C,MAAQnD,KAAKC,UACzBH,EAAiBI,KAAKF,KAC/B,CAtBC,CAuBH"}