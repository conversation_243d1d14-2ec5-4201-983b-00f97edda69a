{"version": 3, "file": "async-replace.js", "sourceRoot": "", "sources": ["../../src/directives/async-replace.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAY,QAAQ,EAAC,MAAM,gBAAgB,CAAC;AACnD,OAAO,EACL,cAAc,EACd,SAAS,GAEV,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAC,MAAM,EAAE,aAAa,EAAE,UAAU,EAAC,MAAM,4BAA4B,CAAC;AAI7E,MAAM,OAAO,qBAAsB,SAAQ,cAAc;IAAzD;;QAEU,eAAU,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;QACrC,aAAQ,GAAG,IAAI,MAAM,EAAE,CAAC;IA4ElC,CAAC;IA1EC,yEAAyE;IACzE,6DAA6D;IAC7D,MAAM,CAAI,KAAuB,EAAE,OAAmB;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEQ,MAAM,CACb,KAAgB,EAChB,CAAC,KAAK,EAAE,MAAM,CAA4B;QAE1C,0EAA0E;QAC1E,6CAA6C;QAC7C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QACD,kEAAkE;QAClE,kBAAkB;QAClB,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,MAAM,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAC,GAAG,IAAI,CAAC;QACtD,sEAAsE;QACtE,wEAAwE;QACxE,0EAA0E;QAC1E,4BAA4B;QAC5B,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,CAAU,EAAE,EAAE;YACrC,iEAAiE;YACjE,gEAAgE;YAChE,OAAO,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC;gBACpB,MAAM,MAAM,CAAC,GAAG,EAAE,CAAC;YACrB,CAAC;YACD,sEAAsE;YACtE,qEAAqE;YACrE,2BAA2B;YAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACxB,kEAAkE;gBAClE,+DAA+D;gBAC/D,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;oBAC5B,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,yDAAyD;gBACzD,kEAAkE;gBAClE,iEAAiE;gBACjE,sCAAsC;gBACtC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,CAAC,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACnB,CAAC;gBAED,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxB,CAAC,EAAE,CAAC;YACN,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,+DAA+D;IACrD,WAAW,CAAC,KAAc,EAAE,MAAc;QAClD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEQ,YAAY;QACnB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEQ,WAAW;QAClB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;CACF;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,SAAS,CAAC,qBAAqB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ChildPart, noChange} from '../lit-html.js';\nimport {\n  AsyncDirective,\n  directive,\n  DirectiveParameters,\n} from '../async-directive.js';\nimport {Pa<PERSON>, PseudoWeakRef, forAwaitOf} from './private-async-helpers.js';\n\ntype Mapper<T> = (v: T, index?: number) => unknown;\n\nexport class AsyncReplaceDirective extends AsyncDirective {\n  private __value?: AsyncIterable<unknown>;\n  private __weakThis = new PseudoWeakRef(this);\n  private __pauser = new Pauser();\n\n  // @ts-expect-error value not used, but we want a nice parameter for docs\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  render<T>(value: AsyncIterable<T>, _mapper?: Mapper<T>) {\n    return noChange;\n  }\n\n  override update(\n    _part: ChildPart,\n    [value, mapper]: DirectiveParameters<this>\n  ) {\n    // If our initial render occurs while disconnected, ensure that the pauser\n    // and weakThis are in the disconnected state\n    if (!this.isConnected) {\n      this.disconnected();\n    }\n    // If we've already set up this particular iterable, we don't need\n    // to do anything.\n    if (value === this.__value) {\n      return noChange;\n    }\n    this.__value = value;\n    let i = 0;\n    const {__weakThis: weakThis, __pauser: pauser} = this;\n    // Note, the callback avoids closing over `this` so that the directive\n    // can be gc'ed before the promise resolves; instead `this` is retrieved\n    // from `weakThis`, which can break the hard reference in the closure when\n    // the directive disconnects\n    forAwaitOf(value, async (v: unknown) => {\n      // The while loop here handles the case that the connection state\n      // thrashes, causing the pauser to resume and then get re-paused\n      while (pauser.get()) {\n        await pauser.get();\n      }\n      // If the callback gets here and there is no `this`, it means that the\n      // directive has been disconnected and garbage collected and we don't\n      // need to do anything else\n      const _this = weakThis.deref();\n      if (_this !== undefined) {\n        // Check to make sure that value is the still the current value of\n        // the part, and if not bail because a new value owns this part\n        if (_this.__value !== value) {\n          return false;\n        }\n\n        // As a convenience, because functional-programming-style\n        // transforms of iterables and async iterables requires a library,\n        // we accept a mapper function. This is especially convenient for\n        // rendering a template for each item.\n        if (mapper !== undefined) {\n          v = mapper(v, i);\n        }\n\n        _this.commitValue(v, i);\n        i++;\n      }\n      return true;\n    });\n    return noChange;\n  }\n\n  // Override point for AsyncAppend to append rather than replace\n  protected commitValue(value: unknown, _index: number) {\n    this.setValue(value);\n  }\n\n  override disconnected() {\n    this.__weakThis.disconnect();\n    this.__pauser.pause();\n  }\n\n  override reconnected() {\n    this.__weakThis.reconnect(this);\n    this.__pauser.resume();\n  }\n}\n\n/**\n * A directive that renders the items of an async iterable[1], replacing\n * previous values with new values, so that only one value is ever rendered\n * at a time. This directive may be used in any expression type.\n *\n * Async iterables are objects with a `[Symbol.asyncIterator]` method, which\n * returns an iterator who's `next()` method returns a Promise. When a new\n * value is available, the Promise resolves and the value is rendered to the\n * Part controlled by the directive. If another value other than this\n * directive has been set on the Part, the iterable will no longer be listened\n * to and new values won't be written to the Part.\n *\n * [1]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/for-await...of\n *\n * @param value An async iterable\n * @param mapper An optional function that maps from (value, index) to another\n *     value. Useful for generating templates for each item in the iterable.\n */\nexport const asyncReplace = directive(AsyncReplaceDirective);\n"]}