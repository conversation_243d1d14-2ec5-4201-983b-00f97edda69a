{"version": 3, "file": "if-defined.js", "sourceRoot": "", "sources": ["../../src/directives/if-defined.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,gBAAgB,CAAC;AAEvC;;;;;GAKG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAI,KAAQ,EAAE,EAAE,CAAC,KAAK,IAAI,OAAO,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\n\n/**\n * For AttributeParts, sets the attribute if the value is defined and removes\n * the attribute if the value is undefined.\n *\n * For other part types, this directive is a no-op.\n */\nexport const ifDefined = <T>(value: T) => value ?? nothing;\n"]}