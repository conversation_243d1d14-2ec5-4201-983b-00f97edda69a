{"version": 3, "file": "keyed.js", "sourceRoot": "", "sources": ["../../src/directives/keyed.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,OAAO,EAAC,MAAM,gBAAgB,CAAC;AACvC,OAAO,EACL,SAAS,EACT,SAAS,GAGV,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAE1D,MAAM,KAAM,SAAQ,SAAS;IAA7B;;QACE,QAAG,GAAY,OAAO,CAAC;IAiBzB,CAAC;IAfC,MAAM,CAAC,CAAU,EAAE,CAAU;QAC3B,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,OAAO,CAAC,CAAC;IACX,CAAC;IAEQ,MAAM,CAAC,IAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAA4B;QAChE,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YACnB,+DAA+D;YAC/D,gEAAgE;YAChE,0BAA0B;YAC1B,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACf,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;CACF;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  ChildPart,\n  DirectiveParameters,\n} from '../directive.js';\nimport {setCommittedValue} from '../directive-helpers.js';\n\nclass Keyed extends Directive {\n  key: unknown = nothing;\n\n  render(k: unknown, v: unknown) {\n    this.key = k;\n    return v;\n  }\n\n  override update(part: ChildPart, [k, v]: DirectiveParameters<this>) {\n    if (k !== this.key) {\n      // Clear the part before returning a value. The one-arg form of\n      // setCommittedValue sets the value to a sentinel which forces a\n      // commit the next render.\n      setCommittedValue(part);\n      this.key = k;\n    }\n    return v;\n  }\n}\n\n/**\n * Associates a renderable value with a unique key. When the key changes, the\n * previous DOM is removed and disposed before rendering the next value, even\n * if the value - such as a template - is the same.\n *\n * This is useful for forcing re-renders of stateful components, or working\n * with code that expects new data to generate new HTML elements, such as some\n * animation techniques.\n */\nexport const keyed = directive(Keyed);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {Keyed};\n"]}