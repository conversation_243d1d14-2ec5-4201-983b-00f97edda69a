{"version": 3, "file": "map.js", "sourceRoot": "", "sources": ["../../src/directives/map.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;;;;;;;;;;;;;;GAeG;AACH,MAAM,SAAS,CAAC,CAAC,GAAG,CAClB,KAA8B,EAC9B,CAAuC;IAEvC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;YAC1B,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable containing the result of calling `f(value)` on each\n * value in `items`.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     <ul>\n *       ${map(items, (i) => html`<li>${i}</li>`)}\n *     </ul>\n *   `;\n * }\n * ```\n */\nexport function* map<T>(\n  items: Iterable<T> | undefined,\n  f: (value: T, index: number) => unknown\n) {\n  if (items !== undefined) {\n    let i = 0;\n    for (const value of items) {\n      yield f(value, i++);\n    }\n  }\n}\n"]}