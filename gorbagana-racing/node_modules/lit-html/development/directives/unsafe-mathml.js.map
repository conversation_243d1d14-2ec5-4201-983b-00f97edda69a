{"version": 3, "file": "unsafe-mathml.js", "sourceRoot": "", "sources": ["../../src/directives/unsafe-mathml.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAC,SAAS,EAAC,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAC,mBAAmB,EAAC,MAAM,kBAAkB,CAAC;AAErD,MAAM,aAAa,GAAG,CAAC,CAAC;AAExB,MAAM,qBAAsB,SAAQ,mBAAmB;;AACrC,mCAAa,GAAG,YAAY,CAAC;AAC7B,gCAAU,GAAG,aAAa,CAAC;AAG7C;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,SAAS,CAAC,qBAAqB,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {directive} from '../directive.js';\nimport {UnsafeHTMLDirective} from './unsafe-html.js';\n\nconst MATHML_RESULT = 3;\n\nclass UnsafeMathMLDirective extends UnsafeHTMLDirective {\n  static override directiveName = 'unsafeMath';\n  static override resultType = MATHML_RESULT;\n}\n\n/**\n * Renders the result as MathML, rather than text.\n *\n * The values `undefined`, `null`, and `nothing`, will all result in no content\n * (empty string) being rendered.\n *\n * Note, this is unsafe to use with any user-provided input that hasn't been\n * sanitized or escaped, as it may lead to cross-site-scripting\n * vulnerabilities.\n */\nexport const unsafeMathML = directive(UnsafeMathMLDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {UnsafeMathMLDirective as UnsafeMathDirective};\n"]}