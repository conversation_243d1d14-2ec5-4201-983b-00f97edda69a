{"version": 3, "file": "when.js", "sourceRoot": "", "sources": ["../../src/directives/when.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAoCH,MAAM,UAAU,IAAI,CAClB,SAAkB,EAClB,QAAiC,EACjC,SAAmC;IAEnC,OAAO,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;AAClE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\ntype Falsy = null | undefined | false | 0 | -0 | 0n | '';\n\n/**\n * When `condition` is true, returns the result of calling `trueCase()`, else\n * returns the result of calling `falseCase()` if `falseCase` is defined.\n *\n * This is a convenience wrapper around a ternary expression that makes it a\n * little nicer to write an inline conditional without an else.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${when(this.user, () => html`User: ${this.user.username}`, () => html`Sign In...`)}\n *   `;\n * }\n * ```\n */\nexport function when<C extends Falsy, T, F = undefined>(\n  condition: C,\n  trueCase: (c: C) => T,\n  falseCase?: (c: C) => F\n): F;\nexport function when<C, T, F>(\n  condition: C extends Falsy ? never : C,\n  trueCase: (c: C) => T,\n  falseCase?: (c: C) => F\n): T;\nexport function when<C, T, F = undefined>(\n  condition: C,\n  trueCase: (c: Exclude<C, Falsy>) => T,\n  falseCase?: (c: Extract<C, Falsy>) => F\n): C extends Falsy ? F : T;\nexport function when(\n  condition: unknown,\n  trueCase: (c: unknown) => unknown,\n  falseCase?: (c: unknown) => unknown\n): unknown {\n  return condition ? trueCase(condition) : falseCase?.(condition);\n}\n"]}