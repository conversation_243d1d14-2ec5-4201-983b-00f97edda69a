{"version": 3, "file": "async-append.js", "sources": ["../src/directives/async-append.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {ChildPart} from '../lit-html.js';\nimport {\n  directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\nimport {AsyncReplaceDirective} from './async-replace.js';\nimport {\n  clearPart,\n  insertPart,\n  setChildPartValue,\n} from '../directive-helpers.js';\n\nclass AsyncAppendDirective extends AsyncReplaceDirective {\n  private __childPart!: ChildPart;\n\n  // Override AsyncReplace to narrow the allowed part type to ChildPart only\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error('asyncAppend can only be used in child expressions');\n    }\n  }\n\n  // Override AsyncReplace to save the part since we need to append into it\n  override update(part: ChildPart, params: DirectiveParameters<this>) {\n    this.__childPart = part;\n    return super.update(part, params);\n  }\n\n  // Override AsyncReplace to append rather than replace\n  protected override commitValue(value: unknown, index: number) {\n    // When we get the first value, clear the part. This lets the\n    // previous value display until we can replace it.\n    if (index === 0) {\n      clearPart(this.__childPart);\n    }\n    // Create and insert a new part and set its value to the next value\n    const newPart = insertPart(this.__childPart);\n    setChildPartValue(newPart, value);\n  }\n}\n\n/**\n * A directive that renders the items of an async iterable[1], appending new\n * values after previous values, similar to the built-in support for iterables.\n * This directive is usable only in child expressions.\n *\n * Async iterables are objects with a [Symbol.asyncIterator] method, which\n * returns an iterator who's `next()` method returns a Promise. When a new\n * value is available, the Promise resolves and the value is appended to the\n * Part controlled by the directive. If another value other than this\n * directive has been set on the Part, the iterable will no longer be listened\n * to and new values won't be written to the Part.\n *\n * [1]: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/for-await...of\n *\n * @param value An async iterable\n * @param mapper An optional function that maps from (value, index) to another\n *     value. Useful for generating templates for each item in the iterable.\n */\nexport const asyncAppend = directive(AsyncAppendDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {AsyncAppendDirective};\n"], "names": ["asyncAppend", "directive", "AsyncReplaceDirective", "constructor", "partInfo", "super", "type", "PartType", "CHILD", "Error", "update", "part", "params", "this", "__child<PERSON><PERSON>", "commitValue", "value", "index", "clearPart", "newPart", "insertPart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;SAoEaA,EAAcC,EAhD3B,cAAmCC,EAIjC,WAAAC,CAAYC,GAEV,GADAC,MAAMD,GACFA,EAASE,OAASC,EAASC,MAC7B,MAAUC,MAAM,oDAEnB,CAGQ,MAAAC,CAAOC,EAAiBC,GAE/B,OADAC,KAAKC,MAAcH,EACZN,MAAMK,OAAOC,EAAMC,EAC3B,CAGkB,WAAAG,CAAYC,EAAgBC,GAG/B,IAAVA,GACFC,EAAUL,KAAKC,OAGjB,MAAMK,EAAUC,EAAWP,KAAKC,OAChCO,EAAkBF,EAASH,EAC5B"}