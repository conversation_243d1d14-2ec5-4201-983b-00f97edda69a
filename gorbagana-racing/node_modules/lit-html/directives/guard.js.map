{"version": 3, "file": "guard.js", "sources": ["../src/directives/guard.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {noChange, Part} from '../lit-html.js';\nimport {directive, Directive, DirectiveParameters} from '../directive.js';\n\n// A sentinel that indicates guard() hasn't rendered anything yet\nconst initialValue = {};\n\nclass GuardDirective extends Directive {\n  private _previousValue: unknown = initialValue;\n\n  render(_value: unknown, f: () => unknown) {\n    return f();\n  }\n\n  override update(_part: Part, [value, f]: DirectiveParameters<this>) {\n    if (Array.isArray(value)) {\n      // Dirty-check arrays by item\n      if (\n        Array.isArray(this._previousValue) &&\n        this._previousValue.length === value.length &&\n        value.every((v, i) => v === (this._previousValue as Array<unknown>)[i])\n      ) {\n        return noChange;\n      }\n    } else if (this._previousValue === value) {\n      // Dirty-check non-arrays by identity\n      return noChange;\n    }\n\n    // Copy the value if it's an array so that if it's mutated we don't forget\n    // what the previous values were.\n    this._previousValue = Array.isArray(value) ? Array.from(value) : value;\n    const r = this.render(value, f);\n    return r;\n  }\n}\n\n/**\n * Prevents re-render of a template function until a single value or an array of\n * values changes.\n *\n * Values are checked against previous values with strict equality (`===`), and\n * so the check won't detect nested property changes inside objects or arrays.\n * Arrays values have each item checked against the previous value at the same\n * index with strict equality. Nested arrays are also checked only by strict\n * equality.\n *\n * Example:\n *\n * ```js\n * html`\n *   <div>\n *     ${guard([user.id, company.id], () => html`...`)}\n *   </div>\n * `\n * ```\n *\n * In this case, the template only rerenders if either `user.id` or `company.id`\n * changes.\n *\n * guard() is useful with immutable data patterns, by preventing expensive work\n * until data updates.\n *\n * Example:\n *\n * ```js\n * html`\n *   <div>\n *     ${guard([immutableItems], () => immutableItems.map(i => html`${i}`))}\n *   </div>\n * `\n * ```\n *\n * In this case, items are mapped over only when the array reference changes.\n *\n * @param value the value to check before re-rendering\n * @param f the template function\n */\nexport const guard = directive(GuardDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {GuardDirective};\n"], "names": ["initialValue", "guard", "directive", "Directive", "constructor", "this", "_previousValue", "render", "_value", "f", "update", "_part", "value", "Array", "isArray", "length", "every", "v", "i", "noChange", "from"], "mappings": ";;;;;;AAUA,MAAMA,EAAe,CAAA,EAyERC,EAAQC,EAvErB,cAA6BC,EAA7B,WAAAC,uBACUC,KAAcC,GAAYN,CA2BnC,CAzBC,MAAAO,CAAOC,EAAiBC,GACtB,OAAOA,GACR,CAEQ,MAAAC,CAAOC,GAAcC,EAAOH,IACnC,GAAII,MAAMC,QAAQF,IAEhB,GACEC,MAAMC,QAAQT,KAAKC,KACnBD,KAAKC,GAAeS,SAAWH,EAAMG,QACrCH,EAAMI,OAAM,CAACC,EAAGC,IAAMD,IAAOZ,KAAKC,GAAkCY,KAEpE,OAAOC,OAEJ,GAAId,KAAKC,KAAmBM,EAEjC,OAAOO,EAOT,OAFAd,KAAKC,GAAiBO,MAAMC,QAAQF,GAASC,MAAMO,KAAKR,GAASA,EACvDP,KAAKE,OAAOK,EAAOH,EAE9B"}