{"version": 3, "file": "live.js", "sources": ["../src/directives/live.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange, nothing} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\nimport {isSingleExpression, setCommittedValue} from '../directive-helpers.js';\n\nclass LiveDirective extends Directive {\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      !(\n        partInfo.type === PartType.PROPERTY ||\n        partInfo.type === PartType.ATTRIBUTE ||\n        partInfo.type === PartType.BOOLEAN_ATTRIBUTE\n      )\n    ) {\n      throw new Error(\n        'The `live` directive is not allowed on child or event bindings'\n      );\n    }\n    if (!isSingleExpression(partInfo)) {\n      throw new Error('`live` bindings can only contain a single expression');\n    }\n  }\n\n  render(value: unknown) {\n    return value;\n  }\n\n  override update(part: AttributePart, [value]: DirectiveParameters<this>) {\n    if (value === noChange || value === nothing) {\n      return value;\n    }\n    const element = part.element;\n    const name = part.name;\n\n    if (part.type === PartType.PROPERTY) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      if (value === (element as any)[name]) {\n        return noChange;\n      }\n    } else if (part.type === PartType.BOOLEAN_ATTRIBUTE) {\n      if (!!value === element.hasAttribute(name)) {\n        return noChange;\n      }\n    } else if (part.type === PartType.ATTRIBUTE) {\n      if (element.getAttribute(name) === String(value)) {\n        return noChange;\n      }\n    }\n    // Resets the part's value, causing its dirty-check to fail so that it\n    // always sets the value.\n    setCommittedValue(part);\n    return value;\n  }\n}\n\n/**\n * Checks binding values against live DOM values, instead of previously bound\n * values, when determining whether to update the value.\n *\n * This is useful for cases where the DOM value may change from outside of\n * lit-html, such as with a binding to an `<input>` element's `value` property,\n * a content editable elements text, or to a custom element that changes it's\n * own properties or attributes.\n *\n * In these cases if the DOM value changes, but the value set through lit-html\n * bindings hasn't, lit-html won't know to update the DOM value and will leave\n * it alone. If this is not what you want--if you want to overwrite the DOM\n * value with the bound value no matter what--use the `live()` directive:\n *\n * ```js\n * html`<input .value=${live(x)}>`\n * ```\n *\n * `live()` performs a strict equality check against the live DOM value, and if\n * the new value is equal to the live value, does nothing. This means that\n * `live()` should not be used when the binding will cause a type conversion. If\n * you use `live()` with an attribute binding, make sure that only strings are\n * passed in, or the binding will update every render.\n */\nexport const live = directive(LiveDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {LiveDirective};\n"], "names": ["live", "directive", "Directive", "constructor", "partInfo", "super", "type", "PartType", "PROPERTY", "ATTRIBUTE", "BOOLEAN_ATTRIBUTE", "Error", "isSingleExpression", "render", "value", "update", "part", "noChange", "nothing", "element", "name", "hasAttribute", "getAttribute", "String", "setCommittedValue"], "mappings": ";;;;;SA2FaA,EAAOC,EA3EpB,cAA4BC,EAC1B,WAAAC,CAAYC,GAEV,GADAC,MAAMD,GAGFA,EAASE,OAASC,EAASC,UAC3BJ,EAASE,OAASC,EAASE,WAC3BL,EAASE,OAASC,EAASG,kBAG7B,MAAUC,MACR,kEAGJ,IAAKC,EAAmBR,GACtB,MAAUO,MAAM,uDAEnB,CAED,MAAAE,CAAOC,GACL,OAAOA,CACR,CAEQ,MAAAC,CAAOC,GAAsBF,IACpC,GAAIA,IAAUG,GAAYH,IAAUI,EAClC,OAAOJ,EAET,MAAMK,EAAUH,EAAKG,QACfC,EAAOJ,EAAKI,KAElB,GAAIJ,EAAKV,OAASC,EAASC,UAEzB,GAAIM,IAAWK,EAAgBC,GAC7B,OAAOH,OAEJ,GAAID,EAAKV,OAASC,EAASG,mBAChC,KAAMI,IAAUK,EAAQE,aAAaD,GACnC,OAAOH,OAEJ,GAAID,EAAKV,OAASC,EAASE,WAC5BU,EAAQG,aAAaF,KAAiBN,EAAPS,GACjC,OAAON,EAMX,OADAO,EAAkBR,GACXF,CACR"}