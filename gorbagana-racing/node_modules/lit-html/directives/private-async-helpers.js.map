{"version": 3, "file": "private-async-helpers.js", "sources": ["../src/directives/private-async-helpers.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// Note, this module is not included in package exports so that it's private to\n// our first-party directives. If it ends up being useful, we can open it up and\n// export it.\n\n/**\n * Helper to iterate an AsyncIterable in its own closure.\n * @param iterable The iterable to iterate\n * @param callback The callback to call for each value. If the callback returns\n * `false`, the loop will be broken.\n */\nexport const forAwaitOf = async <T>(\n  iterable: AsyncIterable<T>,\n  callback: (value: T) => Promise<boolean>\n) => {\n  for await (const v of iterable) {\n    if ((await callback(v)) === false) {\n      return;\n    }\n  }\n};\n\n/**\n * Holds a reference to an instance that can be disconnected and reconnected,\n * so that a closure over the ref (e.g. in a then function to a promise) does\n * not strongly hold a ref to the instance. Approximates a WeakRef but must\n * be manually connected & disconnected to the backing instance.\n */\nexport class PseudoWeakRef<T> {\n  private _ref?: T;\n  constructor(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Disassociates the ref with the backing instance.\n   */\n  disconnect() {\n    this._ref = undefined;\n  }\n  /**\n   * Reassociates the ref with the backing instance.\n   */\n  reconnect(ref: T) {\n    this._ref = ref;\n  }\n  /**\n   * Retrieves the backing instance (will be undefined when disconnected)\n   */\n  deref() {\n    return this._ref;\n  }\n}\n\n/**\n * A helper to pause and resume waiting on a condition in an async function\n */\nexport class Pauser {\n  private _promise?: Promise<void> = undefined;\n  private _resolve?: () => void = undefined;\n  /**\n   * When paused, returns a promise to be awaited; when unpaused, returns\n   * undefined. Note that in the microtask between the pauser being resumed\n   * an await of this promise resolving, the pauser could be paused again,\n   * hence callers should check the promise in a loop when awaiting.\n   * @returns A promise to be awaited when paused or undefined\n   */\n  get() {\n    return this._promise;\n  }\n  /**\n   * Creates a promise to be awaited\n   */\n  pause() {\n    this._promise ??= new Promise((resolve) => (this._resolve = resolve));\n  }\n  /**\n   * Resolves the promise which may be awaited\n   */\n  resume() {\n    this._resolve?.();\n    this._promise = this._resolve = undefined;\n  }\n}\n"], "names": ["forAwaitOf", "async", "iterable", "callback", "v", "PseudoWeakRef", "constructor", "ref", "this", "_ref", "disconnect", "undefined", "reconnect", "deref", "Pa<PERSON>", "_promise", "_resolve", "get", "pause", "Promise", "resolve", "resume"], "mappings": ";;;;;AAgBa,MAAAA,EAAaC,MACxBC,EACAC,KAEA,UAAW,MAAMC,KAAKF,EACpB,IAA4B,UAAjBC,EAASC,GAClB,MAEH,QASUC,EAEX,WAAAC,CAAYC,GACVC,KAAKC,EAAOF,CACb,CAID,UAAAG,GACEF,KAAKC,OAAOE,CACb,CAID,SAAAC,CAAUL,GACRC,KAAKC,EAAOF,CACb,CAID,KAAAM,GACE,OAAOL,KAAKC,CACb,QAMUK,EAAb,WAAAR,GACUE,KAAQO,OAAmBJ,EAC3BH,KAAQQ,OAAgBL,CAwBjC,CAhBC,GAAAM,GACE,OAAOT,KAAKO,CACb,CAID,KAAAG,GACEV,KAAKO,IAAa,IAAII,SAASC,GAAaZ,KAAKQ,EAAWI,GAC7D,CAID,MAAAC,GACEb,KAAKQ,MACLR,KAAKO,EAAWP,KAAKQ,OAAWL,CACjC"}