{"version": 3, "file": "range.js", "sources": ["../src/directives/range.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * Returns an iterable of integers from `start` to `end` (exclusive)\n * incrementing by `step`.\n *\n * If `start` is omitted, the range starts at `0`. `step` defaults to `1`.\n *\n * @example\n *\n * ```ts\n * render() {\n *   return html`\n *     ${map(range(8), () => html`<div class=\"cell\"></div>`)}\n *   `;\n * }\n * ```\n */\nexport function range(end: number): Iterable<number>;\nexport function range(\n  start: number,\n  end: number,\n  step?: number\n): Iterable<number>;\nexport function* range(startOrEnd: number, end?: number, step = 1) {\n  const start = end === undefined ? 0 : startOrEnd;\n  end ??= startOrEnd;\n  for (let i = start; step > 0 ? i < end : end < i; i += step) {\n    yield i;\n  }\n}\n"], "names": ["range", "startOrEnd", "end", "step", "start", "undefined", "i"], "mappings": ";;;;;AA4BM,SAAWA,EAAMC,EAAoBC,EAAcC,EAAO,GAC9D,MAAMC,OAAgBC,IAARH,EAAoB,EAAID,EACtCC,IAAQD,EACR,IAAK,IAAIK,EAAIF,EAAOD,EAAO,EAAIG,EAAIJ,EAAMA,EAAMI,EAAGA,GAAKH,QAC/CG,CAEV"}