{"version": 3, "file": "ref.js", "sources": ["../src/directives/ref.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\nimport {nothing, ElementPart} from '../lit-html.js';\nimport {directive, AsyncDirective} from '../async-directive.js';\n\n/**\n * Creates a new Ref object, which is container for a reference to an element.\n */\nexport const createRef = <T = Element>() => new Ref<T>();\n\n/**\n * An object that holds a ref value.\n */\nclass Ref<T = Element> {\n  /**\n   * The current Element value of the ref, or else `undefined` if the ref is no\n   * longer rendered.\n   */\n  readonly value?: T;\n}\n\nexport type {Ref};\n\ninterface RefInternal {\n  value: Element | undefined;\n}\n\n// When callbacks are used for refs, this map tracks the last value the callback\n// was called with, for ensuring a directive doesn't clear the ref if the ref\n// has already been rendered to a new spot. It is double-keyed on both the\n// context (`options.host`) and the callback, since we auto-bind class methods\n// to `options.host`.\nconst lastElementForContextAndCallback = new WeakMap<\n  object,\n  WeakMap<Function, Element | undefined>\n>();\n\nexport type RefOrCallback<T = Element> = Ref<T> | ((el: T | undefined) => void);\n\nclass RefDirective extends AsyncDirective {\n  private _element?: Element;\n  private _ref?: RefOrCallback;\n  private _context?: object;\n\n  render(_ref?: RefOrCallback) {\n    return nothing;\n  }\n\n  override update(part: ElementPart, [ref]: Parameters<this['render']>) {\n    const refChanged = ref !== this._ref;\n    if (refChanged && this._ref !== undefined) {\n      // The ref passed to the directive has changed;\n      // unset the previous ref's value\n      this._updateRefValue(undefined);\n    }\n    if (refChanged || this._lastElementForRef !== this._element) {\n      // We either got a new ref or this is the first render;\n      // store the ref/element & update the ref value\n      this._ref = ref;\n      this._context = part.options?.host;\n      this._updateRefValue((this._element = part.element));\n    }\n    return nothing;\n  }\n\n  private _updateRefValue(element: Element | undefined) {\n    if (!this.isConnected) {\n      element = undefined;\n    }\n    if (typeof this._ref === 'function') {\n      // If the current ref was called with a previous value, call with\n      // `undefined`; We do this to ensure callbacks are called in a consistent\n      // way regardless of whether a ref might be moving up in the tree (in\n      // which case it would otherwise be called with the new value before the\n      // previous one unsets it) and down in the tree (where it would be unset\n      // before being set). Note that element lookup is keyed by\n      // both the context and the callback, since we allow passing unbound\n      // functions that are called on options.host, and we want to treat\n      // these as unique \"instances\" of a function.\n      const context = this._context ?? globalThis;\n      let lastElementForCallback =\n        lastElementForContextAndCallback.get(context);\n      if (lastElementForCallback === undefined) {\n        lastElementForCallback = new WeakMap();\n        lastElementForContextAndCallback.set(context, lastElementForCallback);\n      }\n      if (lastElementForCallback.get(this._ref) !== undefined) {\n        this._ref.call(this._context, undefined);\n      }\n      lastElementForCallback.set(this._ref, element);\n      // Call the ref with the new element value\n      if (element !== undefined) {\n        this._ref.call(this._context, element);\n      }\n    } else {\n      (this._ref as RefInternal)!.value = element;\n    }\n  }\n\n  private get _lastElementForRef() {\n    return typeof this._ref === 'function'\n      ? lastElementForContextAndCallback\n          .get(this._context ?? globalThis)\n          ?.get(this._ref)\n      : this._ref?.value;\n  }\n\n  override disconnected() {\n    // Only clear the box if our element is still the one in it (i.e. another\n    // directive instance hasn't rendered its element to it before us); that\n    // only happens in the event of the directive being cleared (not via manual\n    // disconnection)\n    if (this._lastElementForRef === this._element) {\n      this._updateRefValue(undefined);\n    }\n  }\n\n  override reconnected() {\n    // If we were manually disconnected, we can safely put our element back in\n    // the box, since no rendering could have occurred to change its state\n    this._updateRefValue(this._element);\n  }\n}\n\n/**\n * Sets the value of a Ref object or calls a ref callback with the element it's\n * bound to.\n *\n * A Ref object acts as a container for a reference to an element. A ref\n * callback is a function that takes an element as its only argument.\n *\n * The ref directive sets the value of the Ref object or calls the ref callback\n * during rendering, if the referenced element changed.\n *\n * Note: If a ref callback is rendered to a different element position or is\n * removed in a subsequent render, it will first be called with `undefined`,\n * followed by another call with the new element it was rendered to (if any).\n *\n * ```js\n * // Using Ref object\n * const inputRef = createRef();\n * render(html`<input ${ref(inputRef)}>`, container);\n * inputRef.value.focus();\n *\n * // Using callback\n * const callback = (inputElement) => inputElement.focus();\n * render(html`<input ${ref(callback)}>`, container);\n * ```\n */\nexport const ref = directive(RefDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {RefDirective};\n"], "names": ["createRef", "Ref", "lastElementForContextAndCallback", "WeakMap", "ref", "directive", "AsyncDirective", "render", "_ref", "nothing", "update", "part", "refChanged", "this", "undefined", "_updateRefValue", "_lastElementForRef", "_element", "_context", "options", "host", "element", "isConnected", "context", "globalThis", "lastElementForCallback", "get", "set", "call", "value", "disconnected", "reconnected"], "mappings": ";;;;;GAWa,MAAAA,EAAY,IAAmB,IAAIC,EAKhD,MAAMA,GAmBN,MAAMC,EAAmC,IAAIC,QAqHhCC,EAAMC,EA9GnB,cAA2BC,EAKzB,MAAAC,CAAOC,GACL,OAAOC,CACR,CAEQ,MAAAC,CAAOC,GAAoBP,IAClC,MAAMQ,EAAaR,IAAQS,KAAKL,EAahC,OAZII,QAA4BE,IAAdD,KAAKL,GAGrBK,KAAKE,QAAgBD,IAEnBF,GAAcC,KAAKG,KAAuBH,KAAKI,MAGjDJ,KAAKL,EAAOJ,EACZS,KAAKK,GAAWP,EAAKQ,SAASC,KAC9BP,KAAKE,GAAiBF,KAAKI,GAAWN,EAAKU,UAEtCZ,CACR,CAEO,EAAAM,CAAgBM,GAItB,GAHKR,KAAKS,cACRD,OAAUP,GAEa,mBAAdD,KAAKL,EAAqB,CAUnC,MAAMe,EAAUV,KAAKK,IAAYM,WACjC,IAAIC,EACFvB,EAAiCwB,IAAIH,QACRT,IAA3BW,IACFA,EAAyB,IAAItB,QAC7BD,EAAiCyB,IAAIJ,EAASE,SAEFX,IAA1CW,EAAuBC,IAAIb,KAAKL,IAClCK,KAAKL,EAAKoB,KAAKf,KAAKK,QAAUJ,GAEhCW,EAAuBE,IAAId,KAAKL,EAAMa,QAEtBP,IAAZO,GACFR,KAAKL,EAAKoB,KAAKf,KAAKK,GAAUG,EAEjC,MACER,KAAKL,EAAsBqB,MAAQR,CAEvC,CAED,MAAYL,GACV,MAA4B,mBAAdH,KAAKL,EACfN,EACGwB,IAAIb,KAAKK,IAAYM,aACpBE,IAAIb,KAAKL,GACbK,KAAKL,GAAMqB,KAChB,CAEQ,YAAAC,GAKHjB,KAAKG,KAAuBH,KAAKI,IACnCJ,KAAKE,QAAgBD,EAExB,CAEQ,WAAAiB,GAGPlB,KAAKE,GAAgBF,KAAKI,GAC3B"}