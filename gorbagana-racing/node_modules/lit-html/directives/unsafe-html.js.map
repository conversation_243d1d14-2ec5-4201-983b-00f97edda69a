{"version": 3, "file": "unsafe-html.js", "sources": ["../src/directives/unsafe-html.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing, TemplateResult, noChange} from '../lit-html.js';\nimport {directive, Directive, PartInfo, PartType} from '../directive.js';\n\nconst HTML_RESULT = 1;\n\nexport class UnsafeHTMLDirective extends Directive {\n  static directiveName = 'unsafeHTML';\n  static resultType = HTML_RESULT;\n\n  private _value: unknown = nothing;\n  private _templateResult?: TemplateResult;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() can only be used in child bindings`\n      );\n    }\n  }\n\n  render(value: string | typeof nothing | typeof noChange | undefined | null) {\n    if (value === nothing || value == null) {\n      this._templateResult = undefined;\n      return (this._value = value);\n    }\n    if (value === noChange) {\n      return value;\n    }\n    if (typeof value != 'string') {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() called with a non-string value`\n      );\n    }\n    if (value === this._value) {\n      return this._templateResult;\n    }\n    this._value = value;\n    const strings = [value] as unknown as TemplateStringsArray;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (strings as any).raw = strings;\n    // WARNING: impersonating a TemplateResult like this is extremely\n    // dangerous. Third-party directives should not do this.\n    return (this._templateResult = {\n      // Cast to a known set of integers that satisfy ResultType so that we\n      // don't have to export ResultType and possibly encourage this pattern.\n      // This property needs to remain unminified.\n      ['_$litType$']: (this.constructor as typeof UnsafeHTMLDirective)\n        .resultType as 1 | 2,\n      strings,\n      values: [],\n    });\n  }\n}\n\n/**\n * Renders the result as HTML, rather than text.\n *\n * The values `undefined`, `null`, and `nothing`, will all result in no content\n * (empty string) being rendered.\n *\n * Note, this is unsafe to use with any user-provided input that hasn't been\n * sanitized or escaped, as it may lead to cross-site-scripting\n * vulnerabilities.\n */\nexport const unsafeHTML = directive(UnsafeHTMLDirective);\n"], "names": ["UnsafeHTMLDirective", "Directive", "constructor", "partInfo", "super", "this", "_value", "nothing", "type", "PartType", "CHILD", "Error", "directiveName", "render", "value", "_templateResult", "undefined", "noChange", "strings", "raw", "_$litType$", "resultType", "values", "unsafeHTML", "directive"], "mappings": ";;;;;GAWM,MAAOA,UAA4BC,EAOvC,WAAAC,CAAYC,GAEV,GADAC,MAAMD,GAJAE,KAAMC,GAAYC,EAKpBJ,EAASK,OAASC,EAASC,MAC7B,MAAUC,MAELN,KAAKH,YAA2CU,cADnD,wCAKL,CAED,MAAAC,CAAOC,GACL,GAAIA,IAAUP,GAAoB,MAATO,EAEvB,OADAT,KAAKU,QAAkBC,EACfX,KAAKC,GAASQ,EAExB,GAAIA,IAAUG,EACZ,OAAOH,EAET,GAAoB,iBAATA,EACT,MAAUH,MAELN,KAAKH,YAA2CU,cADnD,qCAKJ,GAAIE,IAAUT,KAAKC,GACjB,OAAOD,KAAKU,GAEdV,KAAKC,GAASQ,EACd,MAAMI,EAAU,CAACJ,GAKjB,OAHCI,EAAgBC,IAAMD,EAGfb,KAAKU,GAAkB,CAI7BK,WAAiBf,KAAKH,YACnBmB,WACHH,UACAI,OAAQ,GAEX,EAlDMtB,EAAaY,cAAG,aAChBZ,EAAUqB,WAJC,QAkEPE,EAAaC,EAAUxB"}