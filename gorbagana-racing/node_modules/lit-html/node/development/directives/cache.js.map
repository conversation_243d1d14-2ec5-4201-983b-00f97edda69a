{"version": 3, "file": "cache.js", "sources": ["../../../src/directives/cache.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  TemplateResult,\n  ChildPart,\n  RootPart,\n  render,\n  nothing,\n  CompiledTemplateResult,\n} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n} from '../directive.js';\nimport {\n  clearPart,\n  getCommittedValue,\n  insertPart,\n  isCompiledTemplateResult,\n  isTemplateResult,\n  setCommittedValue,\n} from '../directive-helpers.js';\n\n/**\n * The template strings array contents are not compatible between the two\n * template result types as the compiled template contains a prepared string;\n * only use the returned template strings array as a cache key.\n */\nconst getStringsFromTemplateResult = (\n  result: TemplateResult | CompiledTemplateResult\n): TemplateStringsArray =>\n  isCompiledTemplateResult(result) ? result['_$litType$'].h : result.strings;\n\nclass CacheDirective extends Directive {\n  private _templateCache = new WeakMap<TemplateStringsArray, RootPart>();\n  private _value?: TemplateResult | CompiledTemplateResult;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n  }\n\n  render(v: unknown) {\n    // Return an array of the value to induce lit-html to create a ChildPart\n    // for the value that we can move into the cache.\n    return [v];\n  }\n\n  override update(containerPart: ChildPart, [v]: DirectiveParameters<this>) {\n    const _valueKey = isTemplateResult(this._value)\n      ? getStringsFromTemplateResult(this._value)\n      : null;\n    const vKey = isTemplateResult(v) ? getStringsFromTemplateResult(v) : null;\n\n    // If the previous value is a TemplateResult and the new value is not,\n    // or is a different Template as the previous value, move the child part\n    // into the cache.\n    if (_valueKey !== null && (vKey === null || _valueKey !== vKey)) {\n      // This is always an array because we return [v] in render()\n      const partValue = getCommittedValue(containerPart) as Array<ChildPart>;\n      const childPart = partValue.pop()!;\n      let cachedContainerPart = this._templateCache.get(_valueKey);\n      if (cachedContainerPart === undefined) {\n        const fragment = document.createDocumentFragment();\n        cachedContainerPart = render(nothing, fragment);\n        cachedContainerPart.setConnected(false);\n        this._templateCache.set(_valueKey, cachedContainerPart);\n      }\n      // Move into cache\n      setCommittedValue(cachedContainerPart, [childPart]);\n      insertPart(cachedContainerPart, undefined, childPart);\n    }\n    // If the new value is a TemplateResult and the previous value is not,\n    // or is a different Template as the previous value, restore the child\n    // part from the cache.\n    if (vKey !== null) {\n      if (_valueKey === null || _valueKey !== vKey) {\n        const cachedContainerPart = this._templateCache.get(vKey);\n        if (cachedContainerPart !== undefined) {\n          // Move the cached part back into the container part value\n          const partValue = getCommittedValue(\n            cachedContainerPart\n          ) as Array<ChildPart>;\n          const cachedPart = partValue.pop()!;\n          // Move cached part back into DOM\n          clearPart(containerPart);\n          insertPart(containerPart, undefined, cachedPart);\n          setCommittedValue(containerPart, [cachedPart]);\n        }\n      }\n      // Because vKey is non null, v must be a TemplateResult.\n      this._value = v as TemplateResult | CompiledTemplateResult;\n    } else {\n      this._value = undefined;\n    }\n    return this.render(v);\n  }\n}\n\n/**\n * Enables fast switching between multiple templates by caching the DOM nodes\n * and TemplateInstances produced by the templates.\n *\n * Example:\n *\n * ```js\n * let checked = false;\n *\n * html`\n *   ${cache(checked ? html`input is checked` : html`input is not checked`)}\n * `\n * ```\n */\nexport const cache = directive(CacheDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {CacheDirective};\n"], "names": [], "mappings": ";;;;AAAA;;;;AAIG;AAyBH;;;;AAIG;AACH,MAAM,4BAA4B,GAAG,CACnC,MAA+C,KAE/C,wBAAwB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;AAE7E,MAAM,cAAe,SAAQ,SAAS,CAAA;AAIpC,IAAA,WAAA,CAAY,QAAkB,EAAA;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;AAJV,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,OAAO,EAAkC,CAAC;KAKtE;AAED,IAAA,MAAM,CAAC,CAAU,EAAA;;;QAGf,OAAO,CAAC,CAAC,CAAC,CAAC;KACZ;AAEQ,IAAA,MAAM,CAAC,aAAwB,EAAE,CAAC,CAAC,CAA4B,EAAA;AACtE,QAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7C,cAAE,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC;cACzC,IAAI,CAAC;AACT,QAAA,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;;;AAK1E,QAAA,IAAI,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,CAAC,EAAE;;AAE/D,YAAA,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAqB,CAAC;AACvE,YAAA,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,EAAG,CAAC;YACnC,IAAI,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC7D,YAAA,IAAI,mBAAmB,KAAK,SAAS,EAAE;AACrC,gBAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;AACnD,gBAAA,mBAAmB,GAAG,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AAChD,gBAAA,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;aACzD;;AAED,YAAA,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACpD,YAAA,UAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;SACvD;;;;AAID,QAAA,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;gBAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1D,gBAAA,IAAI,mBAAmB,KAAK,SAAS,EAAE;;AAErC,oBAAA,MAAM,SAAS,GAAG,iBAAiB,CACjC,mBAAmB,CACA,CAAC;AACtB,oBAAA,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,EAAG,CAAC;;oBAEpC,SAAS,CAAC,aAAa,CAAC,CAAC;AACzB,oBAAA,UAAU,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AACjD,oBAAA,iBAAiB,CAAC,aAAa,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;iBAChD;aACF;;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,CAA4C,CAAC;SAC5D;aAAM;AACL,YAAA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;AACD,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KACvB;AACF,CAAA;AAED;;;;;;;;;;;;;AAaG;MACU,KAAK,GAAG,SAAS,CAAC,cAAc;;;;"}