{"version": 3, "file": "keyed.js", "sources": ["../../../src/directives/keyed.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2021 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  ChildPart,\n  DirectiveParameters,\n} from '../directive.js';\nimport {setCommittedValue} from '../directive-helpers.js';\n\nclass Keyed extends Directive {\n  key: unknown = nothing;\n\n  render(k: unknown, v: unknown) {\n    this.key = k;\n    return v;\n  }\n\n  override update(part: ChildPart, [k, v]: DirectiveParameters<this>) {\n    if (k !== this.key) {\n      // Clear the part before returning a value. The one-arg form of\n      // setCommittedValue sets the value to a sentinel which forces a\n      // commit the next render.\n      setCommittedValue(part);\n      this.key = k;\n    }\n    return v;\n  }\n}\n\n/**\n * Associates a renderable value with a unique key. When the key changes, the\n * previous DOM is removed and disposed before rendering the next value, even\n * if the value - such as a template - is the same.\n *\n * This is useful for forcing re-renders of stateful components, or working\n * with code that expects new data to generate new HTML elements, such as some\n * animation techniques.\n */\nexport const keyed = directive(Keyed);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {Keyed};\n"], "names": [], "mappings": ";;;;AAAA;;;;AAIG;AAWH,MAAM,KAAM,SAAQ,SAAS,CAAA;AAA7B,IAAA,WAAA,GAAA;;QACE,IAAG,CAAA,GAAA,GAAY,OAAO,CAAC;KAiBxB;IAfC,MAAM,CAAC,CAAU,EAAE,CAAU,EAAA;AAC3B,QAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACb,QAAA,OAAO,CAAC,CAAC;KACV;AAEQ,IAAA,MAAM,CAAC,IAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAA4B,EAAA;AAChE,QAAA,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;;;;YAIlB,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACxB,YAAA,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;SACd;AACD,QAAA,OAAO,CAAC,CAAC;KACV;AACF,CAAA;AAED;;;;;;;;AAQG;MACU,KAAK,GAAG,SAAS,CAAC,KAAK;;;;"}