{"version": 3, "file": "style-map.js", "sources": ["../../../src/directives/style-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of CSS properties and values.\n *\n * The key should be either a valid CSS property name string, like\n * `'background-color'`, or a valid JavaScript camel case property name\n * for CSSStyleDeclaration like `backgroundColor`.\n */\nexport interface StyleInfo {\n  [name: string]: string | number | undefined | null;\n}\n\nconst important = 'important';\n// The leading space is important\nconst importantFlag = ' !' + important;\n// How many characters to remove from a value, as a negative number\nconst flagTrim = 0 - importantFlag.length;\n\nclass StyleMapDirective extends Directive {\n  private _previousStyleProperties?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'style' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        'The `styleMap` directive must be used in the `style` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(styleInfo: Readonly<StyleInfo>) {\n    return Object.keys(styleInfo).reduce((style, prop) => {\n      const value = styleInfo[prop];\n      if (value == null) {\n        return style;\n      }\n      // Convert property names from camel-case to dash-case, i.e.:\n      //  `backgroundColor` -> `background-color`\n      // Vendor-prefixed names need an extra `-` appended to front:\n      //  `webkitAppearance` -> `-webkit-appearance`\n      // Exception is any property name containing a dash, including\n      // custom properties; we assume these are already dash-cased i.e.:\n      //  `--my-button-color` --> `--my-button-color`\n      prop = prop.includes('-')\n        ? prop\n        : prop\n            .replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, '-$&')\n            .toLowerCase();\n      return style + `${prop}:${value};`;\n    }, '');\n  }\n\n  override update(part: AttributePart, [styleInfo]: DirectiveParameters<this>) {\n    const {style} = part.element as HTMLElement;\n\n    if (this._previousStyleProperties === undefined) {\n      this._previousStyleProperties = new Set(Object.keys(styleInfo));\n      return this.render(styleInfo);\n    }\n\n    // Remove old properties that no longer exist in styleInfo\n    for (const name of this._previousStyleProperties) {\n      // If the name isn't in styleInfo or it's null/undefined\n      if (styleInfo[name] == null) {\n        this._previousStyleProperties!.delete(name);\n        if (name.includes('-')) {\n          style.removeProperty(name);\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = null;\n        }\n      }\n    }\n\n    // Add or update properties\n    for (const name in styleInfo) {\n      const value = styleInfo[name];\n      if (value != null) {\n        this._previousStyleProperties.add(name);\n        const isImportant =\n          typeof value === 'string' && value.endsWith(importantFlag);\n        if (name.includes('-') || isImportant) {\n          style.setProperty(\n            name,\n            isImportant\n              ? (value as string).slice(0, flagTrim)\n              : (value as string),\n            isImportant ? important : ''\n          );\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = value;\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies CSS properties to an element.\n *\n * `styleMap` can only be used in the `style` attribute and must be the only\n * expression in the attribute. It takes the property names in the\n * {@link StyleInfo styleInfo} object and adds the properties to the inline\n * style of the element.\n *\n * Property names with dashes (`-`) are assumed to be valid CSS\n * property names and set on the element's style object using `setProperty()`.\n * Names without dashes are assumed to be camelCased JavaScript property names\n * and set on the element's style object using property assignment, allowing the\n * style object to translate JavaScript-style names to CSS property names.\n *\n * For example `styleMap({backgroundColor: 'red', 'border-top': '5px', '--size':\n * '0'})` sets the `background-color`, `border-top` and `--size` properties.\n *\n * @param styleInfo\n * @see {@link https://lit.dev/docs/templates/directives/#stylemap styleMap code samples on Lit.dev}\n */\nexport const styleMap = directive(StyleMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {StyleMapDirective};\n"], "names": [], "mappings": ";;;AAAA;;;;AAIG;AAsBH,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B;AACA,MAAM,aAAa,GAAG,IAAI,GAAG,SAAS,CAAC;AACvC;AACA,MAAM,QAAQ,GAAG,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;AAE1C,MAAM,iBAAkB,SAAQ,SAAS,CAAA;AAGvC,IAAA,WAAA,CAAY,QAAkB,EAAA;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;AAChB,QAAA,IACE,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,SAAS;YACpC,QAAQ,CAAC,IAAI,KAAK,OAAO;AACxB,YAAA,QAAQ,CAAC,OAAO,EAAE,MAAiB,GAAG,CAAC,EACxC;YACA,MAAM,IAAI,KAAK,CACb,iEAAiE;AAC/D,gBAAA,6CAA6C,CAChD,CAAC;SACH;KACF;AAED,IAAA,MAAM,CAAC,SAA8B,EAAA;AACnC,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAI;AACnD,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,gBAAA,OAAO,KAAK,CAAC;aACd;;;;;;;;AAQD,YAAA,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AACvB,kBAAE,IAAI;AACN,kBAAE,IAAI;AACD,qBAAA,OAAO,CAAC,mCAAmC,EAAE,KAAK,CAAC;AACnD,qBAAA,WAAW,EAAE,CAAC;AACrB,YAAA,OAAO,KAAK,GAAG,CAAA,EAAG,IAAI,CAAI,CAAA,EAAA,KAAK,GAAG,CAAC;SACpC,EAAE,EAAE,CAAC,CAAC;KACR;AAEQ,IAAA,MAAM,CAAC,IAAmB,EAAE,CAAC,SAAS,CAA4B,EAAA;AACzE,QAAA,MAAM,EAAC,KAAK,EAAC,GAAG,IAAI,CAAC,OAAsB,CAAC;AAE5C,QAAA,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE;AAC/C,YAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAChE,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAC/B;;AAGD,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,wBAAwB,EAAE;;AAEhD,YAAA,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;AAC3B,gBAAA,IAAI,CAAC,wBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,gBAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACtB,oBAAA,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;iBAC5B;qBAAM;;AAEJ,oBAAA,KAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;iBAC7B;aACF;SACF;;AAGD,QAAA,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;AAC5B,YAAA,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAC9B,YAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,gBAAA,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,gBAAA,MAAM,WAAW,GACf,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC7D,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAW,EAAE;AACrC,oBAAA,KAAK,CAAC,WAAW,CACf,IAAI,EACJ,WAAW;0BACN,KAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC;AACtC,0BAAG,KAAgB,EACrB,WAAW,GAAG,SAAS,GAAG,EAAE,CAC7B,CAAC;iBACH;qBAAM;;AAEJ,oBAAA,KAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;iBAC9B;aACF;SACF;AACD,QAAA,OAAO,QAAQ,CAAC;KACjB;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;AAmBG;MACU,QAAQ,GAAG,SAAS,CAAC,iBAAiB;;;;"}