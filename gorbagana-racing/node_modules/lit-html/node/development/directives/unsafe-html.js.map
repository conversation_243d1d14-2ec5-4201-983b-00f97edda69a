{"version": 3, "file": "unsafe-html.js", "sources": ["../../../src/directives/unsafe-html.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {nothing, TemplateResult, noChange} from '../lit-html.js';\nimport {directive, Directive, PartInfo, PartType} from '../directive.js';\n\nconst HTML_RESULT = 1;\n\nexport class UnsafeHTMLDirective extends Directive {\n  static directiveName = 'unsafeHTML';\n  static resultType = HTML_RESULT;\n\n  private _value: unknown = nothing;\n  private _templateResult?: TemplateResult;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() can only be used in child bindings`\n      );\n    }\n  }\n\n  render(value: string | typeof nothing | typeof noChange | undefined | null) {\n    if (value === nothing || value == null) {\n      this._templateResult = undefined;\n      return (this._value = value);\n    }\n    if (value === noChange) {\n      return value;\n    }\n    if (typeof value != 'string') {\n      throw new Error(\n        `${\n          (this.constructor as typeof UnsafeHTMLDirective).directiveName\n        }() called with a non-string value`\n      );\n    }\n    if (value === this._value) {\n      return this._templateResult;\n    }\n    this._value = value;\n    const strings = [value] as unknown as TemplateStringsArray;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (strings as any).raw = strings;\n    // WARNING: impersonating a TemplateResult like this is extremely\n    // dangerous. Third-party directives should not do this.\n    return (this._templateResult = {\n      // Cast to a known set of integers that satisfy ResultType so that we\n      // don't have to export ResultType and possibly encourage this pattern.\n      // This property needs to remain unminified.\n      ['_$litType$']: (this.constructor as typeof UnsafeHTMLDirective)\n        .resultType as 1 | 2,\n      strings,\n      values: [],\n    });\n  }\n}\n\n/**\n * Renders the result as HTML, rather than text.\n *\n * The values `undefined`, `null`, and `nothing`, will all result in no content\n * (empty string) being rendered.\n *\n * Note, this is unsafe to use with any user-provided input that hasn't been\n * sanitized or escaped, as it may lead to cross-site-scripting\n * vulnerabilities.\n */\nexport const unsafeHTML = directive(UnsafeHTMLDirective);\n"], "names": [], "mappings": ";;;AAAA;;;;AAIG;AAKH,MAAM,WAAW,GAAG,CAAC,CAAC;AAEhB,MAAO,mBAAoB,SAAQ,SAAS,CAAA;AAOhD,IAAA,WAAA,CAAY,QAAkB,EAAA;QAC5B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAJV,IAAM,CAAA,MAAA,GAAY,OAAO,CAAC;QAKhC,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,EAAE;YACpC,MAAM,IAAI,KAAK,CACb,CACG,EAAA,IAAI,CAAC,WAA0C,CAAC,aACnD,CAAuC,qCAAA,CAAA,CACxC,CAAC;SACH;KACF;AAED,IAAA,MAAM,CAAC,KAAmE,EAAA;QACxE,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,EAAE;AACtC,YAAA,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACjC,YAAA,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE;SAC9B;AACD,QAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,CACG,EAAA,IAAI,CAAC,WAA0C,CAAC,aACnD,CAAmC,iCAAA,CAAA,CACpC,CAAC;SACH;AACD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;YACzB,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;AACD,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACpB,QAAA,MAAM,OAAO,GAAG,CAAC,KAAK,CAAoC,CAAC;;AAE1D,QAAA,OAAe,CAAC,GAAG,GAAG,OAAO,CAAC;;;AAG/B,QAAA,QAAQ,IAAI,CAAC,eAAe,GAAG;;;;AAI7B,YAAA,CAAC,YAAY,GAAI,IAAI,CAAC,WAA0C;iBAC7D,UAAmB;YACtB,OAAO;AACP,YAAA,MAAM,EAAE,EAAE;AACX,SAAA,EAAE;KACJ;;AAlDM,mBAAa,CAAA,aAAA,GAAG,YAAH,CAAgB;AAC7B,mBAAU,CAAA,UAAA,GAAG,WAAH,CAAe;AAoDlC;;;;;;;;;AASG;MACU,UAAU,GAAG,SAAS,CAAC,mBAAmB;;;;"}