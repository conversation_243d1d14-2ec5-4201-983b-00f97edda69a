{"version": 3, "file": "polyfill-support.js", "sources": ["../../src/polyfill-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/**\n * lit-html patch to support browsers without native web components.\n *\n * This module should be used in addition to loading the web components\n * polyfills via @webcomponents/webcomponentjs. When using those polyfills\n * support for polyfilled Shadow DOM is automatic via the ShadyDOM polyfill.\n * Scoping classes are added to DOM nodes to facilitate CSS scoping that\n * simulates the style scoping Shadow DOM provides. ShadyDOM does this scoping\n * to all elements added to the DOM. This module provides an important\n * optimization for this process by pre-scoping lit-html template\n * DOM. This means ShadyDOM does not have to scope each instance of the\n * template DOM. Instead, each template is scoped only once.\n *\n * Creating scoped CSS is not covered by this module. It is, however, integrated\n * into the lit-element and @lit/reactive-element packages. See the ShadyCSS docs\n * for how to apply scoping to CSS:\n * https://github.com/webcomponents/polyfills/tree/master/packages/shadycss#usage.\n *\n * @packageDocumentation\n */\n\nexport {};\n\ninterface RenderOptions {\n  readonly renderBefore?: ChildNode | null;\n  scope?: string;\n}\n\ninterface ShadyTemplateResult {\n  strings: TemplateStringsArray;\n  // This property needs to remain unminified.\n  ['_$litType$']?: string;\n}\n\n// Note, this is a dummy type as the full type here is big.\ninterface Directive {\n  __directive?: Directive;\n}\n\ninterface DirectiveParent {\n  _$parent?: DirectiveParent;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\ninterface PatchableChildPartConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableChildPart;\n}\n\ninterface PatchableChildPart {\n  __directive?: Directive;\n  _$committedValue: unknown;\n  _$startNode: ChildNode;\n  _$endNode: ChildNode | null;\n  options: RenderOptions;\n  _$setValue(value: unknown, directiveParent: DirectiveParent): void;\n  _$getTemplate(result: ShadyTemplateResult): HTMLTemplateElement;\n}\n\ninterface PatchableTemplate {\n  el: HTMLTemplateElement;\n}\n\ninterface PatchableTemplateConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-misused-new\n  new (...args: any[]): PatchableTemplate;\n  createElement(html: string, options?: RenderOptions): HTMLTemplateElement;\n}\n\ninterface PatchableTemplateInstance {\n  _$template: PatchableTemplate;\n}\n\n// Scopes that have had styling prepared. Note, must only be done once per\n// scope.\nconst styledScopes = new Set<string>();\n// Map of css per scope. This is collected during first scope render, used when\n// styling is prepared, and then discarded.\nconst scopeCssStore = new Map<string, string[]>();\n\nconst ENABLE_SHADYDOM_NOPATCH = true;\n\n// Note, explicitly use `var` here so that this can be re-defined when\n// bundled.\n// eslint-disable-next-line no-var\nvar DEV_MODE = true;\n\n/**\n * lit-html patches. These properties cannot be renamed.\n * * ChildPart.prototype._$getTemplate\n * * ChildPart.prototype._$setValue\n */\nconst polyfillSupport: NonNullable<typeof litHtmlPolyfillSupport> = (\n  Template: PatchableTemplateConstructor,\n  ChildPart: PatchableChildPartConstructor\n) => {\n  // polyfill-support is only needed if ShadyCSS or the ApplyShim is in use\n  // We test at the point of patching, which makes it safe to load\n  // webcomponentsjs and polyfill-support in either order\n  if (\n    window.ShadyCSS === undefined ||\n    (window.ShadyCSS.nativeShadow && !window.ShadyCSS.ApplyShim)\n  ) {\n    return;\n  }\n\n  // console.log(\n  //   '%c Making lit-html compatible with ShadyDOM/CSS.',\n  //   'color: lightgreen; font-style: italic'\n  // );\n\n  const wrap =\n    ENABLE_SHADYDOM_NOPATCH &&\n    window.ShadyDOM?.inUse &&\n    window.ShadyDOM?.noPatch === true\n      ? window.ShadyDOM!.wrap\n      : (node: Node) => node;\n\n  const needsPrepareStyles = (name: string | undefined) =>\n    name !== undefined && !styledScopes.has(name);\n\n  const cssForScope = (name: string) => {\n    let scopeCss = scopeCssStore.get(name);\n    if (scopeCss === undefined) {\n      scopeCssStore.set(name, (scopeCss = []));\n    }\n    return scopeCss;\n  };\n\n  const prepareStyles = (name: string, template: HTMLTemplateElement) => {\n    // Get styles\n    const scopeCss = cssForScope(name);\n    const hasScopeCss = scopeCss.length !== 0;\n    if (hasScopeCss) {\n      const style = document.createElement('style');\n      style.textContent = scopeCss.join('\\n');\n      // Note, it's important to add the style to the *end* of the template so\n      // it doesn't mess up part indices.\n      template.content.appendChild(style);\n    }\n    // Mark this scope as styled.\n    styledScopes.add(name);\n    // Remove stored data since it's no longer needed.\n    scopeCssStore.delete(name);\n    // ShadyCSS removes scopes and removes the style under ShadyDOM and leaves\n    // it under native Shadow DOM\n    window.ShadyCSS!.prepareTemplateStyles(template, name);\n    // Note, under native Shadow DOM, the style is added to the beginning of the\n    // template. It must be moved to the *end* of the template so it doesn't\n    // mess up part indices.\n    if (hasScopeCss && window.ShadyCSS!.nativeShadow) {\n      // If there were styles but the CSS text was empty, ShadyCSS will\n      // eliminate the style altogether, so the style here could be null\n      const style = template.content.querySelector('style');\n      if (style !== null) {\n        template.content.appendChild(style);\n      }\n    }\n  };\n\n  const scopedTemplateCache = new Map<\n    string | undefined,\n    Map<TemplateStringsArray, PatchableTemplate>\n  >();\n\n  /**\n   * Override to extract style elements from the template\n   * and store all style.textContent in the shady scope data.\n   * Note, it's ok to patch Template since it's only used via ChildPart.\n   */\n  const originalCreateElement = Template.createElement;\n  Template.createElement = function (html: string, options?: RenderOptions) {\n    const element = originalCreateElement.call(Template, html, options);\n    const scope = options?.scope;\n    if (scope !== undefined) {\n      if (!window.ShadyCSS!.nativeShadow) {\n        window.ShadyCSS!.prepareTemplateDom(element, scope);\n      }\n      // Process styles only if this scope is being prepared. Otherwise,\n      // leave styles as is for back compat with Lit1.\n      if (needsPrepareStyles(scope)) {\n        const scopeCss = cssForScope(scope);\n        // Remove styles and store textContent.\n        const styles = element.content.querySelectorAll(\n          'style'\n        ) as NodeListOf<HTMLStyleElement>;\n        // Store the css in this template in the scope css and remove the <style>\n        // from the template _before_ the node-walk captures part indices\n        scopeCss.push(\n          ...Array.from(styles).map((style) => {\n            style.parentNode?.removeChild(style);\n            return style.textContent!;\n          })\n        );\n      }\n    }\n    return element;\n  };\n\n  const renderContainer = document.createDocumentFragment();\n  const renderContainerMarker = document.createComment('');\n\n  const childPartProto = ChildPart.prototype;\n  /**\n   * Patch to apply gathered css via ShadyCSS. This is done only once per scope.\n   */\n  const setValue = childPartProto._$setValue;\n  childPartProto._$setValue = function (\n    this: PatchableChildPart,\n    value: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    const container = wrap(this._$startNode).parentNode!;\n    const scope = this.options?.scope;\n    if (container instanceof ShadowRoot && needsPrepareStyles(scope)) {\n      // Note, @apply requires outer => inner scope rendering on initial\n      // scope renders to apply property values correctly. Style preparation\n      // is tied to rendering into `shadowRoot`'s and this is typically done by\n      // custom elements. If this is done in `connectedCallback`, as is typical,\n      // the code below ensures the right order since content is rendered\n      // into a fragment first so the hosting element can prepare styles first.\n      // If rendering is done in the constructor, this won't work, but that's\n      // not supported in ShadyDOM anyway.\n      const startNode = this._$startNode;\n      const endNode = this._$endNode;\n\n      // Temporarily move this part into the renderContainer.\n      renderContainer.appendChild(renderContainerMarker);\n      this._$startNode = renderContainerMarker;\n      this._$endNode = null;\n\n      // Note, any nested template results render here and their styles will\n      // be extracted and collected.\n      setValue.call(this, value, directiveParent);\n\n      // Get the template for this result or create a dummy one if a result\n      // is not being rendered.\n      // This property needs to remain unminified.\n      const template = (value as ShadyTemplateResult)?.['_$litType$']\n        ? (this._$committedValue as PatchableTemplateInstance)._$template.el\n        : document.createElement('template');\n      prepareStyles(scope!, template);\n\n      // Note, this is the temporary startNode.\n      renderContainer.removeChild(renderContainerMarker);\n      // When using native Shadow DOM, include prepared style in shadowRoot.\n      if (window.ShadyCSS?.nativeShadow) {\n        const style = template.content.querySelector('style');\n        if (style !== null) {\n          renderContainer.appendChild(style.cloneNode(true));\n        }\n      }\n      container.insertBefore(renderContainer, endNode);\n      // Move part back to original container.\n      this._$startNode = startNode;\n      this._$endNode = endNode;\n    } else {\n      setValue.call(this, value, directiveParent);\n    }\n  };\n\n  /**\n   * Patch ChildPart._$getTemplate to look up templates in a cache bucketed\n   * by element name.\n   */\n  childPartProto._$getTemplate = function (\n    this: PatchableChildPart,\n    result: ShadyTemplateResult\n  ) {\n    const scope = this.options?.scope;\n    let templateCache = scopedTemplateCache.get(scope);\n    if (templateCache === undefined) {\n      scopedTemplateCache.set(scope, (templateCache = new Map()));\n    }\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(\n        result.strings,\n        (template = new Template(result, this.options))\n      );\n    }\n    return template;\n  };\n};\n\nif (ENABLE_SHADYDOM_NOPATCH) {\n  polyfillSupport.noPatchSupported = ENABLE_SHADYDOM_NOPATCH;\n}\n\nif (DEV_MODE) {\n  globalThis.litHtmlPolyfillSupportDevMode ??= polyfillSupport;\n} else {\n  globalThis.litHtmlPolyfillSupport ??= polyfillSupport;\n}\n"], "names": [], "mappings": "AAAA;;;;AAIG;;AA4EH;AACA;AACA,IAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;AACvC;AACA;AACA,IAAM,aAAa,GAAG,IAAI,GAAG,EAAoB,CAAC;AAElD,IAAM,uBAAuB,GAAG,IAAI,CAAC;AAOrC;;;;AAIG;AACH,IAAM,eAAe,GAA+C,UAClE,QAAsC,EACtC,SAAwC,EAAA;;;;;AAKxC,IAAA,IACE,MAAM,CAAC,QAAQ,KAAK,SAAS;AAC7B,SAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC5D;QACA,OAAO;KACR;;;;;IAOD,IAAM,IAAI,GAER,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAA;AACtB,QAAA,CAAA,MAAA,MAAM,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,MAAK,IAAI;AAC/B,UAAE,MAAM,CAAC,QAAS,CAAC,IAAI;UACrB,UAAC,IAAU,EAAA,EAAK,OAAA,IAAI,CAAA,EAAA,CAAC;IAE3B,IAAM,kBAAkB,GAAG,UAAC,IAAwB,EAAA;QAClD,OAAA,IAAI,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAA7C,KAA6C,CAAC;IAEhD,IAAM,WAAW,GAAG,UAAC,IAAY,EAAA;QAC/B,IAAI,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC,QAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAE,CAAC;SAC1C;AACD,QAAA,OAAO,QAAQ,CAAC;AAClB,KAAC,CAAC;AAEF,IAAA,IAAM,aAAa,GAAG,UAAC,IAAY,EAAE,QAA6B,EAAA;;AAEhE,QAAA,IAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,QAAA,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1C,IAAI,WAAW,EAAE;YACf,IAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;AAGxC,YAAA,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACrC;;AAED,QAAA,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;AAEvB,QAAA,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;;QAG3B,MAAM,CAAC,QAAS,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;;;;QAIvD,IAAI,WAAW,IAAI,MAAM,CAAC,QAAS,CAAC,YAAY,EAAE;;;YAGhD,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACtD,YAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,gBAAA,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACrC;SACF;AACH,KAAC,CAAC;AAEF,IAAA,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAGhC,CAAC;AAEJ;;;;AAIG;AACH,IAAA,IAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC;AACrD,IAAA,QAAQ,CAAC,aAAa,GAAG,UAAU,IAAY,EAAE,OAAuB,EAAA;AACtE,QAAA,IAAM,OAAO,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACpE,IAAM,KAAK,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,KAAK,CAAC;AAC7B,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,YAAA,IAAI,CAAC,MAAM,CAAC,QAAS,CAAC,YAAY,EAAE;gBAClC,MAAM,CAAC,QAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACrD;;;AAGD,YAAA,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;AAC7B,gBAAA,IAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;;gBAEpC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAC7C,OAAO,CACwB,CAAC;;;AAGlC,gBAAA,QAAQ,CAAC,IAAI,CAAb,KAAA,CAAA,QAAQ,EACH,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,EAAA;;oBAC9B,CAAA,EAAA,GAAA,KAAK,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,KAAK,CAAC,CAAC;oBACrC,OAAO,KAAK,CAAC,WAAY,CAAC;AAC5B,iBAAC,CAAC,CACF,CAAA;aACH;SACF;AACD,QAAA,OAAO,OAAO,CAAC;AACjB,KAAC,CAAC;AAEF,IAAA,IAAM,eAAe,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;IAC1D,IAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AAEzD,IAAA,IAAM,cAAc,GAAG,SAAS,CAAC,SAAS,CAAC;AAC3C;;AAEG;AACH,IAAA,IAAM,QAAQ,GAAG,cAAc,CAAC,UAAU,CAAC;AAC3C,IAAA,cAAc,CAAC,UAAU,GAAG,UAE1B,KAAc,EACd,eAAuC,EAAA;;AAAvC,QAAA,IAAA,eAAA,KAAA,KAAA,CAAA,EAAA,EAAA,eAAuC,GAAA,IAAA,CAAA,EAAA;QAEvC,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,UAAW,CAAC;QACrD,IAAM,KAAK,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;QAClC,IAAI,SAAS,YAAY,UAAU,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE;;;;;;;;;AAShE,YAAA,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC;AACnC,YAAA,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC;;AAG/B,YAAA,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC;AACzC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;;;YAItB,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;;;;YAK5C,IAAM,QAAQ,GAAG,CAAC,KAA6B,KAAA,IAAA,IAA7B,KAAK,KAAL,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,KAAK,CAA2B,YAAY,CAAC;AAC7D,kBAAG,IAAI,CAAC,gBAA8C,CAAC,UAAU,CAAC,EAAE;AACpE,kBAAE,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACvC,YAAA,aAAa,CAAC,KAAM,EAAE,QAAQ,CAAC,CAAC;;AAGhC,YAAA,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;;AAEnD,YAAA,IAAI,MAAA,MAAM,CAAC,QAAQ,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAY,EAAE;gBACjC,IAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACtD,gBAAA,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;iBACpD;aACF;AACD,YAAA,SAAS,CAAC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;;AAEjD,YAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;SAC1B;aAAM;YACL,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;SAC7C;AACH,KAAC,CAAC;AAEF;;;AAGG;AACH,IAAA,cAAc,CAAC,aAAa,GAAG,UAE7B,MAA2B,EAAA;;QAE3B,IAAM,KAAK,GAAG,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;QAClC,IAAI,aAAa,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnD,QAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,mBAAmB,CAAC,GAAG,CAAC,KAAK,GAAG,aAAa,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;SAC7D;QACD,IAAI,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjD,QAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,aAAa,CAAC,GAAG,CACf,MAAM,CAAC,OAAO,GACb,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,EAC/C,CAAC;SACH;AACD,QAAA,OAAO,QAAQ,CAAC;AAClB,KAAC,CAAC;AACJ,CAAC,CAAC;AAE2B;AAC3B,IAAA,eAAe,CAAC,gBAAgB,GAAG,uBAAuB,CAAC;AAC7D,CAAC;AAEa;IACZ,CAAA,EAAA,GAAA,UAAU,CAAC,6BAA6B,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,IAAxC,UAAU,CAAC,6BAA6B,GAAK,eAAe,CAAC,CAAA;AAC/D"}