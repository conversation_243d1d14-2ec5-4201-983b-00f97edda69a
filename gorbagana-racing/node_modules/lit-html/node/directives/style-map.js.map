{"version": 3, "file": "style-map.js", "sources": ["../../src/directives/style-map.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2018 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {AttributePart, noChange} from '../lit-html.js';\nimport {\n  directive,\n  Directive,\n  DirectiveParameters,\n  PartInfo,\n  PartType,\n} from '../directive.js';\n\n/**\n * A key-value set of CSS properties and values.\n *\n * The key should be either a valid CSS property name string, like\n * `'background-color'`, or a valid JavaScript camel case property name\n * for CSSStyleDeclaration like `backgroundColor`.\n */\nexport interface StyleInfo {\n  [name: string]: string | number | undefined | null;\n}\n\nconst important = 'important';\n// The leading space is important\nconst importantFlag = ' !' + important;\n// How many characters to remove from a value, as a negative number\nconst flagTrim = 0 - importantFlag.length;\n\nclass StyleMapDirective extends Directive {\n  private _previousStyleProperties?: Set<string>;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (\n      partInfo.type !== PartType.ATTRIBUTE ||\n      partInfo.name !== 'style' ||\n      (partInfo.strings?.length as number) > 2\n    ) {\n      throw new Error(\n        'The `styleMap` directive must be used in the `style` attribute ' +\n          'and must be the only part in the attribute.'\n      );\n    }\n  }\n\n  render(styleInfo: Readonly<StyleInfo>) {\n    return Object.keys(styleInfo).reduce((style, prop) => {\n      const value = styleInfo[prop];\n      if (value == null) {\n        return style;\n      }\n      // Convert property names from camel-case to dash-case, i.e.:\n      //  `backgroundColor` -> `background-color`\n      // Vendor-prefixed names need an extra `-` appended to front:\n      //  `webkitAppearance` -> `-webkit-appearance`\n      // Exception is any property name containing a dash, including\n      // custom properties; we assume these are already dash-cased i.e.:\n      //  `--my-button-color` --> `--my-button-color`\n      prop = prop.includes('-')\n        ? prop\n        : prop\n            .replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g, '-$&')\n            .toLowerCase();\n      return style + `${prop}:${value};`;\n    }, '');\n  }\n\n  override update(part: AttributePart, [styleInfo]: DirectiveParameters<this>) {\n    const {style} = part.element as HTMLElement;\n\n    if (this._previousStyleProperties === undefined) {\n      this._previousStyleProperties = new Set(Object.keys(styleInfo));\n      return this.render(styleInfo);\n    }\n\n    // Remove old properties that no longer exist in styleInfo\n    for (const name of this._previousStyleProperties) {\n      // If the name isn't in styleInfo or it's null/undefined\n      if (styleInfo[name] == null) {\n        this._previousStyleProperties!.delete(name);\n        if (name.includes('-')) {\n          style.removeProperty(name);\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = null;\n        }\n      }\n    }\n\n    // Add or update properties\n    for (const name in styleInfo) {\n      const value = styleInfo[name];\n      if (value != null) {\n        this._previousStyleProperties.add(name);\n        const isImportant =\n          typeof value === 'string' && value.endsWith(importantFlag);\n        if (name.includes('-') || isImportant) {\n          style.setProperty(\n            name,\n            isImportant\n              ? (value as string).slice(0, flagTrim)\n              : (value as string),\n            isImportant ? important : ''\n          );\n        } else {\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          (style as any)[name] = value;\n        }\n      }\n    }\n    return noChange;\n  }\n}\n\n/**\n * A directive that applies CSS properties to an element.\n *\n * `styleMap` can only be used in the `style` attribute and must be the only\n * expression in the attribute. It takes the property names in the\n * {@link StyleInfo styleInfo} object and adds the properties to the inline\n * style of the element.\n *\n * Property names with dashes (`-`) are assumed to be valid CSS\n * property names and set on the element's style object using `setProperty()`.\n * Names without dashes are assumed to be camelCased JavaScript property names\n * and set on the element's style object using property assignment, allowing the\n * style object to translate JavaScript-style names to CSS property names.\n *\n * For example `styleMap({backgroundColor: 'red', 'border-top': '5px', '--size':\n * '0'})` sets the `background-color`, `border-top` and `--size` properties.\n *\n * @param styleInfo\n * @see {@link https://lit.dev/docs/templates/directives/#stylemap styleMap code samples on Lit.dev}\n */\nexport const styleMap = directive(StyleMapDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {StyleMapDirective};\n"], "names": ["important", "importantFlag", "styleMap", "directive", "Directive", "constructor", "partInfo", "super", "type", "PartType", "ATTRIBUTE", "name", "strings", "length", "Error", "render", "styleInfo", "Object", "keys", "reduce", "style", "prop", "value", "includes", "replace", "toLowerCase", "update", "part", "element", "undefined", "this", "_previousStyleProperties", "Set", "delete", "removeProperty", "add", "isImportant", "endsWith", "setProperty", "slice", "noChange"], "mappings": ";;;;;GA0BA,MAAMA,EAAY,YAEZC,EAAgB,KAAOD,EA8GhBE,EAAWC,EA1GxB,cAAgCC,EAG9B,WAAAC,CAAYC,GAEV,GADAC,MAAMD,GAEJA,EAASE,OAASC,EAASC,WACT,UAAlBJ,EAASK,MACRL,EAASM,SAASC,OAAoB,EAEvC,MAAUC,MACR,6GAIL,CAED,MAAAC,CAAOC,GACL,OAAOC,OAAOC,KAAKF,GAAWG,QAAO,CAACC,EAAOC,KAC3C,MAAMC,EAAQN,EAAUK,GACxB,OAAa,MAATC,EACKF,EAcFA,EAAQ,GALfC,EAAOA,EAAKE,SAAS,KACjBF,EACAA,EACGG,QAAQ,oCAAqC,OAC7CC,iBACmBH,IAAQ,GACjC,GACJ,CAEQ,MAAAI,CAAOC,GAAsBX,IACpC,MAAMI,MAACA,GAASO,EAAKC,QAErB,QAAsCC,IAAlCC,KAAKC,GAEP,OADAD,KAAKC,GAA2B,IAAIC,IAAIf,OAAOC,KAAKF,IAC7Cc,KAAKf,OAAOC,GAIrB,IAAK,MAAML,KAAQmB,KAAKC,GAEC,MAAnBf,EAAUL,KACZmB,KAAKC,GAA0BE,OAAOtB,GAClCA,EAAKY,SAAS,KAChBH,EAAMc,eAAevB,GAGpBS,EAAcT,GAAQ,MAM7B,IAAK,MAAMA,KAAQK,EAAW,CAC5B,MAAMM,EAAQN,EAAUL,GACxB,GAAa,MAATW,EAAe,CACjBQ,KAAKC,GAAyBI,IAAIxB,GAClC,MAAMyB,EACa,iBAAVd,GAAsBA,EAAMe,SAASpC,GAC1CU,EAAKY,SAAS,MAAQa,EACxBhB,EAAMkB,YACJ3B,EACAyB,EACKd,EAAiBiB,MAAM,GA1EvB,IA2EAjB,EACLc,EAAcpC,EAAY,IAI3BoB,EAAcT,GAAQW,CAE1B,CACF,CACD,OAAOkB,CACR"}