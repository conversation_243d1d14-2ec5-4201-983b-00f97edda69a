{"version": 3, "file": "template-content.js", "sources": ["../../src/directives/template-content.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {noChange} from '../lit-html.js';\nimport {directive, Directive, PartInfo, PartType} from '../directive.js';\n\nclass TemplateContentDirective extends Directive {\n  private _previousTemplate?: HTMLTemplateElement;\n\n  constructor(partInfo: PartInfo) {\n    super(partInfo);\n    if (partInfo.type !== PartType.CHILD) {\n      throw new Error('templateContent can only be used in child bindings');\n    }\n  }\n\n  render(template: HTMLTemplateElement) {\n    if (this._previousTemplate === template) {\n      return noChange;\n    }\n    this._previousTemplate = template;\n    return document.importNode(template.content, true);\n  }\n}\n\n/**\n * Renders the content of a template element as HTML.\n *\n * Note, the template should be developer controlled and not user controlled.\n * Rendering a user-controlled template with this directive\n * could lead to cross-site-scripting vulnerabilities.\n */\nexport const templateContent = directive(TemplateContentDirective);\n\n/**\n * The type of the class that powers this directive. Necessary for naming the\n * directive's return type.\n */\nexport type {TemplateContentDirective};\n"], "names": ["templateContent", "directive", "Directive", "constructor", "partInfo", "super", "type", "PartType", "CHILD", "Error", "render", "template", "this", "_previousTemplate", "noChange", "document", "importNode", "content"], "mappings": ";;;;;SAmCaA,EAAkBC,EA1B/B,cAAuCC,EAGrC,WAAAC,CAAYC,GAEV,GADAC,MAAMD,GACFA,EAASE,OAASC,EAASC,MAC7B,MAAUC,MAAM,qDAEnB,CAED,MAAAC,CAAOC,GACL,OAAIC,KAAKC,KAAsBF,EACtBG,GAETF,KAAKC,GAAoBF,EAClBI,SAASC,WAAWL,EAASM,SAAS,GAC9C"}