{"version": 3, "file": "lit-html.js", "sources": ["../src/lit-html.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n// IMPORTANT: these imports must be type-only\nimport type {Directive, DirectiveResult, PartInfo} from './directive.js';\nimport type {TrustedHTML, TrustedTypesWindow} from 'trusted-types/lib/index.js';\n\nconst DEV_MODE = true;\nconst ENABLE_EXTRA_SECURITY_HOOKS = true;\nconst ENABLE_SHADYDOM_NOPATCH = true;\nconst NODE_MODE = false;\n\n// Allows minifiers to rename references to globalThis\nconst global = globalThis;\n\n/**\n * Contains types that are part of the unstable debug API.\n *\n * Everything in this API is not stable and may change or be removed in the future,\n * even on patch releases.\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport namespace LitUnstable {\n  /**\n   * When Lit is running in dev mode and `window.emitLitDebugLogEvents` is true,\n   * we will emit 'lit-debug' events to window, with live details about the update and render\n   * lifecycle. These can be useful for writing debug tooling and visualizations.\n   *\n   * Please be aware that running with window.emitLitDebugLogEvents has performance overhead,\n   * making certain operations that are normally very cheap (like a no-op render) much slower,\n   * because we must copy data and dispatch events.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  export namespace DebugLog {\n    export type Entry =\n      | TemplatePrep\n      | TemplateInstantiated\n      | TemplateInstantiatedAndUpdated\n      | TemplateUpdating\n      | BeginRender\n      | EndRender\n      | CommitPartEntry\n      | SetPartValue;\n    export interface TemplatePrep {\n      kind: 'template prep';\n      template: Template;\n      strings: TemplateStringsArray;\n      clonableTemplate: HTMLTemplateElement;\n      parts: TemplatePart[];\n    }\n    export interface BeginRender {\n      kind: 'begin render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart | undefined;\n    }\n    export interface EndRender {\n      kind: 'end render';\n      id: number;\n      value: unknown;\n      container: HTMLElement | DocumentFragment;\n      options: RenderOptions | undefined;\n      part: ChildPart;\n    }\n    export interface TemplateInstantiated {\n      kind: 'template instantiated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateInstantiatedAndUpdated {\n      kind: 'template instantiated and updated';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      fragment: Node;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface TemplateUpdating {\n      kind: 'template updating';\n      template: Template | CompiledTemplate;\n      instance: TemplateInstance;\n      options: RenderOptions | undefined;\n      parts: Array<Part | undefined>;\n      values: unknown[];\n    }\n    export interface SetPartValue {\n      kind: 'set part';\n      part: Part;\n      value: unknown;\n      valueIndex: number;\n      values: unknown[];\n      templateInstance: TemplateInstance;\n    }\n\n    export type CommitPartEntry =\n      | CommitNothingToChildEntry\n      | CommitText\n      | CommitNode\n      | CommitAttribute\n      | CommitProperty\n      | CommitBooleanAttribute\n      | CommitEventListener\n      | CommitToElementBinding;\n\n    export interface CommitNothingToChildEntry {\n      kind: 'commit nothing to child';\n      start: ChildNode;\n      end: ChildNode | null;\n      parent: Disconnectable | undefined;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitText {\n      kind: 'commit text';\n      node: Text;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitNode {\n      kind: 'commit node';\n      start: Node;\n      parent: Disconnectable | undefined;\n      value: Node;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitAttribute {\n      kind: 'commit attribute';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitProperty {\n      kind: 'commit property';\n      element: Element;\n      name: string;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitBooleanAttribute {\n      kind: 'commit boolean attribute';\n      element: Element;\n      name: string;\n      value: boolean;\n      options: RenderOptions | undefined;\n    }\n\n    export interface CommitEventListener {\n      kind: 'commit event listener';\n      element: Element;\n      name: string;\n      value: unknown;\n      oldListener: unknown;\n      options: RenderOptions | undefined;\n      // True if we're removing the old event listener (e.g. because settings changed, or value is nothing)\n      removeListener: boolean;\n      // True if we're adding a new event listener (e.g. because first render, or settings changed)\n      addListener: boolean;\n    }\n\n    export interface CommitToElementBinding {\n      kind: 'commit to element binding';\n      element: Element;\n      value: unknown;\n      options: RenderOptions | undefined;\n    }\n  }\n}\n\ninterface DebugLoggingWindow {\n  // Even in dev mode, we generally don't want to emit these events, as that's\n  // another level of cost, so only emit them when DEV_MODE is true _and_ when\n  // window.emitLitDebugEvents is true.\n  emitLitDebugLogEvents?: boolean;\n}\n\n/**\n * Useful for visualizing and logging insights into what the Lit template system is doing.\n *\n * Compiled out of prod mode builds.\n */\nconst debugLogEvent = DEV_MODE\n  ? (event: LitUnstable.DebugLog.Entry) => {\n      const shouldEmit = (global as unknown as DebugLoggingWindow)\n        .emitLitDebugLogEvents;\n      if (!shouldEmit) {\n        return;\n      }\n      global.dispatchEvent(\n        new CustomEvent<LitUnstable.DebugLog.Entry>('lit-debug', {\n          detail: event,\n        })\n      );\n    }\n  : undefined;\n// Used for connecting beginRender and endRender events when there are nested\n// renders when errors are thrown preventing an endRender event from being\n// called.\nlet debugLogRenderId = 0;\n\nlet issueWarning: (code: string, warning: string) => void;\n\nif (DEV_MODE) {\n  global.litIssuedWarnings ??= new Set();\n\n  /**\n   * Issue a warning if we haven't already, based either on `code` or `warning`.\n   * Warnings are disabled automatically only by `warning`; disabling via `code`\n   * can be done by users.\n   */\n  issueWarning = (code: string, warning: string) => {\n    warning += code\n      ? ` See https://lit.dev/msg/${code} for more information.`\n      : '';\n    if (\n      !global.litIssuedWarnings!.has(warning) &&\n      !global.litIssuedWarnings!.has(code)\n    ) {\n      console.warn(warning);\n      global.litIssuedWarnings!.add(warning);\n    }\n  };\n\n  queueMicrotask(() => {\n    issueWarning(\n      'dev-mode',\n      `Lit is in dev mode. Not recommended for production!`\n    );\n  });\n}\n\nconst wrap =\n  ENABLE_SHADYDOM_NOPATCH &&\n  global.ShadyDOM?.inUse &&\n  global.ShadyDOM?.noPatch === true\n    ? (global.ShadyDOM!.wrap as <T extends Node>(node: T) => T)\n    : <T extends Node>(node: T) => node;\n\nconst trustedTypes = (global as unknown as TrustedTypesWindow).trustedTypes;\n\n/**\n * Our TrustedTypePolicy for HTML which is declared using the html template\n * tag function.\n *\n * That HTML is a developer-authored constant, and is parsed with innerHTML\n * before any untrusted expressions have been mixed in. Therefor it is\n * considered safe by construction.\n */\nconst policy = trustedTypes\n  ? trustedTypes.createPolicy('lit-html', {\n      createHTML: (s) => s,\n    })\n  : undefined;\n\n/**\n * Used to sanitize any value before it is written into the DOM. This can be\n * used to implement a security policy of allowed and disallowed values in\n * order to prevent XSS attacks.\n *\n * One way of using this callback would be to check attributes and properties\n * against a list of high risk fields, and require that values written to such\n * fields be instances of a class which is safe by construction. Closure's Safe\n * HTML Types is one implementation of this technique (\n * https://github.com/google/safe-html-types/blob/master/doc/safehtml-types.md).\n * The TrustedTypes polyfill in API-only mode could also be used as a basis\n * for this technique (https://github.com/WICG/trusted-types).\n *\n * @param node The HTML node (usually either a #text node or an Element) that\n *     is being written to. Note that this is just an exemplar node, the write\n *     may take place against another instance of the same class of node.\n * @param name The name of an attribute or property (for example, 'href').\n * @param type Indicates whether the write that's about to be performed will\n *     be to a property or a node.\n * @return A function that will sanitize this class of writes.\n */\nexport type SanitizerFactory = (\n  node: Node,\n  name: string,\n  type: 'property' | 'attribute'\n) => ValueSanitizer;\n\n/**\n * A function which can sanitize values that will be written to a specific kind\n * of DOM sink.\n *\n * See SanitizerFactory.\n *\n * @param value The value to sanitize. Will be the actual value passed into\n *     the lit-html template literal, so this could be of any type.\n * @return The value to write to the DOM. Usually the same as the input value,\n *     unless sanitization is needed.\n */\nexport type ValueSanitizer = (value: unknown) => unknown;\n\nconst identityFunction: ValueSanitizer = (value: unknown) => value;\nconst noopSanitizer: SanitizerFactory = (\n  _node: Node,\n  _name: string,\n  _type: 'property' | 'attribute'\n) => identityFunction;\n\n/** Sets the global sanitizer factory. */\nconst setSanitizer = (newSanitizer: SanitizerFactory) => {\n  if (!ENABLE_EXTRA_SECURITY_HOOKS) {\n    return;\n  }\n  if (sanitizerFactoryInternal !== noopSanitizer) {\n    throw new Error(\n      `Attempted to overwrite existing lit-html security policy.` +\n        ` setSanitizeDOMValueFactory should be called at most once.`\n    );\n  }\n  sanitizerFactoryInternal = newSanitizer;\n};\n\n/**\n * Only used in internal tests, not a part of the public API.\n */\nconst _testOnlyClearSanitizerFactoryDoNotCallOrElse = () => {\n  sanitizerFactoryInternal = noopSanitizer;\n};\n\nconst createSanitizer: SanitizerFactory = (node, name, type) => {\n  return sanitizerFactoryInternal(node, name, type);\n};\n\n// Added to an attribute name to mark the attribute as bound so we can find\n// it easily.\nconst boundAttributeSuffix = '$lit$';\n\n// This marker is used in many syntactic positions in HTML, so it must be\n// a valid element name and attribute name. We don't support dynamic names (yet)\n// but this at least ensures that the parse tree is closer to the template\n// intention.\nconst marker = `lit$${Math.random().toFixed(9).slice(2)}$`;\n\n// String used to tell if a comment is a marker comment\nconst markerMatch = '?' + marker;\n\n// Text used to insert a comment marker node. We use processing instruction\n// syntax because it's slightly smaller, but parses as a comment node.\nconst nodeMarker = `<${markerMatch}>`;\n\nconst d =\n  NODE_MODE && global.document === undefined\n    ? ({\n        createTreeWalker() {\n          return {};\n        },\n      } as unknown as Document)\n    : document;\n\n// Creates a dynamic marker. We never have to search for these in the DOM.\nconst createMarker = () => d.createComment('');\n\n// https://tc39.github.io/ecma262/#sec-typeof-operator\ntype Primitive = null | undefined | boolean | number | string | symbol | bigint;\nconst isPrimitive = (value: unknown): value is Primitive =>\n  value === null || (typeof value != 'object' && typeof value != 'function');\nconst isArray = Array.isArray;\nconst isIterable = (value: unknown): value is Iterable<unknown> =>\n  isArray(value) ||\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  typeof (value as any)?.[Symbol.iterator] === 'function';\n\nconst SPACE_CHAR = `[ \\t\\n\\f\\r]`;\nconst ATTR_VALUE_CHAR = `[^ \\t\\n\\f\\r\"'\\`<>=]`;\nconst NAME_CHAR = `[^\\\\s\"'>=/]`;\n\n// These regexes represent the five parsing states that we care about in the\n// Template's HTML scanner. They match the *end* of the state they're named\n// after.\n// Depending on the match, we transition to a new state. If there's no match,\n// we stay in the same state.\n// Note that the regexes are stateful. We utilize lastIndex and sync it\n// across the multiple regexes used. In addition to the five regexes below\n// we also dynamically create a regex to find the matching end tags for raw\n// text elements.\n\n/**\n * End of text is: `<` followed by:\n *   (comment start) or (tag) or (dynamic tag binding)\n */\nconst textEndRegex = /<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g;\nconst COMMENT_START = 1;\nconst TAG_NAME = 2;\nconst DYNAMIC_TAG_NAME = 3;\n\nconst commentEndRegex = /-->/g;\n/**\n * Comments not started with <!--, like </{, can be ended by a single `>`\n */\nconst comment2EndRegex = />/g;\n\n/**\n * The tagEnd regex matches the end of the \"inside an opening\" tag syntax\n * position. It either matches a `>`, an attribute-like sequence, or the end\n * of the string after a space (attribute-name position ending).\n *\n * See attributes in the HTML spec:\n * https://www.w3.org/TR/html5/syntax.html#elements-attributes\n *\n * \" \\t\\n\\f\\r\" are HTML space characters:\n * https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * So an attribute is:\n *  * The name: any character except a whitespace character, (\"), ('), \">\",\n *    \"=\", or \"/\". Note: this is different from the HTML spec which also excludes control characters.\n *  * Followed by zero or more space characters\n *  * Followed by \"=\"\n *  * Followed by zero or more space characters\n *  * Followed by:\n *    * Any character except space, ('), (\"), \"<\", \">\", \"=\", (`), or\n *    * (\") then any non-(\"), or\n *    * (') then any non-(')\n */\nconst tagEndRegex = new RegExp(\n  `>|${SPACE_CHAR}(?:(${NAME_CHAR}+)(${SPACE_CHAR}*=${SPACE_CHAR}*(?:${ATTR_VALUE_CHAR}|(\"|')|))|$)`,\n  'g'\n);\nconst ENTIRE_MATCH = 0;\nconst ATTRIBUTE_NAME = 1;\nconst SPACES_AND_EQUALS = 2;\nconst QUOTE_CHAR = 3;\n\nconst singleQuoteAttrEndRegex = /'/g;\nconst doubleQuoteAttrEndRegex = /\"/g;\n/**\n * Matches the raw text elements.\n *\n * Comments are not parsed within raw text elements, so we need to search their\n * text content for marker strings.\n */\nconst rawTextElement = /^(?:script|style|textarea|title)$/i;\n\n/** TemplateResult types */\nconst HTML_RESULT = 1;\nconst SVG_RESULT = 2;\nconst MATHML_RESULT = 3;\n\ntype ResultType = typeof HTML_RESULT | typeof SVG_RESULT | typeof MATHML_RESULT;\n\n// TemplatePart types\n// IMPORTANT: these must match the values in PartType\nconst ATTRIBUTE_PART = 1;\nconst CHILD_PART = 2;\nconst PROPERTY_PART = 3;\nconst BOOLEAN_ATTRIBUTE_PART = 4;\nconst EVENT_PART = 5;\nconst ELEMENT_PART = 6;\nconst COMMENT_PART = 7;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg} when it hasn't been compiled by @lit-labs/compiler.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n */\nexport type UncompiledTemplateResult<T extends ResultType = ResultType> = {\n  // This property needs to remain unminified.\n  ['_$litType$']: T;\n  strings: TemplateStringsArray;\n  values: unknown[];\n};\n\n/**\n * This is a template result that may be either uncompiled or compiled.\n *\n * In the future, TemplateResult will be this type. If you want to explicitly\n * note that a template result is potentially compiled, you can reference this\n * type and it will continue to behave the same through the next major version\n * of Lit. This can be useful for code that wants to prepare for the next\n * major version of Lit.\n */\nexport type MaybeCompiledTemplateResult<T extends ResultType = ResultType> =\n  | UncompiledTemplateResult<T>\n  | CompiledTemplateResult;\n\n/**\n * The return type of the template tag functions, {@linkcode html} and\n * {@linkcode svg}.\n *\n * A `TemplateResult` object holds all the information about a template\n * expression required to render it: the template strings, expression values,\n * and type of template (html or svg).\n *\n * `TemplateResult` objects do not create any DOM on their own. To create or\n * update DOM you need to render the `TemplateResult`. See\n * [Rendering](https://lit.dev/docs/components/rendering) for more information.\n *\n * In Lit 4, this type will be an alias of\n * MaybeCompiledTemplateResult, so that code will get type errors if it assumes\n * that Lit templates are not compiled. When deliberately working with only\n * one, use either {@linkcode CompiledTemplateResult} or\n * {@linkcode UncompiledTemplateResult} explicitly.\n */\nexport type TemplateResult<T extends ResultType = ResultType> =\n  UncompiledTemplateResult<T>;\n\nexport type HTMLTemplateResult = TemplateResult<typeof HTML_RESULT>;\n\nexport type SVGTemplateResult = TemplateResult<typeof SVG_RESULT>;\n\nexport type MathMLTemplateResult = TemplateResult<typeof MATHML_RESULT>;\n\n/**\n * A TemplateResult that has been compiled by @lit-labs/compiler, skipping the\n * prepare step.\n */\nexport interface CompiledTemplateResult {\n  // This is a factory in order to make template initialization lazy\n  // and allow ShadyRenderOptions scope to be passed in.\n  // This property needs to remain unminified.\n  ['_$litType$']: CompiledTemplate;\n  values: unknown[];\n}\n\nexport interface CompiledTemplate extends Omit<Template, 'el'> {\n  // el is overridden to be optional. We initialize it on first render\n  el?: HTMLTemplateElement;\n\n  // The prepared HTML string to create a template element from.\n  // The type is a TemplateStringsArray to guarantee that the value came from\n  // source code, preventing a JSON injection attack.\n  h: TemplateStringsArray;\n}\n\n/**\n * Generates a template literal tag function that returns a TemplateResult with\n * the given result type.\n */\nconst tag =\n  <T extends ResultType>(type: T) =>\n  (strings: TemplateStringsArray, ...values: unknown[]): TemplateResult<T> => {\n    // Warn against templates octal escape sequences\n    // We do this here rather than in render so that the warning is closer to the\n    // template definition.\n    if (DEV_MODE && strings.some((s) => s === undefined)) {\n      console.warn(\n        'Some template strings are undefined.\\n' +\n          'This is probably caused by illegal octal escape sequences.'\n      );\n    }\n    if (DEV_MODE) {\n      // Import static-html.js results in a circular dependency which g3 doesn't\n      // handle. Instead we know that static values must have the field\n      // `_$litStatic$`.\n      if (\n        values.some((val) => (val as {_$litStatic$: unknown})?.['_$litStatic$'])\n      ) {\n        issueWarning(\n          '',\n          `Static values 'literal' or 'unsafeStatic' cannot be used as values to non-static templates.\\n` +\n            `Please use the static 'html' tag function. See https://lit.dev/docs/templates/expressions/#static-expressions`\n        );\n      }\n    }\n    return {\n      // This property needs to remain unminified.\n      ['_$litType$']: type,\n      strings,\n      values,\n    };\n  };\n\n/**\n * Interprets a template literal as an HTML template that can efficiently\n * render to and update a container.\n *\n * ```ts\n * const header = (title: string) => html`<h1>${title}</h1>`;\n * ```\n *\n * The `html` tag returns a description of the DOM to render as a value. It is\n * lazy, meaning no work is done until the template is rendered. When rendering,\n * if a template comes from the same expression as a previously rendered result,\n * it's efficiently updated instead of replaced.\n */\nexport const html = tag(HTML_RESULT);\n\n/**\n * Interprets a template literal as an SVG fragment that can efficiently render\n * to and update a container.\n *\n * ```ts\n * const rect = svg`<rect width=\"10\" height=\"10\"></rect>`;\n *\n * const myImage = html`\n *   <svg viewBox=\"0 0 10 10\" xmlns=\"http://www.w3.org/2000/svg\">\n *     ${rect}\n *   </svg>`;\n * ```\n *\n * The `svg` *tag function* should only be used for SVG fragments, or elements\n * that would be contained **inside** an `<svg>` HTML element. A common error is\n * placing an `<svg>` *element* in a template tagged with the `svg` tag\n * function. The `<svg>` element is an HTML element and should be used within a\n * template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an SVG fragment from the\n * `render()` method, as the SVG fragment will be contained within the element's\n * shadow root and thus not be properly contained within an `<svg>` HTML\n * element.\n */\nexport const svg = tag(SVG_RESULT);\n\n/**\n * Interprets a template literal as MathML fragment that can efficiently render\n * to and update a container.\n *\n * ```ts\n * const num = mathml`<mn>1</mn>`;\n *\n * const eq = html`\n *   <math>\n *     ${num}\n *   </math>`;\n * ```\n *\n * The `mathml` *tag function* should only be used for MathML fragments, or\n * elements that would be contained **inside** a `<math>` HTML element. A common\n * error is placing a `<math>` *element* in a template tagged with the `mathml`\n * tag function. The `<math>` element is an HTML element and should be used\n * within a template tagged with the {@linkcode html} tag function.\n *\n * In LitElement usage, it's invalid to return an MathML fragment from the\n * `render()` method, as the MathML fragment will be contained within the\n * element's shadow root and thus not be properly contained within a `<math>`\n * HTML element.\n */\nexport const mathml = tag(MATHML_RESULT);\n\n/**\n * A sentinel value that signals that a value was handled by a directive and\n * should not be written to the DOM.\n */\nexport const noChange = Symbol.for('lit-noChange');\n\n/**\n * A sentinel value that signals a ChildPart to fully clear its content.\n *\n * ```ts\n * const button = html`${\n *  user.isAdmin\n *    ? html`<button>DELETE</button>`\n *    : nothing\n * }`;\n * ```\n *\n * Prefer using `nothing` over other falsy values as it provides a consistent\n * behavior between various expression binding contexts.\n *\n * In child expressions, `undefined`, `null`, `''`, and `nothing` all behave the\n * same and render no nodes. In attribute expressions, `nothing` _removes_ the\n * attribute, while `undefined` and `null` will render an empty string. In\n * property expressions `nothing` becomes `undefined`.\n */\nexport const nothing = Symbol.for('lit-nothing');\n\n/**\n * The cache of prepared templates, keyed by the tagged TemplateStringsArray\n * and _not_ accounting for the specific template tag used. This means that\n * template tags cannot be dynamic - they must statically be one of html, svg,\n * or attr. This restriction simplifies the cache lookup, which is on the hot\n * path for rendering.\n */\nconst templateCache = new WeakMap<TemplateStringsArray, Template>();\n\n/**\n * Object specifying options for controlling lit-html rendering. Note that\n * while `render` may be called multiple times on the same `container` (and\n * `renderBefore` reference node) to efficiently update the rendered content,\n * only the options passed in during the first render are respected during\n * the lifetime of renders to that unique `container` + `renderBefore`\n * combination.\n */\nexport interface RenderOptions {\n  /**\n   * An object to use as the `this` value for event listeners. It's often\n   * useful to set this to the host component rendering a template.\n   */\n  host?: object;\n  /**\n   * A DOM node before which to render content in the container.\n   */\n  renderBefore?: ChildNode | null;\n  /**\n   * Node used for cloning the template (`importNode` will be called on this\n   * node). This controls the `ownerDocument` of the rendered DOM, along with\n   * any inherited context. Defaults to the global `document`.\n   */\n  creationScope?: {importNode(node: Node, deep?: boolean): Node};\n  /**\n   * The initial connected state for the top-level part being rendered. If no\n   * `isConnected` option is set, `AsyncDirective`s will be connected by\n   * default. Set to `false` if the initial render occurs in a disconnected tree\n   * and `AsyncDirective`s should see `isConnected === false` for their initial\n   * render. The `part.setConnected()` method must be used subsequent to initial\n   * render to change the connected state of the part.\n   */\n  isConnected?: boolean;\n}\n\nconst walker = d.createTreeWalker(\n  d,\n  129 /* NodeFilter.SHOW_{ELEMENT|COMMENT} */\n);\n\nlet sanitizerFactoryInternal: SanitizerFactory = noopSanitizer;\n\n//\n// Classes only below here, const variable declarations only above here...\n//\n// Keeping variable declarations and classes together improves minification.\n// Interfaces and type aliases can be interleaved freely.\n//\n\n// Type for classes that have a `_directive` or `_directives[]` field, used by\n// `resolveDirective`\nexport interface DirectiveParent {\n  _$parent?: DirectiveParent;\n  _$isConnected: boolean;\n  __directive?: Directive;\n  __directives?: Array<Directive | undefined>;\n}\n\nfunction trustFromTemplateString(\n  tsa: TemplateStringsArray,\n  stringFromTSA: string\n): TrustedHTML {\n  // A security check to prevent spoofing of Lit template results.\n  // In the future, we may be able to replace this with Array.isTemplateObject,\n  // though we might need to make that check inside of the html and svg\n  // functions, because precompiled templates don't come in as\n  // TemplateStringArray objects.\n  if (!isArray(tsa) || !tsa.hasOwnProperty('raw')) {\n    let message = 'invalid template strings array';\n    if (DEV_MODE) {\n      message = `\n          Internal Error: expected template strings to be an array\n          with a 'raw' field. Faking a template strings array by\n          calling html or svg like an ordinary function is effectively\n          the same as calling unsafeHtml and can lead to major security\n          issues, e.g. opening your code up to XSS attacks.\n          If you're using the html or svg tagged template functions normally\n          and still seeing this error, please file a bug at\n          https://github.com/lit/lit/issues/new?template=bug_report.md\n          and include information about your build tooling, if any.\n        `\n        .trim()\n        .replace(/\\n */g, '\\n');\n    }\n    throw new Error(message);\n  }\n  return policy !== undefined\n    ? policy.createHTML(stringFromTSA)\n    : (stringFromTSA as unknown as TrustedHTML);\n}\n\n/**\n * Returns an HTML string for the given TemplateStringsArray and result type\n * (HTML or SVG), along with the case-sensitive bound attribute names in\n * template order. The HTML contains comment markers denoting the `ChildPart`s\n * and suffixes on bound attributes denoting the `AttributeParts`.\n *\n * @param strings template strings array\n * @param type HTML or SVG\n * @return Array containing `[html, attrNames]` (array returned for terseness,\n *     to avoid object fields since this code is shared with non-minified SSR\n *     code)\n */\nconst getTemplateHtml = (\n  strings: TemplateStringsArray,\n  type: ResultType\n): [TrustedHTML, Array<string>] => {\n  // Insert makers into the template HTML to represent the position of\n  // bindings. The following code scans the template strings to determine the\n  // syntactic position of the bindings. They can be in text position, where\n  // we insert an HTML comment, attribute value position, where we insert a\n  // sentinel string and re-write the attribute name, or inside a tag where\n  // we insert the sentinel string.\n  const l = strings.length - 1;\n  // Stores the case-sensitive bound attribute names in the order of their\n  // parts. ElementParts are also reflected in this array as undefined\n  // rather than a string, to disambiguate from attribute bindings.\n  const attrNames: Array<string> = [];\n  let html =\n    type === SVG_RESULT ? '<svg>' : type === MATHML_RESULT ? '<math>' : '';\n\n  // When we're inside a raw text tag (not it's text content), the regex\n  // will still be tagRegex so we can find attributes, but will switch to\n  // this regex when the tag ends.\n  let rawTextEndRegex: RegExp | undefined;\n\n  // The current parsing state, represented as a reference to one of the\n  // regexes\n  let regex = textEndRegex;\n\n  for (let i = 0; i < l; i++) {\n    const s = strings[i];\n    // The index of the end of the last attribute name. When this is\n    // positive at end of a string, it means we're in an attribute value\n    // position and need to rewrite the attribute name.\n    // We also use a special value of -2 to indicate that we encountered\n    // the end of a string in attribute name position.\n    let attrNameEndIndex = -1;\n    let attrName: string | undefined;\n    let lastIndex = 0;\n    let match!: RegExpExecArray | null;\n\n    // The conditions in this loop handle the current parse state, and the\n    // assignments to the `regex` variable are the state transitions.\n    while (lastIndex < s.length) {\n      // Make sure we start searching from where we previously left off\n      regex.lastIndex = lastIndex;\n      match = regex.exec(s);\n      if (match === null) {\n        break;\n      }\n      lastIndex = regex.lastIndex;\n      if (regex === textEndRegex) {\n        if (match[COMMENT_START] === '!--') {\n          regex = commentEndRegex;\n        } else if (match[COMMENT_START] !== undefined) {\n          // We started a weird comment, like </{\n          regex = comment2EndRegex;\n        } else if (match[TAG_NAME] !== undefined) {\n          if (rawTextElement.test(match[TAG_NAME])) {\n            // Record if we encounter a raw-text element. We'll switch to\n            // this regex at the end of the tag.\n            rawTextEndRegex = new RegExp(`</${match[TAG_NAME]}`, 'g');\n          }\n          regex = tagEndRegex;\n        } else if (match[DYNAMIC_TAG_NAME] !== undefined) {\n          if (DEV_MODE) {\n            throw new Error(\n              'Bindings in tag names are not supported. Please use static templates instead. ' +\n                'See https://lit.dev/docs/templates/expressions/#static-expressions'\n            );\n          }\n          regex = tagEndRegex;\n        }\n      } else if (regex === tagEndRegex) {\n        if (match[ENTIRE_MATCH] === '>') {\n          // End of a tag. If we had started a raw-text element, use that\n          // regex\n          regex = rawTextEndRegex ?? textEndRegex;\n          // We may be ending an unquoted attribute value, so make sure we\n          // clear any pending attrNameEndIndex\n          attrNameEndIndex = -1;\n        } else if (match[ATTRIBUTE_NAME] === undefined) {\n          // Attribute name position\n          attrNameEndIndex = -2;\n        } else {\n          attrNameEndIndex = regex.lastIndex - match[SPACES_AND_EQUALS].length;\n          attrName = match[ATTRIBUTE_NAME];\n          regex =\n            match[QUOTE_CHAR] === undefined\n              ? tagEndRegex\n              : match[QUOTE_CHAR] === '\"'\n                ? doubleQuoteAttrEndRegex\n                : singleQuoteAttrEndRegex;\n        }\n      } else if (\n        regex === doubleQuoteAttrEndRegex ||\n        regex === singleQuoteAttrEndRegex\n      ) {\n        regex = tagEndRegex;\n      } else if (regex === commentEndRegex || regex === comment2EndRegex) {\n        regex = textEndRegex;\n      } else {\n        // Not one of the five state regexes, so it must be the dynamically\n        // created raw text regex and we're at the close of that element.\n        regex = tagEndRegex;\n        rawTextEndRegex = undefined;\n      }\n    }\n\n    if (DEV_MODE) {\n      // If we have a attrNameEndIndex, which indicates that we should\n      // rewrite the attribute name, assert that we're in a valid attribute\n      // position - either in a tag, or a quoted attribute value.\n      console.assert(\n        attrNameEndIndex === -1 ||\n          regex === tagEndRegex ||\n          regex === singleQuoteAttrEndRegex ||\n          regex === doubleQuoteAttrEndRegex,\n        'unexpected parse state B'\n      );\n    }\n\n    // We have four cases:\n    //  1. We're in text position, and not in a raw text element\n    //     (regex === textEndRegex): insert a comment marker.\n    //  2. We have a non-negative attrNameEndIndex which means we need to\n    //     rewrite the attribute name to add a bound attribute suffix.\n    //  3. We're at the non-first binding in a multi-binding attribute, use a\n    //     plain marker.\n    //  4. We're somewhere else inside the tag. If we're in attribute name\n    //     position (attrNameEndIndex === -2), add a sequential suffix to\n    //     generate a unique attribute name.\n\n    // Detect a binding next to self-closing tag end and insert a space to\n    // separate the marker from the tag end:\n    const end =\n      regex === tagEndRegex && strings[i + 1].startsWith('/>') ? ' ' : '';\n    html +=\n      regex === textEndRegex\n        ? s + nodeMarker\n        : attrNameEndIndex >= 0\n          ? (attrNames.push(attrName!),\n            s.slice(0, attrNameEndIndex) +\n              boundAttributeSuffix +\n              s.slice(attrNameEndIndex)) +\n            marker +\n            end\n          : s + marker + (attrNameEndIndex === -2 ? i : end);\n  }\n\n  const htmlResult: string | TrustedHTML =\n    html +\n    (strings[l] || '<?>') +\n    (type === SVG_RESULT ? '</svg>' : type === MATHML_RESULT ? '</math>' : '');\n\n  // Returned as an array for terseness\n  return [trustFromTemplateString(strings, htmlResult), attrNames];\n};\n\n/** @internal */\nexport type {Template};\nclass Template {\n  /** @internal */\n  el!: HTMLTemplateElement;\n\n  parts: Array<TemplatePart> = [];\n\n  constructor(\n    // This property needs to remain unminified.\n    {strings, ['_$litType$']: type}: UncompiledTemplateResult,\n    options?: RenderOptions\n  ) {\n    let node: Node | null;\n    let nodeIndex = 0;\n    let attrNameIndex = 0;\n    const partCount = strings.length - 1;\n    const parts = this.parts;\n\n    // Create template element\n    const [html, attrNames] = getTemplateHtml(strings, type);\n    this.el = Template.createElement(html, options);\n    walker.currentNode = this.el.content;\n\n    // Re-parent SVG or MathML nodes into template root\n    if (type === SVG_RESULT || type === MATHML_RESULT) {\n      const wrapper = this.el.content.firstChild!;\n      wrapper.replaceWith(...wrapper.childNodes);\n    }\n\n    // Walk the template to find binding markers and create TemplateParts\n    while ((node = walker.nextNode()) !== null && parts.length < partCount) {\n      if (node.nodeType === 1) {\n        if (DEV_MODE) {\n          const tag = (node as Element).localName;\n          // Warn if `textarea` includes an expression and throw if `template`\n          // does since these are not supported. We do this by checking\n          // innerHTML for anything that looks like a marker. This catches\n          // cases like bindings in textarea there markers turn into text nodes.\n          if (\n            /^(?:textarea|template)$/i!.test(tag) &&\n            (node as Element).innerHTML.includes(marker)\n          ) {\n            const m =\n              `Expressions are not supported inside \\`${tag}\\` ` +\n              `elements. See https://lit.dev/msg/expression-in-${tag} for more ` +\n              `information.`;\n            if (tag === 'template') {\n              throw new Error(m);\n            } else issueWarning('', m);\n          }\n        }\n        // TODO (justinfagnani): for attempted dynamic tag names, we don't\n        // increment the bindingIndex, and it'll be off by 1 in the element\n        // and off by two after it.\n        if ((node as Element).hasAttributes()) {\n          for (const name of (node as Element).getAttributeNames()) {\n            if (name.endsWith(boundAttributeSuffix)) {\n              const realName = attrNames[attrNameIndex++];\n              const value = (node as Element).getAttribute(name)!;\n              const statics = value.split(marker);\n              const m = /([.?@])?(.*)/.exec(realName)!;\n              parts.push({\n                type: ATTRIBUTE_PART,\n                index: nodeIndex,\n                name: m[2],\n                strings: statics,\n                ctor:\n                  m[1] === '.'\n                    ? PropertyPart\n                    : m[1] === '?'\n                      ? BooleanAttributePart\n                      : m[1] === '@'\n                        ? EventPart\n                        : AttributePart,\n              });\n              (node as Element).removeAttribute(name);\n            } else if (name.startsWith(marker)) {\n              parts.push({\n                type: ELEMENT_PART,\n                index: nodeIndex,\n              });\n              (node as Element).removeAttribute(name);\n            }\n          }\n        }\n        // TODO (justinfagnani): benchmark the regex against testing for each\n        // of the 3 raw text element names.\n        if (rawTextElement.test((node as Element).tagName)) {\n          // For raw text elements we need to split the text content on\n          // markers, create a Text node for each segment, and create\n          // a TemplatePart for each marker.\n          const strings = (node as Element).textContent!.split(marker);\n          const lastIndex = strings.length - 1;\n          if (lastIndex > 0) {\n            (node as Element).textContent = trustedTypes\n              ? (trustedTypes.emptyScript as unknown as '')\n              : '';\n            // Generate a new text node for each literal section\n            // These nodes are also used as the markers for child parts\n            for (let i = 0; i < lastIndex; i++) {\n              (node as Element).append(strings[i], createMarker());\n              // Walk past the marker node we just added\n              walker.nextNode();\n              parts.push({type: CHILD_PART, index: ++nodeIndex});\n            }\n            // Note because this marker is added after the walker's current\n            // node, it will be walked to in the outer loop (and ignored), so\n            // we don't need to adjust nodeIndex here\n            (node as Element).append(strings[lastIndex], createMarker());\n          }\n        }\n      } else if (node.nodeType === 8) {\n        const data = (node as Comment).data;\n        if (data === markerMatch) {\n          parts.push({type: CHILD_PART, index: nodeIndex});\n        } else {\n          let i = -1;\n          while ((i = (node as Comment).data.indexOf(marker, i + 1)) !== -1) {\n            // Comment node has a binding marker inside, make an inactive part\n            // The binding won't work, but subsequent bindings will\n            parts.push({type: COMMENT_PART, index: nodeIndex});\n            // Move to the end of the match\n            i += marker.length - 1;\n          }\n        }\n      }\n      nodeIndex++;\n    }\n\n    if (DEV_MODE) {\n      // If there was a duplicate attribute on a tag, then when the tag is\n      // parsed into an element the attribute gets de-duplicated. We can detect\n      // this mismatch if we haven't precisely consumed every attribute name\n      // when preparing the template. This works because `attrNames` is built\n      // from the template string and `attrNameIndex` comes from processing the\n      // resulting DOM.\n      if (attrNames.length !== attrNameIndex) {\n        throw new Error(\n          `Detected duplicate attribute bindings. This occurs if your template ` +\n            `has duplicate attributes on an element tag. For example ` +\n            `\"<input ?disabled=\\${true} ?disabled=\\${false}>\" contains a ` +\n            `duplicate \"disabled\" attribute. The error was detected in ` +\n            `the following template: \\n` +\n            '`' +\n            strings.join('${...}') +\n            '`'\n        );\n      }\n    }\n\n    // We could set walker.currentNode to another node here to prevent a memory\n    // leak, but every time we prepare a template, we immediately render it\n    // and re-use the walker in new TemplateInstance._clone().\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'template prep',\n        template: this,\n        clonableTemplate: this.el,\n        parts: this.parts,\n        strings,\n      });\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @nocollapse */\n  static createElement(html: TrustedHTML, _options?: RenderOptions) {\n    const el = d.createElement('template');\n    el.innerHTML = html as unknown as string;\n    return el;\n  }\n}\n\nexport interface Disconnectable {\n  _$parent?: Disconnectable;\n  _$disconnectableChildren?: Set<Disconnectable>;\n  // Rather than hold connection state on instances, Disconnectables recursively\n  // fetch the connection state from the RootPart they are connected in via\n  // getters up the Disconnectable tree via _$parent references. This pushes the\n  // cost of tracking the isConnected state to `AsyncDirectives`, and avoids\n  // needing to pass all Disconnectables (parts, template instances, and\n  // directives) their connection state each time it changes, which would be\n  // costly for trees that have no AsyncDirectives.\n  _$isConnected: boolean;\n}\n\nfunction resolveDirective(\n  part: ChildPart | AttributePart | ElementPart,\n  value: unknown,\n  parent: DirectiveParent = part,\n  attributeIndex?: number\n): unknown {\n  // Bail early if the value is explicitly noChange. Note, this means any\n  // nested directive is still attached and is not run.\n  if (value === noChange) {\n    return value;\n  }\n  let currentDirective =\n    attributeIndex !== undefined\n      ? (parent as AttributePart).__directives?.[attributeIndex]\n      : (parent as ChildPart | ElementPart | Directive).__directive;\n  const nextDirectiveConstructor = isPrimitive(value)\n    ? undefined\n    : // This property needs to remain unminified.\n      (value as DirectiveResult)['_$litDirective$'];\n  if (currentDirective?.constructor !== nextDirectiveConstructor) {\n    // This property needs to remain unminified.\n    currentDirective?.['_$notifyDirectiveConnectionChanged']?.(false);\n    if (nextDirectiveConstructor === undefined) {\n      currentDirective = undefined;\n    } else {\n      currentDirective = new nextDirectiveConstructor(part as PartInfo);\n      currentDirective._$initialize(part, parent, attributeIndex);\n    }\n    if (attributeIndex !== undefined) {\n      ((parent as AttributePart).__directives ??= [])[attributeIndex] =\n        currentDirective;\n    } else {\n      (parent as ChildPart | Directive).__directive = currentDirective;\n    }\n  }\n  if (currentDirective !== undefined) {\n    value = resolveDirective(\n      part,\n      currentDirective._$resolve(part, (value as DirectiveResult).values),\n      currentDirective,\n      attributeIndex\n    );\n  }\n  return value;\n}\n\nexport type {TemplateInstance};\n/**\n * An updateable instance of a Template. Holds references to the Parts used to\n * update the template instance.\n */\nclass TemplateInstance implements Disconnectable {\n  _$template: Template;\n  _$parts: Array<Part | undefined> = [];\n\n  /** @internal */\n  _$parent: ChildPart;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  constructor(template: Template, parent: ChildPart) {\n    this._$template = template;\n    this._$parent = parent;\n  }\n\n  // Called by ChildPart parentNode getter\n  get parentNode() {\n    return this._$parent.parentNode;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  // This method is separate from the constructor because we need to return a\n  // DocumentFragment and we don't want to hold onto it with an instance field.\n  _clone(options: RenderOptions | undefined) {\n    const {\n      el: {content},\n      parts: parts,\n    } = this._$template;\n    const fragment = (options?.creationScope ?? d).importNode(content, true);\n    walker.currentNode = fragment;\n\n    let node = walker.nextNode()!;\n    let nodeIndex = 0;\n    let partIndex = 0;\n    let templatePart = parts[0];\n\n    while (templatePart !== undefined) {\n      if (nodeIndex === templatePart.index) {\n        let part: Part | undefined;\n        if (templatePart.type === CHILD_PART) {\n          part = new ChildPart(\n            node as HTMLElement,\n            node.nextSibling,\n            this,\n            options\n          );\n        } else if (templatePart.type === ATTRIBUTE_PART) {\n          part = new templatePart.ctor(\n            node as HTMLElement,\n            templatePart.name,\n            templatePart.strings,\n            this,\n            options\n          );\n        } else if (templatePart.type === ELEMENT_PART) {\n          part = new ElementPart(node as HTMLElement, this, options);\n        }\n        this._$parts.push(part);\n        templatePart = parts[++partIndex];\n      }\n      if (nodeIndex !== templatePart?.index) {\n        node = walker.nextNode()!;\n        nodeIndex++;\n      }\n    }\n    // We need to set the currentNode away from the cloned tree so that we\n    // don't hold onto the tree even if the tree is detached and should be\n    // freed.\n    walker.currentNode = d;\n    return fragment;\n  }\n\n  _update(values: Array<unknown>) {\n    let i = 0;\n    for (const part of this._$parts) {\n      if (part !== undefined) {\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'set part',\n            part,\n            value: values[i],\n            valueIndex: i,\n            values,\n            templateInstance: this,\n          });\n        if ((part as AttributePart).strings !== undefined) {\n          (part as AttributePart)._$setValue(values, part as AttributePart, i);\n          // The number of values the part consumes is part.strings.length - 1\n          // since values are in between template spans. We increment i by 1\n          // later in the loop, so increment it by part.strings.length - 2 here\n          i += (part as AttributePart).strings!.length - 2;\n        } else {\n          part._$setValue(values[i]);\n        }\n      }\n      i++;\n    }\n  }\n}\n\n/*\n * Parts\n */\ntype AttributeTemplatePart = {\n  readonly type: typeof ATTRIBUTE_PART;\n  readonly index: number;\n  readonly name: string;\n  readonly ctor: typeof AttributePart;\n  readonly strings: ReadonlyArray<string>;\n};\ntype ChildTemplatePart = {\n  readonly type: typeof CHILD_PART;\n  readonly index: number;\n};\ntype ElementTemplatePart = {\n  readonly type: typeof ELEMENT_PART;\n  readonly index: number;\n};\ntype CommentTemplatePart = {\n  readonly type: typeof COMMENT_PART;\n  readonly index: number;\n};\n\n/**\n * A TemplatePart represents a dynamic part in a template, before the template\n * is instantiated. When a template is instantiated Parts are created from\n * TemplateParts.\n */\ntype TemplatePart =\n  | ChildTemplatePart\n  | AttributeTemplatePart\n  | ElementTemplatePart\n  | CommentTemplatePart;\n\nexport type Part =\n  | ChildPart\n  | AttributePart\n  | PropertyPart\n  | BooleanAttributePart\n  | ElementPart\n  | EventPart;\n\nexport type {ChildPart};\nclass ChildPart implements Disconnectable {\n  readonly type = CHILD_PART;\n  readonly options: RenderOptions | undefined;\n  _$committedValue: unknown = nothing;\n  /** @internal */\n  __directive?: Directive;\n  /** @internal */\n  _$startNode: ChildNode;\n  /** @internal */\n  _$endNode: ChildNode | null;\n  private _textSanitizer: ValueSanitizer | undefined;\n  /** @internal */\n  _$parent: Disconnectable | undefined;\n  /**\n   * Connection state for RootParts only (i.e. ChildPart without _$parent\n   * returned from top-level `render`). This field is unused otherwise. The\n   * intention would be clearer if we made `RootPart` a subclass of `ChildPart`\n   * with this field (and a different _$isConnected getter), but the subclass\n   * caused a perf regression, possibly due to making call sites polymorphic.\n   * @internal\n   */\n  __isConnected: boolean;\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    // ChildParts that are not at the root should always be created with a\n    // parent; only RootChildNode's won't, so they return the local isConnected\n    // state\n    return this._$parent?._$isConnected ?? this.__isConnected;\n  }\n\n  // The following fields will be patched onto ChildParts when required by\n  // AsyncDirective\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n  /** @internal */\n  _$notifyConnectionChanged?(\n    isConnected: boolean,\n    removeFromParent?: boolean,\n    from?: number\n  ): void;\n  /** @internal */\n  _$reparentDisconnectables?(parent: Disconnectable): void;\n\n  constructor(\n    startNode: ChildNode,\n    endNode: ChildNode | null,\n    parent: TemplateInstance | ChildPart | undefined,\n    options: RenderOptions | undefined\n  ) {\n    this._$startNode = startNode;\n    this._$endNode = endNode;\n    this._$parent = parent;\n    this.options = options;\n    // Note __isConnected is only ever accessed on RootParts (i.e. when there is\n    // no _$parent); the value on a non-root-part is \"don't care\", but checking\n    // for parent would be more code\n    this.__isConnected = options?.isConnected ?? true;\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      // Explicitly initialize for consistent class shape.\n      this._textSanitizer = undefined;\n    }\n  }\n\n  /**\n   * The parent node into which the part renders its content.\n   *\n   * A ChildPart's content consists of a range of adjacent child nodes of\n   * `.parentNode`, possibly bordered by 'marker nodes' (`.startNode` and\n   * `.endNode`).\n   *\n   * - If both `.startNode` and `.endNode` are non-null, then the part's content\n   * consists of all siblings between `.startNode` and `.endNode`, exclusively.\n   *\n   * - If `.startNode` is non-null but `.endNode` is null, then the part's\n   * content consists of all siblings following `.startNode`, up to and\n   * including the last child of `.parentNode`. If `.endNode` is non-null, then\n   * `.startNode` will always be non-null.\n   *\n   * - If both `.endNode` and `.startNode` are null, then the part's content\n   * consists of all child nodes of `.parentNode`.\n   */\n  get parentNode(): Node {\n    let parentNode: Node = wrap(this._$startNode).parentNode!;\n    const parent = this._$parent;\n    if (\n      parent !== undefined &&\n      parentNode?.nodeType === 11 /* Node.DOCUMENT_FRAGMENT */\n    ) {\n      // If the parentNode is a DocumentFragment, it may be because the DOM is\n      // still in the cloned fragment during initial render; if so, get the real\n      // parentNode the part will be committed into by asking the parent.\n      parentNode = (parent as ChildPart | TemplateInstance).parentNode;\n    }\n    return parentNode;\n  }\n\n  /**\n   * The part's leading marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get startNode(): Node | null {\n    return this._$startNode;\n  }\n\n  /**\n   * The part's trailing marker node, if any. See `.parentNode` for more\n   * information.\n   */\n  get endNode(): Node | null {\n    return this._$endNode;\n  }\n\n  _$setValue(value: unknown, directiveParent: DirectiveParent = this): void {\n    if (DEV_MODE && this.parentNode === null) {\n      throw new Error(\n        `This \\`ChildPart\\` has no \\`parentNode\\` and therefore cannot accept a value. This likely means the element containing the part was manipulated in an unsupported way outside of Lit's control such that the part's marker nodes were ejected from DOM. For example, setting the element's \\`innerHTML\\` or \\`textContent\\` can do this.`\n      );\n    }\n    value = resolveDirective(this, value, directiveParent);\n    if (isPrimitive(value)) {\n      // Non-rendering child values. It's important that these do not render\n      // empty text nodes to avoid issues with preventing default <slot>\n      // fallback content.\n      if (value === nothing || value == null || value === '') {\n        if (this._$committedValue !== nothing) {\n          debugLogEvent &&\n            debugLogEvent({\n              kind: 'commit nothing to child',\n              start: this._$startNode,\n              end: this._$endNode,\n              parent: this._$parent,\n              options: this.options,\n            });\n          this._$clear();\n        }\n        this._$committedValue = nothing;\n      } else if (value !== this._$committedValue && value !== noChange) {\n        this._commitText(value);\n      }\n      // This property needs to remain unminified.\n    } else if ((value as TemplateResult)['_$litType$'] !== undefined) {\n      this._commitTemplateResult(value as TemplateResult);\n    } else if ((value as Node).nodeType !== undefined) {\n      if (DEV_MODE && this.options?.host === value) {\n        this._commitText(\n          `[probable mistake: rendered a template's host in itself ` +\n            `(commonly caused by writing \\${this} in a template]`\n        );\n        console.warn(\n          `Attempted to render the template host`,\n          value,\n          `inside itself. This is almost always a mistake, and in dev mode `,\n          `we render some warning text. In production however, we'll `,\n          `render it, which will usually result in an error, and sometimes `,\n          `in the element disappearing from the DOM.`\n        );\n        return;\n      }\n      this._commitNode(value as Node);\n    } else if (isIterable(value)) {\n      this._commitIterable(value);\n    } else {\n      // Fallback, will render the string representation\n      this._commitText(value);\n    }\n  }\n\n  private _insert<T extends Node>(node: T) {\n    return wrap(wrap(this._$startNode).parentNode!).insertBefore(\n      node,\n      this._$endNode\n    );\n  }\n\n  private _commitNode(value: Node): void {\n    if (this._$committedValue !== value) {\n      this._$clear();\n      if (\n        ENABLE_EXTRA_SECURITY_HOOKS &&\n        sanitizerFactoryInternal !== noopSanitizer\n      ) {\n        const parentNodeName = this._$startNode.parentNode?.nodeName;\n        if (parentNodeName === 'STYLE' || parentNodeName === 'SCRIPT') {\n          let message = 'Forbidden';\n          if (DEV_MODE) {\n            if (parentNodeName === 'STYLE') {\n              message =\n                `Lit does not support binding inside style nodes. ` +\n                `This is a security risk, as style injection attacks can ` +\n                `exfiltrate data and spoof UIs. ` +\n                `Consider instead using css\\`...\\` literals ` +\n                `to compose styles, and do dynamic styling with ` +\n                `css custom properties, ::parts, <slot>s, ` +\n                `and by mutating the DOM rather than stylesheets.`;\n            } else {\n              message =\n                `Lit does not support binding inside script nodes. ` +\n                `This is a security risk, as it could allow arbitrary ` +\n                `code execution.`;\n            }\n          }\n          throw new Error(message);\n        }\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit node',\n          start: this._$startNode,\n          parent: this._$parent,\n          value: value,\n          options: this.options,\n        });\n      this._$committedValue = this._insert(value);\n    }\n  }\n\n  private _commitText(value: unknown): void {\n    // If the committed value is a primitive it means we called _commitText on\n    // the previous render, and we know that this._$startNode.nextSibling is a\n    // Text node. We can now just replace the text content (.data) of the node.\n    if (\n      this._$committedValue !== nothing &&\n      isPrimitive(this._$committedValue)\n    ) {\n      const node = wrap(this._$startNode).nextSibling as Text;\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(node, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit text',\n          node,\n          value,\n          options: this.options,\n        });\n      (node as Text).data = value as string;\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        const textNode = d.createTextNode('');\n        this._commitNode(textNode);\n        // When setting text content, for security purposes it matters a lot\n        // what the parent is. For example, <style> and <script> need to be\n        // handled with care, while <span> does not. So first we need to put a\n        // text node into the document, then we can sanitize its content.\n        if (this._textSanitizer === undefined) {\n          this._textSanitizer = createSanitizer(textNode, 'data', 'property');\n        }\n        value = this._textSanitizer(value);\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'commit text',\n            node: textNode,\n            value,\n            options: this.options,\n          });\n        textNode.data = value as string;\n      } else {\n        this._commitNode(d.createTextNode(value as string));\n        debugLogEvent &&\n          debugLogEvent({\n            kind: 'commit text',\n            node: wrap(this._$startNode).nextSibling as Text,\n            value,\n            options: this.options,\n          });\n      }\n    }\n    this._$committedValue = value;\n  }\n\n  private _commitTemplateResult(\n    result: TemplateResult | CompiledTemplateResult\n  ): void {\n    // This property needs to remain unminified.\n    const {values, ['_$litType$']: type} = result;\n    // If $litType$ is a number, result is a plain TemplateResult and we get\n    // the template from the template cache. If not, result is a\n    // CompiledTemplateResult and _$litType$ is a CompiledTemplate and we need\n    // to create the <template> element the first time we see it.\n    const template: Template | CompiledTemplate =\n      typeof type === 'number'\n        ? this._$getTemplate(result as UncompiledTemplateResult)\n        : (type.el === undefined &&\n            (type.el = Template.createElement(\n              trustFromTemplateString(type.h, type.h[0]),\n              this.options\n            )),\n          type);\n\n    if ((this._$committedValue as TemplateInstance)?._$template === template) {\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template updating',\n          template,\n          instance: this._$committedValue as TemplateInstance,\n          parts: (this._$committedValue as TemplateInstance)._$parts,\n          options: this.options,\n          values,\n        });\n      (this._$committedValue as TemplateInstance)._update(values);\n    } else {\n      const instance = new TemplateInstance(template as Template, this);\n      const fragment = instance._clone(this.options);\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template instantiated',\n          template,\n          instance,\n          parts: instance._$parts,\n          options: this.options,\n          fragment,\n          values,\n        });\n      instance._update(values);\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'template instantiated and updated',\n          template,\n          instance,\n          parts: instance._$parts,\n          options: this.options,\n          fragment,\n          values,\n        });\n      this._commitNode(fragment);\n      this._$committedValue = instance;\n    }\n  }\n\n  // Overridden via `litHtmlPolyfillSupport` to provide platform support.\n  /** @internal */\n  _$getTemplate(result: UncompiledTemplateResult) {\n    let template = templateCache.get(result.strings);\n    if (template === undefined) {\n      templateCache.set(result.strings, (template = new Template(result)));\n    }\n    return template;\n  }\n\n  private _commitIterable(value: Iterable<unknown>): void {\n    // For an Iterable, we create a new InstancePart per item, then set its\n    // value to the item. This is a little bit of overhead for every item in\n    // an Iterable, but it lets us recurse easily and efficiently update Arrays\n    // of TemplateResults that will be commonly returned from expressions like:\n    // array.map((i) => html`${i}`), by reusing existing TemplateInstances.\n\n    // If value is an array, then the previous render was of an\n    // iterable and value will contain the ChildParts from the previous\n    // render. If value is not an array, clear this part and make a new\n    // array for ChildParts.\n    if (!isArray(this._$committedValue)) {\n      this._$committedValue = [];\n      this._$clear();\n    }\n\n    // Lets us keep track of how many items we stamped so we can clear leftover\n    // items from a previous render\n    const itemParts = this._$committedValue as ChildPart[];\n    let partIndex = 0;\n    let itemPart: ChildPart | undefined;\n\n    for (const item of value) {\n      if (partIndex === itemParts.length) {\n        // If no existing part, create a new one\n        // TODO (justinfagnani): test perf impact of always creating two parts\n        // instead of sharing parts between nodes\n        // https://github.com/lit/lit/issues/1266\n        itemParts.push(\n          (itemPart = new ChildPart(\n            this._insert(createMarker()),\n            this._insert(createMarker()),\n            this,\n            this.options\n          ))\n        );\n      } else {\n        // Reuse an existing part\n        itemPart = itemParts[partIndex];\n      }\n      itemPart._$setValue(item);\n      partIndex++;\n    }\n\n    if (partIndex < itemParts.length) {\n      // itemParts always have end nodes\n      this._$clear(\n        itemPart && wrap(itemPart._$endNode!).nextSibling,\n        partIndex\n      );\n      // Truncate the parts array so _value reflects the current state\n      itemParts.length = partIndex;\n    }\n  }\n\n  /**\n   * Removes the nodes contained within this Part from the DOM.\n   *\n   * @param start Start node to clear from, for clearing a subset of the part's\n   *     DOM (used when truncating iterables)\n   * @param from  When `start` is specified, the index within the iterable from\n   *     which ChildParts are being removed, used for disconnecting directives in\n   *     those Parts.\n   *\n   * @internal\n   */\n  _$clear(\n    start: ChildNode | null = wrap(this._$startNode).nextSibling,\n    from?: number\n  ) {\n    this._$notifyConnectionChanged?.(false, true, from);\n    while (start && start !== this._$endNode) {\n      const n = wrap(start!).nextSibling;\n      (wrap(start!) as Element).remove();\n      start = n;\n    }\n  }\n  /**\n   * Implementation of RootPart's `isConnected`. Note that this method\n   * should only be called on `RootPart`s (the `ChildPart` returned from a\n   * top-level `render()` call). It has no effect on non-root ChildParts.\n   * @param isConnected Whether to set\n   * @internal\n   */\n  setConnected(isConnected: boolean) {\n    if (this._$parent === undefined) {\n      this.__isConnected = isConnected;\n      this._$notifyConnectionChanged?.(isConnected);\n    } else if (DEV_MODE) {\n      throw new Error(\n        'part.setConnected() may only be called on a ' +\n          'RootPart returned from render().'\n      );\n    }\n  }\n}\n\n/**\n * A top-level `ChildPart` returned from `render` that manages the connected\n * state of `AsyncDirective`s created throughout the tree below it.\n */\nexport interface RootPart extends ChildPart {\n  /**\n   * Sets the connection state for `AsyncDirective`s contained within this root\n   * ChildPart.\n   *\n   * lit-html does not automatically monitor the connectedness of DOM rendered;\n   * as such, it is the responsibility of the caller to `render` to ensure that\n   * `part.setConnected(false)` is called before the part object is potentially\n   * discarded, to ensure that `AsyncDirective`s have a chance to dispose of\n   * any resources being held. If a `RootPart` that was previously\n   * disconnected is subsequently re-connected (and its `AsyncDirective`s should\n   * re-connect), `setConnected(true)` should be called.\n   *\n   * @param isConnected Whether directives within this tree should be connected\n   * or not\n   */\n  setConnected(isConnected: boolean): void;\n}\n\nexport type {AttributePart};\nclass AttributePart implements Disconnectable {\n  readonly type:\n    | typeof ATTRIBUTE_PART\n    | typeof PROPERTY_PART\n    | typeof BOOLEAN_ATTRIBUTE_PART\n    | typeof EVENT_PART = ATTRIBUTE_PART;\n  readonly element: HTMLElement;\n  readonly name: string;\n  readonly options: RenderOptions | undefined;\n\n  /**\n   * If this attribute part represents an interpolation, this contains the\n   * static strings of the interpolation. For single-value, complete bindings,\n   * this is undefined.\n   */\n  readonly strings?: ReadonlyArray<string>;\n  /** @internal */\n  _$committedValue: unknown | Array<unknown> = nothing;\n  /** @internal */\n  __directives?: Array<Directive | undefined>;\n  /** @internal */\n  _$parent: Disconnectable;\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  protected _sanitizer: ValueSanitizer | undefined;\n\n  get tagName() {\n    return this.element.tagName;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this.element = element;\n    this.name = name;\n    this._$parent = parent;\n    this.options = options;\n    if (strings.length > 2 || strings[0] !== '' || strings[1] !== '') {\n      this._$committedValue = new Array(strings.length - 1).fill(new String());\n      this.strings = strings;\n    } else {\n      this._$committedValue = nothing;\n    }\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      this._sanitizer = undefined;\n    }\n  }\n\n  /**\n   * Sets the value of this part by resolving the value from possibly multiple\n   * values and static strings and committing it to the DOM.\n   * If this part is single-valued, `this._strings` will be undefined, and the\n   * method will be called with a single value argument. If this part is\n   * multi-value, `this._strings` will be defined, and the method is called\n   * with the value array of the part's owning TemplateInstance, and an offset\n   * into the value array from which the values should be read.\n   * This method is overloaded this way to eliminate short-lived array slices\n   * of the template instance values, and allow a fast-path for single-valued\n   * parts.\n   *\n   * @param value The part value, or an array of values for multi-valued parts\n   * @param valueIndex the index to start reading values from. `undefined` for\n   *   single-valued parts\n   * @param noCommit causes the part to not commit its value to the DOM. Used\n   *   in hydration to prime attribute parts with their first-rendered value,\n   *   but not set the attribute, and in SSR to no-op the DOM operation and\n   *   capture the value for serialization.\n   *\n   * @internal\n   */\n  _$setValue(\n    value: unknown | Array<unknown>,\n    directiveParent: DirectiveParent = this,\n    valueIndex?: number,\n    noCommit?: boolean\n  ) {\n    const strings = this.strings;\n\n    // Whether any of the values has changed, for dirty-checking\n    let change = false;\n\n    if (strings === undefined) {\n      // Single-value binding case\n      value = resolveDirective(this, value, directiveParent, 0);\n      change =\n        !isPrimitive(value) ||\n        (value !== this._$committedValue && value !== noChange);\n      if (change) {\n        this._$committedValue = value;\n      }\n    } else {\n      // Interpolation case\n      const values = value as Array<unknown>;\n      value = strings[0];\n\n      let i, v;\n      for (i = 0; i < strings.length - 1; i++) {\n        v = resolveDirective(this, values[valueIndex! + i], directiveParent, i);\n\n        if (v === noChange) {\n          // If the user-provided value is `noChange`, use the previous value\n          v = (this._$committedValue as Array<unknown>)[i];\n        }\n        change ||=\n          !isPrimitive(v) || v !== (this._$committedValue as Array<unknown>)[i];\n        if (v === nothing) {\n          value = nothing;\n        } else if (value !== nothing) {\n          value += (v ?? '') + strings[i + 1];\n        }\n        // We always record each value, even if one is `nothing`, for future\n        // change detection.\n        (this._$committedValue as Array<unknown>)[i] = v;\n      }\n    }\n    if (change && !noCommit) {\n      this._commitValue(value);\n    }\n  }\n\n  /** @internal */\n  _commitValue(value: unknown) {\n    if (value === nothing) {\n      (wrap(this.element) as Element).removeAttribute(this.name);\n    } else {\n      if (ENABLE_EXTRA_SECURITY_HOOKS) {\n        if (this._sanitizer === undefined) {\n          this._sanitizer = sanitizerFactoryInternal(\n            this.element,\n            this.name,\n            'attribute'\n          );\n        }\n        value = this._sanitizer(value ?? '');\n      }\n      debugLogEvent &&\n        debugLogEvent({\n          kind: 'commit attribute',\n          element: this.element,\n          name: this.name,\n          value,\n          options: this.options,\n        });\n      (wrap(this.element) as Element).setAttribute(\n        this.name,\n        (value ?? '') as string\n      );\n    }\n  }\n}\n\nexport type {PropertyPart};\nclass PropertyPart extends AttributePart {\n  override readonly type = PROPERTY_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    if (ENABLE_EXTRA_SECURITY_HOOKS) {\n      if (this._sanitizer === undefined) {\n        this._sanitizer = sanitizerFactoryInternal(\n          this.element,\n          this.name,\n          'property'\n        );\n      }\n      value = this._sanitizer(value);\n    }\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit property',\n        element: this.element,\n        name: this.name,\n        value,\n        options: this.options,\n      });\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (this.element as any)[this.name] = value === nothing ? undefined : value;\n  }\n}\n\nexport type {BooleanAttributePart};\nclass BooleanAttributePart extends AttributePart {\n  override readonly type = BOOLEAN_ATTRIBUTE_PART;\n\n  /** @internal */\n  override _commitValue(value: unknown) {\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit boolean attribute',\n        element: this.element,\n        name: this.name,\n        value: !!(value && value !== nothing),\n        options: this.options,\n      });\n    (wrap(this.element) as Element).toggleAttribute(\n      this.name,\n      !!value && value !== nothing\n    );\n  }\n}\n\ntype EventListenerWithOptions = EventListenerOrEventListenerObject &\n  Partial<AddEventListenerOptions>;\n\n/**\n * An AttributePart that manages an event listener via add/removeEventListener.\n *\n * This part works by adding itself as the event listener on an element, then\n * delegating to the value passed to it. This reduces the number of calls to\n * add/removeEventListener if the listener changes frequently, such as when an\n * inline function is used as a listener.\n *\n * Because event options are passed when adding listeners, we must take case\n * to add and remove the part as a listener when the event options change.\n */\nexport type {EventPart};\nclass EventPart extends AttributePart {\n  override readonly type = EVENT_PART;\n\n  constructor(\n    element: HTMLElement,\n    name: string,\n    strings: ReadonlyArray<string>,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    super(element, name, strings, parent, options);\n\n    if (DEV_MODE && this.strings !== undefined) {\n      throw new Error(\n        `A \\`<${element.localName}>\\` has a \\`@${name}=...\\` listener with ` +\n          'invalid content. Event listeners in templates must have exactly ' +\n          'one expression and no surrounding text.'\n      );\n    }\n  }\n\n  // EventPart does not use the base _$setValue/_resolveValue implementation\n  // since the dirty checking is more complex\n  /** @internal */\n  override _$setValue(\n    newListener: unknown,\n    directiveParent: DirectiveParent = this\n  ) {\n    newListener =\n      resolveDirective(this, newListener, directiveParent, 0) ?? nothing;\n    if (newListener === noChange) {\n      return;\n    }\n    const oldListener = this._$committedValue;\n\n    // If the new value is nothing or any options change we have to remove the\n    // part as a listener.\n    const shouldRemoveListener =\n      (newListener === nothing && oldListener !== nothing) ||\n      (newListener as EventListenerWithOptions).capture !==\n        (oldListener as EventListenerWithOptions).capture ||\n      (newListener as EventListenerWithOptions).once !==\n        (oldListener as EventListenerWithOptions).once ||\n      (newListener as EventListenerWithOptions).passive !==\n        (oldListener as EventListenerWithOptions).passive;\n\n    // If the new value is not nothing and we removed the listener, we have\n    // to add the part as a listener.\n    const shouldAddListener =\n      newListener !== nothing &&\n      (oldListener === nothing || shouldRemoveListener);\n\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit event listener',\n        element: this.element,\n        name: this.name,\n        value: newListener,\n        options: this.options,\n        removeListener: shouldRemoveListener,\n        addListener: shouldAddListener,\n        oldListener,\n      });\n    if (shouldRemoveListener) {\n      this.element.removeEventListener(\n        this.name,\n        this,\n        oldListener as EventListenerWithOptions\n      );\n    }\n    if (shouldAddListener) {\n      this.element.addEventListener(\n        this.name,\n        this,\n        newListener as EventListenerWithOptions\n      );\n    }\n    this._$committedValue = newListener;\n  }\n\n  handleEvent(event: Event) {\n    if (typeof this._$committedValue === 'function') {\n      this._$committedValue.call(this.options?.host ?? this.element, event);\n    } else {\n      (this._$committedValue as EventListenerObject).handleEvent(event);\n    }\n  }\n}\n\nexport type {ElementPart};\nclass ElementPart implements Disconnectable {\n  readonly type = ELEMENT_PART;\n\n  /** @internal */\n  __directive?: Directive;\n\n  // This is to ensure that every Part has a _$committedValue\n  _$committedValue: undefined;\n\n  /** @internal */\n  _$parent!: Disconnectable;\n\n  /** @internal */\n  _$disconnectableChildren?: Set<Disconnectable> = undefined;\n\n  options: RenderOptions | undefined;\n\n  constructor(\n    public element: Element,\n    parent: Disconnectable,\n    options: RenderOptions | undefined\n  ) {\n    this._$parent = parent;\n    this.options = options;\n  }\n\n  // See comment in Disconnectable interface for why this is a getter\n  get _$isConnected() {\n    return this._$parent._$isConnected;\n  }\n\n  _$setValue(value: unknown): void {\n    debugLogEvent &&\n      debugLogEvent({\n        kind: 'commit to element binding',\n        element: this.element,\n        value,\n        options: this.options,\n      });\n    resolveDirective(this, value);\n  }\n}\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * Private exports for use by other Lit packages, not intended for use by\n * external users.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n *\n * This has a unique name, to disambiguate it from private exports in\n * lit-element, which re-exports all of lit-html.\n *\n * @private\n */\nexport const _$LH = {\n  // Used in lit-ssr\n  _boundAttributeSuffix: boundAttributeSuffix,\n  _marker: marker,\n  _markerMatch: markerMatch,\n  _HTML_RESULT: HTML_RESULT,\n  _getTemplateHtml: getTemplateHtml,\n  // Used in tests and private-ssr-support\n  _TemplateInstance: TemplateInstance,\n  _isIterable: isIterable,\n  _resolveDirective: resolveDirective,\n  _ChildPart: ChildPart,\n  _AttributePart: AttributePart,\n  _BooleanAttributePart: BooleanAttributePart,\n  _EventPart: EventPart,\n  _PropertyPart: PropertyPart,\n  _ElementPart: ElementPart,\n};\n\n// Apply polyfills if available\nconst polyfillSupport = DEV_MODE\n  ? global.litHtmlPolyfillSupportDevMode\n  : global.litHtmlPolyfillSupport;\npolyfillSupport?.(Template, ChildPart);\n\n// IMPORTANT: do not change the property name or the assignment expression.\n// This line will be used in regexes to search for lit-html usage.\n(global.litHtmlVersions ??= []).push('3.3.0');\nif (DEV_MODE && global.litHtmlVersions.length > 1) {\n  queueMicrotask(() => {\n    issueWarning!(\n      'multiple-versions',\n      `Multiple versions of Lit loaded. ` +\n        `Loading multiple versions is not recommended.`\n    );\n  });\n}\n\n/**\n * Renders a value, usually a lit-html TemplateResult, to the container.\n *\n * This example renders the text \"Hello, Zoe!\" inside a paragraph tag, appending\n * it to the container `document.body`.\n *\n * ```js\n * import {html, render} from 'lit';\n *\n * const name = \"Zoe\";\n * render(html`<p>Hello, ${name}!</p>`, document.body);\n * ```\n *\n * @param value Any [renderable\n *   value](https://lit.dev/docs/templates/expressions/#child-expressions),\n *   typically a {@linkcode TemplateResult} created by evaluating a template tag\n *   like {@linkcode html} or {@linkcode svg}.\n * @param container A DOM container to render to. The first render will append\n *   the rendered value to the container, and subsequent renders will\n *   efficiently update the rendered value if the same result type was\n *   previously rendered there.\n * @param options See {@linkcode RenderOptions} for options documentation.\n * @see\n * {@link https://lit.dev/docs/libraries/standalone-templates/#rendering-lit-html-templates| Rendering Lit HTML Templates}\n */\nexport const render = (\n  value: unknown,\n  container: HTMLElement | DocumentFragment,\n  options?: RenderOptions\n): RootPart => {\n  if (DEV_MODE && container == null) {\n    // Give a clearer error message than\n    //     Uncaught TypeError: Cannot read properties of null (reading\n    //     '_$litPart$')\n    // which reads like an internal Lit error.\n    throw new TypeError(`The container to render into may not be ${container}`);\n  }\n  const renderId = DEV_MODE ? debugLogRenderId++ : 0;\n  const partOwnerNode = options?.renderBefore ?? container;\n  // This property needs to remain unminified.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let part: ChildPart = (partOwnerNode as any)['_$litPart$'];\n  debugLogEvent &&\n    debugLogEvent({\n      kind: 'begin render',\n      id: renderId,\n      value,\n      container,\n      options,\n      part,\n    });\n  if (part === undefined) {\n    const endNode = options?.renderBefore ?? null;\n    // This property needs to remain unminified.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (partOwnerNode as any)['_$litPart$'] = part = new ChildPart(\n      container.insertBefore(createMarker(), endNode),\n      endNode,\n      undefined,\n      options ?? {}\n    );\n  }\n  part._$setValue(value);\n  debugLogEvent &&\n    debugLogEvent({\n      kind: 'end render',\n      id: renderId,\n      value,\n      container,\n      options,\n      part,\n    });\n  return part as RootPart;\n};\n\nif (ENABLE_EXTRA_SECURITY_HOOKS) {\n  render.setSanitizer = setSanitizer;\n  render.createSanitizer = createSanitizer;\n  if (DEV_MODE) {\n    render._testOnlyClearSanitizerFactoryDoNotCallOrElse =\n      _testOnlyClearSanitizerFactoryDoNotCallOrElse;\n  }\n}\n"], "names": ["global", "globalThis", "trustedTypes", "policy", "createPolicy", "createHTML", "s", "undefined", "boundAttributeSuffix", "marker", "Math", "random", "toFixed", "slice", "markerMatch", "node<PERSON>ark<PERSON>", "d", "document", "createTreeWalker", "createMarker", "createComment", "isPrimitive", "value", "isArray", "Array", "isIterable", "Symbol", "iterator", "SPACE_CHAR", "textEndRegex", "commentEndRegex", "comment2EndRegex", "tagEndRegex", "RegExp", "singleQuoteAttrEndRegex", "doubleQuoteAttrEndRegex", "rawTextElement", "tag", "type", "strings", "values", "_$litType$", "html", "svg", "mathml", "noChange", "for", "nothing", "templateCache", "WeakMap", "walker", "trustFromTemplateString", "tsa", "stringFromTSA", "hasOwnProperty", "Error", "getTemplateHtml", "l", "length", "attrNames", "rawTextEndRegex", "regex", "i", "attrName", "match", "attrNameEndIndex", "lastIndex", "exec", "test", "end", "startsWith", "push", "Template", "constructor", "options", "node", "this", "parts", "nodeIndex", "attrNameIndex", "partCount", "el", "createElement", "currentNode", "content", "wrapper", "<PERSON><PERSON><PERSON><PERSON>", "replaceWith", "childNodes", "nextNode", "nodeType", "hasAttributes", "name", "getAttributeNames", "endsWith", "realName", "statics", "getAttribute", "split", "m", "index", "ctor", "PropertyPart", "BooleanAttributePart", "EventPart", "AttributePart", "removeAttribute", "tagName", "textContent", "emptyScript", "append", "data", "indexOf", "_options", "innerHTML", "resolveDirective", "part", "parent", "attributeIndex", "currentDirective", "__directives", "__directive", "nextDirectiveConstructor", "_$initialize", "_$resolve", "TemplateInstance", "template", "_$parts", "_$disconnectableChildren", "_$template", "_$parent", "parentNode", "_$isConnected", "_clone", "fragment", "creationScope", "importNode", "partIndex", "templatePart", "<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "ElementPart", "_update", "_$setValue", "__isConnected", "startNode", "endNode", "_$committedValue", "_$startNode", "_$endNode", "isConnected", "directiveParent", "_$clear", "_commitText", "_commitTemplateResult", "_commitNode", "_commitIterable", "_insert", "insertBefore", "createTextNode", "result", "_$getTemplate", "h", "instance", "get", "set", "itemParts", "itemPart", "item", "start", "from", "_$notifyConnectionChanged", "n", "remove", "setConnected", "element", "fill", "String", "valueIndex", "noCommit", "change", "v", "_commitValue", "setAttribute", "toggleAttribute", "super", "newListener", "old<PERSON><PERSON><PERSON>", "shouldRemoveListener", "capture", "once", "passive", "shouldAddListener", "removeEventListener", "addEventListener", "handleEvent", "event", "call", "host", "_$LH", "_boundAttributeSuffix", "_marker", "_markerMatch", "_HTML_RESULT", "_getTemplateHtml", "_TemplateInstance", "_isIterable", "_resolveDirective", "_<PERSON><PERSON><PERSON>", "_AttributePart", "_BooleanAttributePart", "_EventPart", "_PropertyPart", "_ElementPart", "polyfillSupport", "litHtmlPolyfillSupport", "litHtmlVersions", "render", "container", "partOwnerNode", "renderBefore"], "mappings": ";;;;;AAgBA,MAAMA,EAASC,WA4OTC,EAAgBF,EAAyCE,aAUzDC,EAASD,EACXA,EAAaE,aAAa,WAAY,CACpCC,WAAaC,GAAMA,SAErBC,EA4EEC,EAAuB,QAMvBC,EAAS,OAAOC,KAAKC,SAASC,QAAQ,GAAGC,MAAM,MAG/CC,EAAc,IAAML,EAIpBM,EAAa,IAAID,KAEjBE,OAC6BT,IAApBP,EAAOiB,SACf,CACCC,iBAAgB,KACP,KAGXD,SAGAE,EAAe,IAAMH,EAAEI,cAAc,IAIrCC,EAAeC,GACT,OAAVA,GAAmC,iBAATA,GAAqC,mBAATA,EAClDC,EAAUC,MAAMD,QAChBE,EAAcH,GAClBC,EAAQD,IAEqC,mBAArCA,IAAgBI,OAAOC,UAE3BC,EAAa,cAkBbC,EAAe,sDAKfC,EAAkB,OAIlBC,EAAmB,KAwBnBC,EAAkBC,OACtB,KAAKL,sBAAgCA,MAAeA,uCACpD,KAOIM,EAA0B,KAC1BC,EAA0B,KAO1BC,EAAiB,qCAyGjBC,EACmBC,GACvB,CAACC,KAAkCC,KAwB1B,CAELC,WAAgBH,EAChBC,UACAC,WAiBOE,EAAOL,EArJA,GA+KPM,EAAMN,EA9KA,GAwMNO,EAASP,EAvMA,GA6MTQ,EAAWnB,OAAOoB,IAAI,gBAqBtBC,EAAUrB,OAAOoB,IAAI,eAS5BE,EAAgB,IAAIC,QAqCpBC,EAASlC,EAAEE,iBACfF,EACA,KAqBF,SAASmC,EACPC,EACAC,GAOA,IAAK9B,EAAQ6B,KAASA,EAAIE,eAAe,OAiBvC,MAAUC,MAhBI,kCAkBhB,YAAkBhD,IAAXJ,EACHA,EAAOE,WAAWgD,GACjBA,CACP,CAcA,MAAMG,EAAkB,CACtBjB,EACAD,KAQA,MAAMmB,EAAIlB,EAAQmB,OAAS,EAIrBC,EAA2B,GACjC,IAMIC,EANAlB,EApWa,IAqWfJ,EAAsB,QApWJ,IAoWcA,EAAyB,SAAW,GASlEuB,EAAQhC,EAEZ,IAAK,IAAIiC,EAAI,EAAGA,EAAIL,EAAGK,IAAK,CAC1B,MAAMxD,EAAIiC,EAAQuB,GAMlB,IACIC,EAEAC,EAHAC,GAAoB,EAEpBC,EAAY,EAKhB,KAAOA,EAAY5D,EAAEoD,SAEnBG,EAAMK,UAAYA,EAClBF,EAAQH,EAAMM,KAAK7D,GACL,OAAV0D,IAGJE,EAAYL,EAAMK,UACdL,IAAUhC,EACiB,QAAzBmC,EA5bU,GA6bZH,EAAQ/B,OAC0BvB,IAAzByD,EA9bG,GAgcZH,EAAQ9B,OACqBxB,IAApByD,EAhcF,IAicH5B,EAAegC,KAAKJ,EAjcjB,MAocLJ,EAAsB3B,OAAO,KAAK+B,EApc7B,GAocgD,MAEvDH,EAAQ7B,QAC6BzB,IAA5ByD,EAtcM,KA6cfH,EAAQ7B,GAED6B,IAAU7B,EACS,MAAxBgC,EA9aS,IAibXH,EAAQD,GAAmB/B,EAG3BoC,GAAoB,QACe1D,IAA1ByD,EApbI,GAsbbC,GAAoB,GAEpBA,EAAmBJ,EAAMK,UAAYF,EAvbrB,GAub8CN,OAC9DK,EAAWC,EAzbE,GA0bbH,OACwBtD,IAAtByD,EAzbO,GA0bHhC,EACsB,MAAtBgC,EA3bG,GA4bD7B,EACAD,GAGV2B,IAAU1B,GACV0B,IAAU3B,EAEV2B,EAAQ7B,EACC6B,IAAU/B,GAAmB+B,IAAU9B,EAChD8B,EAAQhC,GAIRgC,EAAQ7B,EACR4B,OAAkBrD,GA8BtB,MAAM8D,EACJR,IAAU7B,GAAeO,EAAQuB,EAAI,GAAGQ,WAAW,MAAQ,IAAM,GACnE5B,GACEmB,IAAUhC,EACNvB,EAAIS,EACJkD,GAAoB,GACjBN,EAAUY,KAAKR,GAChBzD,EAAEO,MAAM,EAAGoD,GACTzD,EACAF,EAAEO,MAAMoD,GACVxD,EACA4D,GACA/D,EAAIG,IAAgC,IAAtBwD,EAA0BH,EAAIO,EACrD,CAQD,MAAO,CAAClB,EAAwBZ,EAL9BG,GACCH,EAAQkB,IAAM,QA3eA,IA4ednB,EAAsB,SA3eL,IA2egBA,EAAyB,UAAY,KAGnBqB,EAAU,EAKlE,MAAMa,EAMJ,WAAAC,EAEElC,QAACA,EAASE,WAAgBH,GAC1BoC,GAEA,IAAIC,EAPNC,KAAKC,MAAwB,GAQ3B,IAAIC,EAAY,EACZC,EAAgB,EACpB,MAAMC,EAAYzC,EAAQmB,OAAS,EAC7BmB,EAAQD,KAAKC,OAGZnC,EAAMiB,GAAaH,EAAgBjB,EAASD,GAKnD,GAJAsC,KAAKK,GAAKT,EAASU,cAAcxC,EAAMgC,GACvCxB,EAAOiC,YAAcP,KAAKK,GAAGG,QAxgBd,IA2gBX9C,GA1gBc,IA0gBSA,EAAwB,CACjD,MAAM+C,EAAUT,KAAKK,GAAGG,QAAQE,WAChCD,EAAQE,eAAeF,EAAQG,WAChC,CAGD,KAAsC,QAA9Bb,EAAOzB,EAAOuC,aAAwBZ,EAAMnB,OAASsB,GAAW,CACtE,GAAsB,IAAlBL,EAAKe,SAAgB,CAuBvB,GAAKf,EAAiBgB,gBACpB,IAAK,MAAMC,KAASjB,EAAiBkB,oBACnC,GAAID,EAAKE,SAAStF,GAAuB,CACvC,MAAMuF,EAAWpC,EAAUoB,KAErBiB,EADSrB,EAAiBsB,aAAaL,GACvBM,MAAMzF,GACtB0F,EAAI,eAAehC,KAAK4B,GAC9BlB,EAAMN,KAAK,CACTjC,KA1iBO,EA2iBP8D,MAAOtB,EACPc,KAAMO,EAAE,GACR5D,QAASyD,EACTK,KACW,MAATF,EAAE,GACEG,EACS,MAATH,EAAE,GACAI,EACS,MAATJ,EAAE,GACAK,EACAC,IAEX9B,EAAiB+B,gBAAgBd,EACnC,MAAUA,EAAKtB,WAAW7D,KACzBoE,EAAMN,KAAK,CACTjC,KArjBK,EAsjBL8D,MAAOtB,IAERH,EAAiB+B,gBAAgBd,IAMxC,GAAIxD,EAAegC,KAAMO,EAAiBgC,SAAU,CAIlD,MAAMpE,EAAWoC,EAAiBiC,YAAaV,MAAMzF,GAC/CyD,EAAY3B,EAAQmB,OAAS,EACnC,GAAIQ,EAAY,EAAG,CAChBS,EAAiBiC,YAAc1G,EAC3BA,EAAa2G,YACd,GAGJ,IAAK,IAAI/C,EAAI,EAAGA,EAAII,EAAWJ,IAC5Ba,EAAiBmC,OAAOvE,EAAQuB,GAAI3C,KAErC+B,EAAOuC,WACPZ,EAAMN,KAAK,CAACjC,KAllBP,EAklByB8D,QAAStB,IAKxCH,EAAiBmC,OAAOvE,EAAQ2B,GAAY/C,IAC9C,CACF,CACF,MAAM,GAAsB,IAAlBwD,EAAKe,SAEd,GADcf,EAAiBoC,OAClBjG,EACX+D,EAAMN,KAAK,CAACjC,KA7lBH,EA6lBqB8D,MAAOtB,QAChC,CACL,IAAIhB,GAAK,EACT,MAAgE,KAAxDA,EAAKa,EAAiBoC,KAAKC,QAAQvG,EAAQqD,EAAI,KAGrDe,EAAMN,KAAK,CAACjC,KA9lBH,EA8lBuB8D,MAAOtB,IAEvChB,GAAKrD,EAAOiD,OAAS,CAExB,CAEHoB,GACD,CAkCF,CAID,oBAAOI,CAAcxC,EAAmBuE,GACtC,MAAMhC,EAAKjE,EAAEkE,cAAc,YAE3B,OADAD,EAAGiC,UAAYxE,EACRuC,CACR,EAgBH,SAASkC,EACPC,EACA9F,EACA+F,EAA0BD,EAC1BE,GAIA,GAAIhG,IAAUuB,EACZ,OAAOvB,EAET,IAAIiG,OACiBhH,IAAnB+G,EACKD,EAAyBG,OAAeF,GACxCD,EAA+CI,KACtD,MAAMC,EAA2BrG,EAAYC,QACzCf,EAECe,EAA2C,gBAyBhD,OAxBIiG,GAAkB9C,cAAgBiD,IAEpCH,GAAuD,QAAI,QAC1BhH,IAA7BmH,EACFH,OAAmBhH,GAEnBgH,EAAmB,IAAIG,EAAyBN,GAChDG,EAAiBI,KAAaP,EAAMC,EAAQC,SAEvB/G,IAAnB+G,GACAD,EAAyBG,OAAiB,IAAIF,GAC9CC,EAEDF,EAAiCI,KAAcF,QAG3BhH,IAArBgH,IACFjG,EAAQ6F,EACNC,EACAG,EAAiBK,KAAUR,EAAO9F,EAA0BkB,QAC5D+E,EACAD,IAGGhG,CACT,CAOA,MAAMuG,EASJ,WAAApD,CAAYqD,EAAoBT,GAPhCzC,KAAOmD,KAA4B,GAKnCnD,KAAwBoD,UAAyBzH,EAG/CqE,KAAKqD,KAAaH,EAClBlD,KAAKsD,KAAWb,CACjB,CAGD,cAAIc,GACF,OAAOvD,KAAKsD,KAASC,UACtB,CAGD,QAAIC,GACF,OAAOxD,KAAKsD,KAASE,IACtB,CAID,CAAAC,CAAO3D,GACL,MACEO,IAAIG,QAACA,GACLP,MAAOA,GACLD,KAAKqD,KACHK,GAAY5D,GAAS6D,eAAiBvH,GAAGwH,WAAWpD,GAAS,GACnElC,EAAOiC,YAAcmD,EAErB,IAAI3D,EAAOzB,EAAOuC,WACdX,EAAY,EACZ2D,EAAY,EACZC,EAAe7D,EAAM,GAEzB,UAAwBtE,IAAjBmI,GAA4B,CACjC,GAAI5D,IAAc4D,EAAatC,MAAO,CACpC,IAAIgB,EAhwBO,IAiwBPsB,EAAapG,KACf8E,EAAO,IAAIuB,EACThE,EACAA,EAAKiE,YACLhE,KACAF,GAvwBW,IAywBJgE,EAAapG,KACtB8E,EAAO,IAAIsB,EAAarC,KACtB1B,EACA+D,EAAa9C,KACb8C,EAAanG,QACbqC,KACAF,GA1wBS,IA4wBFgE,EAAapG,OACtB8E,EAAO,IAAIyB,EAAYlE,EAAqBC,KAAMF,IAEpDE,KAAKmD,KAAQxD,KAAK6C,GAClBsB,EAAe7D,IAAQ4D,EACxB,CACG3D,IAAc4D,GAActC,QAC9BzB,EAAOzB,EAAOuC,WACdX,IAEH,CAKD,OADA5B,EAAOiC,YAAcnE,EACdsH,CACR,CAED,CAAAQ,CAAQtG,GACN,IAAIsB,EAAI,EACR,IAAK,MAAMsD,KAAQxC,KAAKmD,UACTxH,IAAT6G,SAUsC7G,IAAnC6G,EAAuB7E,SACzB6E,EAAuB2B,KAAWvG,EAAQ4E,EAAuBtD,GAIlEA,GAAMsD,EAAuB7E,QAASmB,OAAS,GAE/C0D,EAAK2B,KAAWvG,EAAOsB,KAG3BA,GAEH,EA8CH,MAAM6E,EAwBJ,QAAIP,GAIF,OAAOxD,KAAKsD,MAAUE,MAAiBxD,KAAKoE,IAC7C,CAeD,WAAAvE,CACEwE,EACAC,EACA7B,EACA3C,GA/COE,KAAItC,KA12BI,EA42BjBsC,KAAgBuE,KAAYpG,EA+B5B6B,KAAwBoD,UAAyBzH,EAgB/CqE,KAAKwE,KAAcH,EACnBrE,KAAKyE,KAAYH,EACjBtE,KAAKsD,KAAWb,EAChBzC,KAAKF,QAAUA,EAIfE,KAAKoE,KAAgBtE,GAAS4E,cAAe,CAK9C,CAoBD,cAAInB,GACF,IAAIA,EAAwBvD,KAAKwE,KAAajB,WAC9C,MAAMd,EAASzC,KAAKsD,KAUpB,YARa3H,IAAX8G,GACyB,KAAzBc,GAAYzC,WAKZyC,EAAcd,EAAwCc,YAEjDA,CACR,CAMD,aAAIc,GACF,OAAOrE,KAAKwE,IACb,CAMD,WAAIF,GACF,OAAOtE,KAAKyE,IACb,CAED,IAAAN,CAAWzH,EAAgBiI,EAAmC3E,MAM5DtD,EAAQ6F,EAAiBvC,KAAMtD,EAAOiI,GAClClI,EAAYC,GAIVA,IAAUyB,GAAoB,MAATzB,GAA2B,KAAVA,GACpCsD,KAAKuE,OAAqBpG,GAS5B6B,KAAK4E,OAEP5E,KAAKuE,KAAmBpG,GACfzB,IAAUsD,KAAKuE,MAAoB7H,IAAUuB,GACtD+B,KAAK6E,EAAYnI,QAGkCf,IAA3Ce,EAAqC,WAC/CsD,KAAK8E,EAAsBpI,QACWf,IAA5Be,EAAeoE,SAgBzBd,KAAK+E,EAAYrI,GACRG,EAAWH,GACpBsD,KAAKgF,EAAgBtI,GAGrBsD,KAAK6E,EAAYnI,EAEpB,CAEO,CAAAuI,CAAwBlF,GAC9B,OAAiBC,KAAKwE,KAAajB,WAAa2B,aAC9CnF,EACAC,KAAKyE,KAER,CAEO,CAAAM,CAAYrI,GACdsD,KAAKuE,OAAqB7H,IAC5BsD,KAAK4E,OAoCL5E,KAAKuE,KAAmBvE,KAAKiF,EAAQvI,GAExC,CAEO,CAAAmI,CAAYnI,GAKhBsD,KAAKuE,OAAqBpG,GAC1B1B,EAAYuD,KAAKuE,MAECvE,KAAKwE,KAAaR,YAcrB7B,KAAOzF,EAsBpBsD,KAAK+E,EAAY3I,EAAE+I,eAAezI,IAUtCsD,KAAKuE,KAAmB7H,CACzB,CAEO,CAAAoI,CACNM,GAGA,MAAMxH,OAACA,EAAQC,WAAgBH,GAAQ0H,EAKjClC,EACY,iBAATxF,EACHsC,KAAKqF,KAAcD,SACNzJ,IAAZ+B,EAAK2C,KACH3C,EAAK2C,GAAKT,EAASU,cAClB/B,EAAwBb,EAAK4H,EAAG5H,EAAK4H,EAAE,IACvCtF,KAAKF,UAETpC,GAEN,GAAKsC,KAAKuE,MAAuClB,OAAeH,EAU7DlD,KAAKuE,KAAsCL,EAAQtG,OAC/C,CACL,MAAM2H,EAAW,IAAItC,EAAiBC,EAAsBlD,MACtD0D,EAAW6B,EAAS9B,EAAOzD,KAAKF,SAWtCyF,EAASrB,EAAQtG,GAWjBoC,KAAK+E,EAAYrB,GACjB1D,KAAKuE,KAAmBgB,CACzB,CACF,CAID,IAAAF,CAAcD,GACZ,IAAIlC,EAAW9E,EAAcoH,IAAIJ,EAAOzH,SAIxC,YAHiBhC,IAAbuH,GACF9E,EAAcqH,IAAIL,EAAOzH,QAAUuF,EAAW,IAAItD,EAASwF,IAEtDlC,CACR,CAEO,CAAA8B,CAAgBtI,GAWjBC,EAAQqD,KAAKuE,QAChBvE,KAAKuE,KAAmB,GACxBvE,KAAK4E,QAKP,MAAMc,EAAY1F,KAAKuE,KACvB,IACIoB,EADA9B,EAAY,EAGhB,IAAK,MAAM+B,KAAQlJ,EACbmH,IAAc6B,EAAU5G,OAK1B4G,EAAU/F,KACPgG,EAAW,IAAI5B,EACd/D,KAAKiF,EAAQ1I,KACbyD,KAAKiF,EAAQ1I,KACbyD,KACAA,KAAKF,UAKT6F,EAAWD,EAAU7B,GAEvB8B,EAASxB,KAAWyB,GACpB/B,IAGEA,EAAY6B,EAAU5G,SAExBkB,KAAK4E,KACHe,GAAiBA,EAASlB,KAAYT,YACtCH,GAGF6B,EAAU5G,OAAS+E,EAEtB,CAaD,IAAAe,CACEiB,EAA+B7F,KAAKwE,KAAaR,YACjD8B,GAGA,IADA9F,KAAK+F,QAA4B,GAAO,EAAMD,GACvCD,GAASA,IAAU7F,KAAKyE,MAAW,CACxC,MAAMuB,EAASH,EAAQ7B,YACjB6B,EAAoBI,SAC1BJ,EAAQG,CACT,CACF,CAQD,YAAAE,CAAaxB,QACW/I,IAAlBqE,KAAKsD,OACPtD,KAAKoE,KAAgBM,EACrB1E,KAAK+F,OAA4BrB,GAOpC,EA2BH,MAAM7C,EA2BJ,WAAIE,GACF,OAAO/B,KAAKmG,QAAQpE,OACrB,CAGD,QAAIyB,GACF,OAAOxD,KAAKsD,KAASE,IACtB,CAED,WAAA3D,CACEsG,EACAnF,EACArD,EACA8E,EACA3C,GAxCOE,KAAItC,KA3zCQ,EA20CrBsC,KAAgBuE,KAA6BpG,EAM7C6B,KAAwBoD,UAAyBzH,EAoB/CqE,KAAKmG,QAAUA,EACfnG,KAAKgB,KAAOA,EACZhB,KAAKsD,KAAWb,EAChBzC,KAAKF,QAAUA,EACXnC,EAAQmB,OAAS,GAAoB,KAAfnB,EAAQ,IAA4B,KAAfA,EAAQ,IACrDqC,KAAKuE,KAAuB3H,MAAMe,EAAQmB,OAAS,GAAGsH,KAAK,IAAIC,QAC/DrG,KAAKrC,QAAUA,GAEfqC,KAAKuE,KAAmBpG,CAK3B,CAwBD,IAAAgG,CACEzH,EACAiI,EAAmC3E,KACnCsG,EACAC,GAEA,MAAM5I,EAAUqC,KAAKrC,QAGrB,IAAI6I,GAAS,EAEb,QAAgB7K,IAAZgC,EAEFjB,EAAQ6F,EAAiBvC,KAAMtD,EAAOiI,EAAiB,GACvD6B,GACG/J,EAAYC,IACZA,IAAUsD,KAAKuE,MAAoB7H,IAAUuB,EAC5CuI,IACFxG,KAAKuE,KAAmB7H,OAErB,CAEL,MAAMkB,EAASlB,EAGf,IAAIwC,EAAGuH,EACP,IAHA/J,EAAQiB,EAAQ,GAGXuB,EAAI,EAAGA,EAAIvB,EAAQmB,OAAS,EAAGI,IAClCuH,EAAIlE,EAAiBvC,KAAMpC,EAAO0I,EAAcpH,GAAIyF,EAAiBzF,GAEjEuH,IAAMxI,IAERwI,EAAKzG,KAAKuE,KAAoCrF,IAEhDsH,KACG/J,EAAYgK,IAAMA,IAAOzG,KAAKuE,KAAoCrF,GACjEuH,IAAMtI,EACRzB,EAAQyB,EACCzB,IAAUyB,IACnBzB,IAAU+J,GAAK,IAAM9I,EAAQuB,EAAI,IAIlCc,KAAKuE,KAAoCrF,GAAKuH,CAElD,CACGD,IAAWD,GACbvG,KAAK0G,EAAahK,EAErB,CAGD,CAAAgK,CAAahK,GACPA,IAAUyB,EACN6B,KAAKmG,QAAqBrE,gBAAgB9B,KAAKgB,MAoB/ChB,KAAKmG,QAAqBQ,aAC9B3G,KAAKgB,KACJtE,GAAS,GAGf,EAIH,MAAMgF,UAAqBG,EAA3B,WAAAhC,uBACoBG,KAAItC,KA39CF,CAo/CrB,CAtBU,CAAAgJ,CAAahK,GAoBnBsD,KAAKmG,QAAgBnG,KAAKgB,MAAQtE,IAAUyB,OAAUxC,EAAYe,CACpE,EAIH,MAAMiF,UAA6BE,EAAnC,WAAAhC,uBACoBG,KAAItC,KAv/CO,CAwgD9B,CAdU,CAAAgJ,CAAahK,GASdsD,KAAKmG,QAAqBS,gBAC9B5G,KAAKgB,OACHtE,GAASA,IAAUyB,EAExB,EAkBH,MAAMyD,UAAkBC,EAGtB,WAAAhC,CACEsG,EACAnF,EACArD,EACA8E,EACA3C,GAEA+G,MAAMV,EAASnF,EAAMrD,EAAS8E,EAAQ3C,GATtBE,KAAItC,KAzhDL,CA2iDhB,CAKQ,IAAAyG,CACP2C,EACAnC,EAAmC3E,MAInC,IAFA8G,EACEvE,EAAiBvC,KAAM8G,EAAanC,EAAiB,IAAMxG,KACzCF,EAClB,OAEF,MAAM8I,EAAc/G,KAAKuE,KAInByC,EACHF,IAAgB3I,GAAW4I,IAAgB5I,GAC3C2I,EAAyCG,UACvCF,EAAyCE,SAC3CH,EAAyCI,OACvCH,EAAyCG,MAC3CJ,EAAyCK,UACvCJ,EAAyCI,QAIxCC,EACJN,IAAgB3I,IACf4I,IAAgB5I,GAAW6I,GAa1BA,GACFhH,KAAKmG,QAAQkB,oBACXrH,KAAKgB,KACLhB,KACA+G,GAGAK,GACFpH,KAAKmG,QAAQmB,iBACXtH,KAAKgB,KACLhB,KACA8G,GAGJ9G,KAAKuE,KAAmBuC,CACzB,CAED,WAAAS,CAAYC,GAC2B,mBAA1BxH,KAAKuE,KACdvE,KAAKuE,KAAiBkD,KAAKzH,KAAKF,SAAS4H,MAAQ1H,KAAKmG,QAASqB,GAE9DxH,KAAKuE,KAAyCgD,YAAYC,EAE9D,EAIH,MAAMvD,EAiBJ,WAAApE,CACSsG,EACP1D,EACA3C,GAFOE,KAAOmG,QAAPA,EAjBAnG,KAAItC,KAlnDM,EA8nDnBsC,KAAwBoD,UAAyBzH,EAS/CqE,KAAKsD,KAAWb,EAChBzC,KAAKF,QAAUA,CAChB,CAGD,QAAI0D,GACF,OAAOxD,KAAKsD,KAASE,IACtB,CAED,IAAAW,CAAWzH,GAQT6F,EAAiBvC,KAAMtD,EACxB,EAqBU,MAAAiL,EAAO,CAElBC,EAAuBhM,EACvBiM,EAAShM,EACTiM,EAAc5L,EACd6L,EAhsDkB,EAisDlBC,EAAkBpJ,EAElBqJ,EAAmBhF,EACnBiF,EAAarL,EACbsL,EAAmB5F,EACnB6F,EAAYrE,EACZsE,EAAgBxG,EAChByG,EAAuB3G,EACvB4G,EAAY3G,EACZ4G,EAAe9G,EACf+G,EAAcxE,GAIVyE,EAEFtN,EAAOuN,uBACXD,IAAkB9I,EAAUmE,IAI3B3I,EAAOwN,kBAAoB,IAAIjJ,KAAK,SAoCxB,MAAAkJ,EAAS,CACpBnM,EACAoM,EACAhJ,KAUA,MAAMiJ,EAAgBjJ,GAASkJ,cAAgBF,EAG/C,IAAItG,EAAmBuG,EAAkC,WAUzD,QAAapN,IAAT6G,EAAoB,CACtB,MAAM8B,EAAUxE,GAASkJ,cAAgB,KAGxCD,EAAkC,WAAIvG,EAAO,IAAIuB,EAChD+E,EAAU5D,aAAa3I,IAAgB+H,GACvCA,OACA3I,EACAmE,GAAW,CAAE,EAEhB,CAWD,OAVA0C,EAAK2B,KAAWzH,GAUT8F,CAAgB"}