/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
var i,n=new Set,o=new Map,t=function(i,t){var d,v;if(void 0!==window.ShadyCSS&&(!window.ShadyCSS.nativeShadow||window.ShadyCSS.ApplyShim)){var l=(null===(d=window.ShadyDOM)||void 0===d?void 0:d.inUse)&&!0===(null===(v=window.ShadyDOM)||void 0===v?void 0:v.noPatch)?window.ShadyDOM.wrap:function(i){return i},u=function(i){return void 0!==i&&!n.has(i)},r=function(i){var n=o.get(i);return void 0===n&&o.set(i,n=[]),n},w=new Map,a=i.createElement;i.createElement=function(n,o){var t=a.call(i,n,o),d=null==o?void 0:o.scope;if(void 0!==d&&(window.ShadyCSS.nativeShadow||window.ShadyCSS.prepareTemplateDom(t,d),u(d))){var v=r(d),l=t.content.querySelectorAll("style");v.push.apply(v,Array.from(l).map((function(i){var n;return null===(n=i.parentNode)||void 0===n||n.removeChild(i),i.textContent})))}return t};var e=document.createDocumentFragment(),s=document.createComment(""),h=t.prototype,c=h._$AI;h._$AI=function(i,t){var d,v;void 0===t&&(t=this);var w=l(this._$AA).parentNode,a=null===(d=this.options)||void 0===d?void 0:d.scope;if(w instanceof ShadowRoot&&u(a)){var h=this._$AA,f=this._$AB;e.appendChild(s),this._$AA=s,this._$AB=null,c.call(this,i,t);var m=(null==i?void 0:i._$litType$)?this._$AH._$AD.el:document.createElement("template");if(function(i,t){var d,v=r(i),l=0!==v.length;l&&((d=document.createElement("style")).textContent=v.join("\n"),t.content.appendChild(d)),n.add(i),o.delete(i),window.ShadyCSS.prepareTemplateStyles(t,i),l&&window.ShadyCSS.nativeShadow&&null!==(d=t.content.querySelector("style"))&&t.content.appendChild(d)}(a,m),e.removeChild(s),null===(v=window.ShadyCSS)||void 0===v?void 0:v.nativeShadow){var y=m.content.querySelector("style");null!==y&&e.appendChild(y.cloneNode(!0))}w.insertBefore(e,f),this._$AA=h,this._$AB=f}else c.call(this,i,t)},h._$AC=function(n){var o,t=null===(o=this.options)||void 0===o?void 0:o.scope,d=w.get(t);void 0===d&&w.set(t,d=new Map);var v=d.get(n.strings);return void 0===v&&d.set(n.strings,v=new i(n,this.options)),v}}};t.noPatchSupported=!0,null!==(i=globalThis.litHtmlPolyfillSupportDevMode)&&void 0!==i||(globalThis.litHtmlPolyfillSupportDevMode=t);
//# sourceMappingURL=polyfill-support.js.map
