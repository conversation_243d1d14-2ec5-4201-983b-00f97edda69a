!function(i){"function"==typeof define&&define.amd?define(i):i()}((function(){"use strict";
/**
     * @license
     * Copyright 2017 Google LLC
     * SPDX-License-Identifier: BSD-3-Clause
     */var i,n=new Set,o=new Map;null!==(i=globalThis.litHtmlPolyfillSupport)&&void 0!==i||(globalThis.litHtmlPolyfillSupport=function(i,t){if(void 0!==window.ShadyCSS&&(!window.ShadyCSS.nativeShadow||window.ShadyCSS.ApplyShim)){var d=function(i){return void 0!==i&&!n.has(i)},e=function(i){var n=o.get(i);return void 0===n&&o.set(i,n=[]),n},v=new Map,u=i.createElement;i.createElement=function(n,o){var t=u.call(i,n,o),v=null==o?void 0:o.scope;if(void 0!==v&&(window.ShadyCSS.nativeShadow||window.ShadyCSS.prepareTemplateDom(t,v),d(v))){var l=e(v),r=t.content.querySelectorAll("style");l.push.apply(l,Array.from(r).map((function(i){var n;return null===(n=i.parentNode)||void 0===n||n.removeChild(i),i.textContent})))}return t};var l=document.createDocumentFragment(),r=document.createComment(""),a=t.prototype,s=a._$AI;a._$AI=function(i,t){var v,u;void 0===t&&(t=this);var a=this._$AA.parentNode,w=null===(v=this.options)||void 0===v?void 0:v.scope;if(a instanceof ShadowRoot&&d(w)){var f=this._$AA,c=this._$AB;l.appendChild(r),this._$AA=r,this._$AB=null,s.call(this,i,t);var h=(null==i?void 0:i._$litType$)?this._$AH._$AD.el:document.createElement("template");if(function(i,t){var d,v=e(i),u=0!==v.length;u&&((d=document.createElement("style")).textContent=v.join("\n"),t.content.appendChild(d)),n.add(i),o.delete(i),window.ShadyCSS.prepareTemplateStyles(t,i),u&&window.ShadyCSS.nativeShadow&&null!==(d=t.content.querySelector("style"))&&t.content.appendChild(d)}(w,h),l.removeChild(r),null===(u=window.ShadyCSS)||void 0===u?void 0:u.nativeShadow){var y=h.content.querySelector("style");null!==y&&l.appendChild(y.cloneNode(!0))}a.insertBefore(l,c),this._$AA=f,this._$AB=c}else s.call(this,i,t)},a._$AC=function(n){var o,t=null===(o=this.options)||void 0===o?void 0:o.scope,d=v.get(t);void 0===d&&v.set(t,d=new Map);var e=d.get(n.strings);return void 0===e&&d.set(n.strings,e=new i(n,this.options)),e}}})}));
//# sourceMappingURL=polyfill-support.js.map
