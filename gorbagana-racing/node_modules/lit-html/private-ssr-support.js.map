{"version": 3, "file": "private-ssr-support.js", "sources": ["src/private-ssr-support.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nimport {\n  Directive,\n  PartInfo,\n  DirectiveClass,\n  DirectiveResult,\n} from './directive.js';\nimport {\n  _$LH as p,\n  AttributePart,\n  noChange,\n  Part,\n  Disconnectable,\n} from './lit-html.js';\n\nimport type {\n  PropertyPart,\n  ChildPart,\n  BooleanAttributePart,\n  EventPart,\n  ElementPart,\n  TemplateInstance,\n} from './lit-html.js';\n\n// Contains either the minified or unminified `_$resolve` Directive method name.\nlet resolveMethodName: Extract<keyof Directive, '_$resolve'> | null = null;\n\n/**\n * END USERS SHOULD NOT RELY ON THIS OBJECT.\n *\n * We currently do not make a mangled rollup build of the lit-ssr code. In order\n * to keep a number of (otherwise private) top-level exports mangled in the\n * client side code, we export a _$LH object containing those members (or\n * helper methods for accessing private fields of those members), and then\n * re-export them for use in lit-ssr. This keeps lit-ssr agnostic to whether the\n * client-side code is being used in `dev` mode or `prod` mode.\n * @private\n */\nexport const _$LH = {\n  boundAttributeSuffix: p._boundAttributeSuffix,\n  marker: p._marker,\n  markerMatch: p._markerMatch,\n  HTML_RESULT: p._HTML_RESULT,\n  getTemplateHtml: p._getTemplateHtml,\n  overrideDirectiveResolve: (\n    directiveClass: new (part: PartInfo) => Directive & {render(): unknown},\n    resolveOverrideFn: (directive: Directive, values: unknown[]) => unknown\n  ) =>\n    class extends directiveClass {\n      override _$resolve(\n        this: Directive,\n        _part: Part,\n        values: unknown[]\n      ): unknown {\n        return resolveOverrideFn(this, values);\n      }\n    },\n  patchDirectiveResolve: (\n    directiveClass: typeof Directive,\n    resolveOverrideFn: (\n      this: Directive,\n      _part: Part,\n      values: unknown[]\n    ) => unknown\n  ) => {\n    if (directiveClass.prototype._$resolve !== resolveOverrideFn) {\n      resolveMethodName ??= directiveClass.prototype._$resolve\n        .name as NonNullable<typeof resolveMethodName>;\n      for (\n        let proto = directiveClass.prototype;\n        proto !== Object.prototype;\n        proto = Object.getPrototypeOf(proto)\n      ) {\n        if (proto.hasOwnProperty(resolveMethodName)) {\n          proto[resolveMethodName] = resolveOverrideFn;\n          return;\n        }\n      }\n      // Nothing was patched which indicates an error. The most likely error is\n      // that somehow both minified and unminified lit code passed through this\n      // codepath. This is possible as lit-labs/ssr contains its own lit-html\n      // module as a dependency for server rendering client Lit code. If a\n      // client contains multiple duplicate Lit modules with minified and\n      // unminified exports, we currently cannot handle both.\n      throw new Error(\n        `Internal error: It is possible that both dev mode and production mode` +\n          ` Lit was mixed together during SSR. Please comment on the issue: ` +\n          `https://github.com/lit/lit/issues/4527`\n      );\n    }\n  },\n  setDirectiveClass(value: DirectiveResult, directiveClass: DirectiveClass) {\n    // This property needs to remain unminified.\n    value['_$litDirective$'] = directiveClass;\n  },\n  getAttributePartCommittedValue: (\n    part: AttributePart,\n    value: unknown,\n    index: number | undefined\n  ) => {\n    // Use the part setter to resolve directives/concatenate multiple parts\n    // into a final value (captured by passing in a commitValue override)\n    let committedValue: unknown = noChange;\n    // Note that _commitValue need not be in `stableProperties` because this\n    // method is only run on `AttributePart`s created by lit-ssr using the same\n    // version of the library as this file\n    part._commitValue = (value: unknown) => (committedValue = value);\n    part._$setValue(value, part, index);\n    return committedValue;\n  },\n  connectedDisconnectable: (props?: object): Disconnectable => ({\n    ...props,\n    _$isConnected: true,\n  }),\n  resolveDirective: p._resolveDirective,\n  AttributePart: p._AttributePart,\n  PropertyPart: p._PropertyPart as typeof PropertyPart,\n  BooleanAttributePart: p._BooleanAttributePart as typeof BooleanAttributePart,\n  EventPart: p._EventPart as typeof EventPart,\n  ElementPart: p._ElementPart as typeof ElementPart,\n  TemplateInstance: p._TemplateInstance as typeof TemplateInstance,\n  isIterable: p._isIterable,\n  ChildPart: p._ChildPart as typeof ChildPart,\n};\n"], "names": ["resolveMethodName", "_$LH", "boundAttributeSuffix", "p", "_boundAttributeSuffix", "marker", "_marker", "markerMatch", "_markerMatch", "HTML_RESULT", "_HTML_RESULT", "getTemplateHtml", "_getTemplateHtml", "overrideDirectiveResolve", "directiveClass", "resolveOverrideFn", "_$resolve", "_part", "values", "this", "patchDirectiveResolve", "prototype", "name", "proto", "Object", "getPrototypeOf", "hasOwnProperty", "Error", "setDirectiveClass", "value", "getAttributePartCommittedValue", "part", "index", "committedValue", "noChange", "_commitValue", "_$setValue", "connectedDisconnectable", "props", "_$isConnected", "resolveDirective", "_resolveDirective", "AttributePart", "_AttributePart", "PropertyPart", "_PropertyPart", "BooleanAttributePart", "_BooleanAttributePart", "EventPart", "_EventPart", "ElementPart", "_ElementPart", "TemplateInstance", "_TemplateInstance", "isIterable", "_isIterable", "<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;AA8BA,IAAIA,EAAkE,KAazD,MAAAC,EAAO,CAClBC,qBAAsBC,EAAEC,EACxBC,OAAQF,EAAEG,EACVC,YAAaJ,EAAEK,EACfC,YAAaN,EAAEO,EACfC,gBAAiBR,EAAES,EACnBC,yBAA0B,CACxBC,EACAC,IAEA,cAAcD,EACH,IAAAE,CAEPC,EACAC,GAEA,OAAOH,EAAkBI,KAAMD,EAChC,GAELE,sBAAuB,CACrBN,EACAC,KAMA,GAAID,EAAeO,UAAUL,OAAcD,EAAmB,CAC5Df,IAAsBc,EAAeO,UAAUL,KAC5CM,KACH,IACE,IAAIC,EAAQT,EAAeO,UAC3BE,IAAUC,OAAOH,UACjBE,EAAQC,OAAOC,eAAeF,GAE9B,GAAIA,EAAMG,eAAe1B,GAEvB,YADAuB,EAAMvB,GAAqBe,GAU/B,MAAUY,MACR,+KAIH,GAEH,iBAAAC,CAAkBC,EAAwBf,GAExCe,EAAuB,gBAAIf,CAC5B,EACDgB,+BAAgC,CAC9BC,EACAF,EACAG,KAIA,IAAIC,EAA0BC,EAM9B,OAFAH,EAAKI,EAAgBN,GAAoBI,EAAiBJ,EAC1DE,EAAKK,KAAWP,EAAOE,EAAMC,GACtBC,CAAc,EAEvBI,wBAA0BC,IAAoC,IACzDA,EACHC,MAAe,IAEjBC,iBAAkBrC,EAAEsC,EACpBC,cAAevC,EAAEwC,EACjBC,aAAczC,EAAE0C,EAChBC,qBAAsB3C,EAAE4C,EACxBC,UAAW7C,EAAE8C,EACbC,YAAa/C,EAAEgD,EACfC,iBAAkBjD,EAAEkD,EACpBC,WAAYnD,EAAEoD,EACdC,UAAWrD,EAAEsD"}