{"version": 3, "file": "importFile.worker.worker.js", "mappings": "wCACAA,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQE,0BAA4BF,EAAQG,2BAA6BH,EAAQI,yBAA2BJ,EAAQK,sBAAmB,EACvI,MAAMC,EAAYC,EAAQ,KACpBC,EAAUD,EAAQ,KAClBE,EAAqBF,EAAQ,KAC7BG,EAAmBH,EAAQ,KAajCP,EAAQK,iBAZR,SAA0BM,GACtB,IAAKC,MAAMC,QAAQF,GACf,OAAO,EACX,GAAIA,EAAWG,OAAS,EACpB,OAAO,EACX,MAAMC,EAAQJ,EAAW,GACzB,MAAM,QAASI,GAAS,QAASA,GAAS,OAAQA,GAAS,QAASA,KAE/DJ,EAAWK,MAAKC,GAAgB,eAAXA,EAAEC,MAAoC,YAAXD,EAAEC,MAAiC,iBAAXD,EAAEC,MAGlF,EAuFDlB,EAAQI,yBArFR,SAAkCe,EAAQC,GAGtC,MAAMC,EAAiB,IAAIC,IAErBC,EAAa,IAAID,IAEjBE,EAAqB,IAAIF,IAG/Bd,EAAQiB,OAAON,GAAQF,GAAKA,EAAES,KAC9B,IAAK,IAAIC,KAASR,EAAQ,CACtB,GAAmB,eAAfQ,EAAMT,KAAuB,CAC7B,MAAMU,EAAU,GAAED,EAAME,OAAOF,EAAMG,MAC/BC,EAAKJ,EAAMI,IAAMH,EACvBP,EAAeW,IAAID,EAAIJ,EAAMM,KAAKC,KAAKC,YACvCZ,EAAWS,IAAID,EAAIH,EACtB,CACD,GAAmB,YAAfD,EAAMT,KAAoB,CAC1B,MAAMU,EAAU,GAAED,EAAME,OAAOF,EAAMG,MACrCT,EAAeW,IAAIL,EAAMI,IAAMH,EAAQ9B,OAAOsC,OAAO,CAAEC,UAAW,EAAGC,QAAS,EAAGC,MAAO,GAAIC,QAAS,GAAIC,WAAY,IAAMd,EAAMM,KAAKC,OAClIP,EAAMI,IACNR,EAAWS,IAAIL,EAAMI,GAAK,GAAEJ,EAAME,OAAOF,EAAMG,MAEtD,CAID,GAHmB,gBAAfH,EAAMT,MACNM,EAAmBQ,IAAK,GAAEL,EAAME,OAAOF,EAAMG,MAAOH,EAAMM,KAAKf,MAEhD,iBAAfS,EAAMT,KAAyB,CAC/B,MAAMU,EAAU,GAAED,EAAME,OAAOF,EAAMG,MAC/BK,EAAad,EAAeqB,IAAIf,EAAMI,IAAMH,GAClD,GAAIO,EAAY,CACZ,MAAMQ,EAAQhB,EAAMM,KAAKC,KACrBS,EAAMR,aACFQ,EAAMR,WAAWI,QACjBJ,EAAWI,MAAQJ,EAAWI,MAAMK,OAAOD,EAAMR,WAAWI,QAE5DI,EAAMR,WAAWK,UACjBL,EAAWK,QAAUL,EAAWK,QAAQI,OAAOD,EAAMR,WAAWK,WAGpEG,EAAMF,aACNN,EAAWM,WAAaN,EAAWM,WAAWG,OAAOD,EAAMF,aAExC,MAAnBE,EAAMN,YACNF,EAAWE,UAAYM,EAAMN,WAEZ,MAAjBM,EAAML,UACNH,EAAWG,QAAUK,EAAML,QAElC,MAEGO,QAAQC,KAAM,wDAAuDnB,EAAMI,IAAMH,IAExF,CACJ,CACD,GAAIP,EAAe0B,KAAO,EAAG,CACzB,MAAMC,EAAW,GACjB,IAAIC,EAAc,EAqBlB,OApBAzC,EAAQ0C,UAAU7B,EAAe8B,QAAQC,IACrC,IAAIC,EAAa,KACbzB,EAASL,EAAWmB,IAAIU,GACxBxB,IACAyB,EAAa7B,EAAmBkB,IAAId,IAAW,MAInD,MAAM0B,EAAUnD,EAA2BkB,EAAeqB,IAAIU,IAC1DC,GAAchC,EAAe0B,KAAO,GACpCO,EAAQC,QAAS,GAAEnC,OAAciC,KACd,mBAAfA,IACAJ,EAAcD,EAASlC,SAI3BwC,EAAQC,QAAS,GAAEnC,KAEvB4B,EAASQ,KAAKF,EAAd,IAEG,CAAEpC,KAAME,EAAU6B,cAAaD,WACzC,CAEG,MAAM,IAAIS,MAAM,yCAEvB,EAED,MAAMC,EAAuB,IAAIpC,IACjC,SAASqC,EAAsBC,GAC3B,OAAOpD,EAAQqD,YAAYH,EAAsBE,GAAWA,IACxD,MAAM1C,EAAO0C,EAAUE,cAAgB,cACjCC,EAAOH,EAAUI,IACjBC,EAAOL,EAAUM,WACjBC,EAAMP,EAAUQ,aACtB,MAAO,CACHC,IAAM,GAAEnD,KAAQ6C,KAAQE,KAAQE,IAChCjD,OACA6C,OACAE,OACAE,MALJ,GAQP,CACD,SAASG,EAAqBV,GAC1B,MAAM,aAAEE,EAAF,IAAgBE,GAAQJ,EAC9B,MAAY,oBAARI,GAMoB,WAAjBF,GAA8C,WAAjBA,CACvC,CACD,SAASS,EAAgCT,GACrC,MAAwB,wBAAjBA,GAA2D,cAAjBA,CACpD,CACD,SAAS3D,EAA2BqE,GAChC,MAAMlB,EAAU,IAAIhD,EAAUmE,uBAAuBD,EAAclC,QAAUkC,EAAcnC,WACrFqC,EAAW,IAAIpD,IACrB,IAAK,IAAIqD,KAAQH,EAAcjC,MAC3BmC,EAAS1C,IAAI2C,EAAK5C,GAAI4C,GAE1B,IAAK,IAAIA,KAAQH,EAAcjC,MAI3B,GAH2B,iBAAhBoC,EAAKC,SACZD,EAAKC,OAASF,EAAShC,IAAIiC,EAAKC,SAE/BD,EAAKE,SAEV,IAAK,IAAIC,KAAWH,EAAKE,SAAU,CAC/B,MAAME,EAAQL,EAAShC,IAAIoC,GACtBC,IAELA,EAAMH,OAASD,EAClB,CAEL,MAAMnC,EAAU,GACVwC,EAAc,GAGpB,IAAIC,EAAUT,EAAc/B,WAAW,GAGnCyC,EAAmBD,EACnBE,EAAaC,IAGjB,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAchC,QAAQ1B,OAAQuE,IAAK,CACnD,MAAMC,EAASd,EAAchC,QAAQ6C,GACjCC,GAAUH,IACV3C,EAAQgB,KAAK8B,GACTL,EAAUC,EACVF,EAAYxB,KAAK0B,IAGjBF,EAAYxB,KAAKyB,GACjBC,EAAmBD,IAGvBI,IAAMb,EAAchC,QAAQ1B,OAAS,EAChCyE,MAAMJ,KACP3C,EAAQgB,KAAK2B,GACTF,EAAUC,EACVF,EAAYxB,KAAK0B,IAGjBF,EAAYxB,KAAKyB,GACjBC,EAAmBD,KAM3BA,GADkBT,EAAc/B,WAAW4C,EAAI,GAE/CF,EAAaG,EAEpB,CACD,IAAIE,EAAY,GAChB,IAAK,IAAIH,EAAI,EAAGA,EAAI7C,EAAQ1B,OAAQuE,IAAK,CACrC,MAAMpF,EAAQ+E,EAAYK,GACpBC,EAAS9C,EAAQ6C,GACvB,IAAII,EAAWf,EAAShC,IAAI4C,GAC5B,IAAKG,EACD,SAEJ,IAAIC,EAAM,KAGV,IAAKA,EAAMD,EAAUC,IAAmC,IAA5BF,EAAUG,QAAQD,GAAaA,EAAMnB,EAAgCmB,EAAI9B,UAAUE,cACzGtD,EAAQoF,OAAOJ,GACfE,EAAId,QAAU,MAEpB,KAAOY,EAAU1E,OAAS,GAAKN,EAAQoF,OAAOJ,IAAcE,GAAK,CAC7D,MACMG,EAAQlC,EADM6B,EAAUM,MACkBlC,WAChDN,EAAQyC,WAAWF,EAAO5F,EAC7B,CAED,MAAM+F,EAAS,GACf,IAAK,IAAIrB,EAAOc,EAAUd,GAAQA,GAAQe,IAAQpB,EAAqBK,EAAKf,WAE5Ee,EAAOJ,EAAgCI,EAAKf,UAAUE,cAChDtD,EAAQoF,OAAOJ,GACfb,EAAKC,QAAU,KACjBoB,EAAOxC,KAAKmB,GAEhBqB,EAAOC,UACP,IAAK,IAAItB,KAAQqB,EACb1C,EAAQ4C,WAAWvC,EAAsBgB,EAAKf,WAAY3D,GAE9DuF,EAAYA,EAAU5C,OAAOoD,EAChC,CAED,IAAK,IAAIX,EAAIG,EAAU1E,OAAS,EAAGuE,GAAK,EAAGA,IACvC/B,EAAQyC,WAAWpC,EAAsB6B,EAAUH,GAAGzB,WAAYpD,EAAQoF,OAAOZ,IAGrF,OADA1B,EAAQ6C,kBAAkB,IAAI1F,EAAmB2F,cAAc,iBACxD9C,EAAQ+C,OAClB,CACDrG,EAAQG,2BAA6BA,EAIrCH,EAAQE,0BAHR,SAAmCoG,GAC/B,OAAOnG,EAA2BO,EAAiB6F,kBAAkBD,GACxE,C,2BC7OD,SAASE,EAAYC,GACjB,MAAMlE,EAAQ,GAiBd,OAhBA,SAASmE,EAAM/B,GACXpC,EAAMiB,KAAK,CACPzB,GAAI4C,EAAK5C,GACT6B,UAAW,CACPQ,aAAc,EACdN,aAAca,EAAKb,aACnBI,WAAYS,EAAKT,WACjByC,SAAUhC,EAAKgC,SACf3C,IAAKW,EAAKX,KAEd4C,SAAUjC,EAAKiC,SACf/B,SAAUF,EAAKE,SAASgC,KAAI9B,GAASA,EAAMhD,OAE/C4C,EAAKE,SAASiC,QAAQJ,EACzB,CACDA,CAAMD,GACClE,CACV,CArBDzC,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQuG,uBAAoB,EAwC5BvG,EAAQuG,kBAVR,SAA2BD,GAEvB,MAAO,CACH9D,QAAS8D,EAAQ9D,QACjBH,UAA+B,IAApBiE,EAAQjE,UACnBC,QAA2B,IAAlBgE,EAAQhE,QACjBC,MAAOiE,EAAYF,EAAQS,MAC3BtE,YAhBoBuE,EAgBWV,EAAQU,WAhBP3E,EAgBmBiE,EAAQjE,UAfxD2E,EAAWH,KAAI,CAACI,EAAWC,IAEvBD,GADyB,IAAVC,EAA0B,IAAZ7E,EAAsB2E,EAAWE,EAAQ,QAFrF,IAA4BF,EAAY3E,CAkBvC,C,2BCtCD,IAAI8E,EAFJrH,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQoH,iBAAc,EAEtB,MAAMC,EAAQ,IAAI/F,IAqBlBtB,EAAQoH,YAlBR,SAAqBlG,GACjB,GAAIA,EAAKoG,WAAW,OAAQ,CACxB,IAAIC,EAASF,EAAM3E,IAAIxB,QACRsG,IAAXD,IAIKJ,IACDA,EAAU,IAAIM,SAAS,UAAWC,EAAxB,IAEdH,EAASJ,EAAQjG,EAAKyG,MAAM,IAC5BJ,EAAoB,WAAXA,EAAsBrG,EAAOqG,EACtCF,EAAMrF,IAAId,EAAMqG,IARhBrG,EAAOqG,CAWd,CACD,OAAOrG,CACV,EAGD,MAAMwG,EAAQ,4snK,6BC1Bd5H,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQ4H,gBAAa,EACrB,MAAMpH,EAAUD,EAAQ,KAClBsH,EAAStH,EAAQ,KAyGvBP,EAAQ4H,WAxGR,MACIE,YAAYC,GACRC,KAAKD,OAASA,EAEdC,KAAKC,OAAS,GACdD,KAAKE,YAAc,EACnBF,KAAKG,cAAgB,EACrB,MAAMC,EAAQ,GAedJ,KAAKG,cAAgBE,IAarBL,KAAKE,YAAcH,EAAOO,iBAC1BP,EAAOQ,aA5BW,CAAC5D,EAAM1E,KACrB,MAAM2E,EAASpE,EAAQoF,OAAOwC,GACxBvC,EAAQ,CACVlB,OACAC,SACAC,SAAU,GACV2D,MAAOvI,EACPwI,IAAKxI,GAEL2E,GACAA,EAAOC,SAASrB,KAAKqC,GAEzBuC,EAAM5E,KAAKqC,EAAX,IAGe,CAAClB,EAAM1E,KACtB4C,QAAQ6F,OAAON,EAAMtH,OAAS,GAC9B,MAAM2E,EAAW2C,EAAMtC,MAEvB,GADAL,EAASgD,IAAMxI,EACXwF,EAASgD,IAAMhD,EAAS+C,OAAU,EAClC,OACJ,MAAMG,EAAaP,EAAMtH,OACzB,KAAOkH,KAAKC,OAAOnH,QAAU6H,GACzBX,KAAKC,OAAOzE,KAAK,IACrBwE,KAAKC,OAAOU,GAAYnF,KAAKiC,GAC7BuC,KAAKG,cAAgBS,KAAKC,IAAIb,KAAKG,cAAe1C,EAASgD,IAAMhD,EAAS+C,MAA1E,IAICM,SAASd,KAAKG,iBACfH,KAAKG,cAAgB,EAC5B,CACDG,iBACI,OAAON,KAAKE,WACf,CACDa,YACI,OAAOf,KAAKC,MACf,CACDe,uBAAuBnD,GACnB,OAAOmC,KAAKD,OAAOiB,uBAAuBnD,EAC7C,CACDoD,mBACI,OAAOjB,KAAKG,aACf,CACDe,YAAYC,GACR,OAAOnB,KAAKD,OAAOmB,YAAYC,EAClC,CACDC,wBAAwBC,GACpB,MAAMC,EAAWtB,KAAKM,iBA4BhBiB,EAAUX,KAAKY,IAAI,EAAG,IAMtBC,EAAW5B,EAAO6B,MAAM,EAAI1B,KAAKiB,mBAAoBK,EAAWC,EAASD,GAC/E,OAAOzB,EAAO6B,MAAML,EAAeI,EAAUH,EAChD,CAIDK,mCAAkC,wBAAEC,EAAF,eAA2BC,IACzD,MAAMC,EAAkB,IAAIjC,EAAOkC,KAAK/B,KAAKM,iBAAkBN,KAAKe,YAAYjI,QAC1EkJ,EAAQhC,KAAKoB,wBAAwBQ,EAAwB7G,KAAKkH,GAClElH,EAAO6G,EAAwB7G,KAAKmH,MAAMF,GAC1CG,EAAStC,EAAOkC,KAAKL,MAAME,EAAwBO,OAAQ,IAAItC,EAAOkC,KAAK,EAAGF,EAAiB,GAAK,GAAIhC,EAAOkC,KAAKK,IAAIvC,EAAOkC,KAAKM,KAAMP,EAAgBQ,MAAMvH,GAAMwH,KAAK,IAAI1C,EAAOkC,KAAK,EAAG,MACpM,OAAO,IAAIlC,EAAO2C,KAAKL,EAAQP,EAAwB7G,KAAKmH,MAAMF,GACrE,E,2BCxGL,SAASN,EAAMO,EAAGQ,EAAQC,GACtB,OAAIT,EAAIQ,EACGA,EACPR,EAAIS,EACGA,EACJT,CACV,CARDnK,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwK,KAAOxK,EAAQ2K,gBAAkB3K,EAAQ+J,KAAO/J,EAAQ0J,WAAQ,EAQxE1J,EAAQ0J,MAAQA,EAChB,IAAIK,EAAqB,MACrB,MAAMA,EACFjC,YAAYmC,EAAGW,GACX5C,KAAKiC,EAAIA,EACTjC,KAAK4C,EAAIA,CACZ,CACDV,MAAMD,GACF,OAAO,IAAIF,EAAKE,EAAGjC,KAAK4C,EAC3B,CACDC,MAAMD,GACF,OAAO,IAAIb,EAAK/B,KAAKiC,EAAGW,EAC3B,CACDL,KAAKO,GACD,OAAO,IAAIf,EAAK/B,KAAKiC,EAAIa,EAAMb,EAAGjC,KAAK4C,EAAIE,EAAMF,EACpD,CACDN,MAAMQ,GACF,OAAO,IAAIf,EAAK/B,KAAKiC,EAAIa,EAAMb,EAAGjC,KAAK4C,EAAIE,EAAMF,EACpD,CACDG,MAAMC,GACF,OAAO,IAAIjB,EAAK/B,KAAKiC,EAAIe,EAAQhD,KAAK4C,EAAII,EAC7C,CACDC,eAAeH,GACX,OAAO,IAAIf,EAAK/B,KAAKiC,EAAIa,EAAMb,EAAGjC,KAAK4C,EAAIE,EAAMF,EACpD,CACDM,mBAAmBJ,GACf,OAAO,IAAIf,EAAK/B,KAAKiC,EAAIa,EAAMb,EAAGjC,KAAK4C,EAAIE,EAAMF,EACpD,CACDO,IAAIL,GACA,OAAO9C,KAAKiC,EAAIa,EAAMb,EAAIjC,KAAK4C,EAAIE,EAAMF,CAC5C,CACDQ,OAAON,GACH,OAAO9C,KAAKiC,IAAMa,EAAMb,GAAKjC,KAAK4C,IAAME,EAAMF,CACjD,CACDS,aAAaP,EAAOQ,EAAU,MAC1B,OAAO1C,KAAK2C,IAAIvD,KAAKiC,EAAIa,EAAMb,GAAKqB,GAAW1C,KAAK2C,IAAIvD,KAAK4C,EAAIE,EAAMF,GAAKU,CAC/E,CACDE,UACI,OAAOxD,KAAKmD,IAAInD,KACnB,CACDlH,SACI,OAAO8H,KAAK6C,KAAKzD,KAAKwD,UACzB,CACDD,MACI,OAAO,IAAIxB,EAAKnB,KAAK2C,IAAIvD,KAAKiC,GAAIrB,KAAK2C,IAAIvD,KAAK4C,GACnD,CACDc,WAAWC,EAAGC,GACV,OAAO,IAAI7B,EAAKnB,KAAKC,IAAI8C,EAAE1B,EAAG2B,EAAE3B,GAAIrB,KAAKC,IAAI8C,EAAEf,EAAGgB,EAAEhB,GACvD,CACDc,WAAWC,EAAGC,GACV,OAAO,IAAI7B,EAAKnB,KAAKwB,IAAIuB,EAAE1B,EAAG2B,EAAE3B,GAAIrB,KAAKwB,IAAIuB,EAAEf,EAAGgB,EAAEhB,GACvD,CACDc,aAAavC,EAAGN,EAAKuB,GACjB,OAAO,IAAIL,EAAKL,EAAMP,EAAEc,EAAGpB,EAAIoB,EAAGG,EAAIH,GAAIP,EAAMP,EAAEyB,EAAG/B,EAAI+B,EAAGR,EAAIQ,GACnE,CACDiB,UACI,MAAO,CAAC7D,KAAKiC,EAAGjC,KAAK4C,EACxB,EAIL,OAFAb,EAAKM,KAAO,IAAIN,EAAK,EAAG,GACxBA,EAAK+B,KAAO,IAAI/B,EAAK,EAAG,GACjBA,CA5Dc,KA8DzB/J,EAAQ+J,KAAOA,EACf,MAAMY,EACF7C,YAAYiE,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,GAC3DpE,KAAK+D,IAAMA,EACX/D,KAAKgE,IAAMA,EACXhE,KAAKiE,IAAMA,EACXjE,KAAKkE,IAAMA,EACXlE,KAAKmE,IAAMA,EACXnE,KAAKoE,IAAMA,CACd,CACDC,UAAUC,GACN,IAAI,IAAEP,EAAF,IAAOC,EAAP,IAAYC,EAAZ,IAAiBC,EAAjB,IAAsBC,EAAtB,IAA2BC,GAAQpE,KAGvC,OAFA+D,EAAMO,EAAErC,EACRkC,EAAMG,EAAE1B,EACD,IAAID,EAAgBoB,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACvD,CACDV,iBAAiBY,GACb,OAAO,IAAI3B,GAAkB0B,UAAUC,EAC1C,CACDC,SAASD,GACL,OAAO3B,EAAgB0B,UAAUC,GAAGvB,MAAM/C,KAC7C,CACDwE,WACI,OAAO,IAAIzC,EAAK/B,KAAK+D,IAAK/D,KAAKmE,IAClC,CACDM,gBAAgBC,GACZ,IAAI,IAAEX,EAAF,IAAOC,EAAP,IAAYC,EAAZ,IAAiBC,EAAjB,IAAsBC,EAAtB,IAA2BC,GAAQpE,KAGvC,OAFAiE,EAAMS,EAAEzC,EACRmC,EAAMM,EAAE9B,EACD,IAAID,EAAgBoB,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACvD,CACDV,uBAAuBgB,GACnB,OAAO,IAAI/B,GAAkB8B,gBAAgBC,EAChD,CACDC,iBACI,OAAO,IAAI5C,EAAK/B,KAAKiE,IAAKjE,KAAKoE,IAClC,CACDQ,aAAaF,GACT,OAAO/B,EAAgB8B,gBAAgBC,GAAG3B,MAAM/C,KACnD,CACD0D,oBAAoBmB,EAAMC,GACtB,OAAOnC,EAAgB8B,gBAAgBI,EAAK1C,OAAOY,OAAO,IACrDwB,SAAS,IAAIxC,EAAK+C,EAAG/J,KAAKkH,EAAI4C,EAAK9J,KAAKkH,EAAG6C,EAAG/J,KAAK6H,EAAIiC,EAAK9J,KAAK6H,IACjEgC,aAAaE,EAAG3C,OACxB,CACDY,MAAMD,GACF,MAAMiB,EAAM/D,KAAK+D,IAAMjB,EAAMiB,IAAM/D,KAAKgE,IAAMlB,EAAMoB,IAC9CF,EAAMhE,KAAK+D,IAAMjB,EAAMkB,IAAMhE,KAAKgE,IAAMlB,EAAMqB,IAC9CF,EAAMjE,KAAK+D,IAAMjB,EAAMmB,IAAMjE,KAAKgE,IAAMlB,EAAMsB,IAAMpE,KAAKiE,IACzDC,EAAMlE,KAAKkE,IAAMpB,EAAMiB,IAAM/D,KAAKmE,IAAMrB,EAAMoB,IAC9CC,EAAMnE,KAAKkE,IAAMpB,EAAMkB,IAAMhE,KAAKmE,IAAMrB,EAAMqB,IAC9CC,EAAMpE,KAAKkE,IAAMpB,EAAMmB,IAAMjE,KAAKmE,IAAMrB,EAAMsB,IAAMpE,KAAKoE,IAC/D,OAAO,IAAIzB,EAAgBoB,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACvD,CACDhB,OAAON,GACH,OAAQ9C,KAAK+D,KAAOjB,EAAMiB,KACtB/D,KAAKgE,KAAOlB,EAAMkB,KAClBhE,KAAKiE,KAAOnB,EAAMmB,KAClBjE,KAAKkE,KAAOpB,EAAMoB,KAClBlE,KAAKmE,KAAOrB,EAAMqB,KAClBnE,KAAKoE,KAAOtB,EAAMsB,GACzB,CACDf,aAAaP,EAAOQ,EAAU,MAC1B,OAAQ1C,KAAK2C,IAAIvD,KAAK+D,IAAMjB,EAAMiB,KAAOT,GACrC1C,KAAK2C,IAAIvD,KAAKgE,IAAMlB,EAAMkB,KAAOV,GACjC1C,KAAK2C,IAAIvD,KAAKiE,IAAMnB,EAAMmB,KAAOX,GACjC1C,KAAK2C,IAAIvD,KAAKkE,IAAMpB,EAAMoB,KAAOZ,GACjC1C,KAAK2C,IAAIvD,KAAKmE,IAAMrB,EAAMqB,KAAOb,GACjC1C,KAAK2C,IAAIvD,KAAKoE,IAAMtB,EAAMsB,KAAOd,CACxC,CACDyB,YAAYT,GACR,MAAM,IAAEP,EAAF,IAAOC,EAAP,IAAYC,EAAZ,IAAiBC,EAAjB,IAAsBC,EAAtB,IAA2BC,GAAQpE,KACzC,OAAO,IAAI2C,EAAgB2B,EAAIP,EAAKO,EAAIN,EAAKM,EAAIL,EAAKK,EAAIJ,EAAKI,EAAIH,EAAKG,EAAIF,EAC/E,CACDY,MACI,MAAM,IAAEjB,EAAF,IAAOC,EAAP,IAAYC,EAAZ,IAAiBC,EAAjB,IAAsBC,EAAtB,IAA2BC,GAAQpE,KAIzC,OAAQ+D,GADI,EACGI,EAFH,EAEeC,GAAaJ,GAD5B,EACmCE,EAHnC,EAG+CE,GAAaH,GAF5D,EAEmEC,EAHnE,EAG+EC,EAC9F,CACDc,MACI,MAAM,IAAElB,EAAF,IAAOC,EAAP,IAAYC,EAAZ,IAAiBC,EAAjB,IAAsBC,EAAtB,IAA2BC,GAAQpE,KAgBzC,OAAO,IAAI2C,IAbC,EAOcwB,EARd,EAQ0BC,KAP1B,EAQcJ,EATd,EAS0BC,KACZD,EAAMI,EAAMH,EAAME,KAThC,EAUcD,EAZd,EAY0BE,KAV1B,EAWcL,EAbd,EAa0BE,KACZF,EAAMK,EAAMH,EAAMC,GAE/C,CACDgB,WACI,MAAMF,EAAMhF,KAAKgF,MACjB,OAAY,IAARA,EACO,KACChF,KAAKiF,MACNF,YAAY,EAAIC,EAC9B,CACDG,gBAAgBhE,GACZ,OAAO,IAAIY,EAAKZ,EAAEc,EAAIjC,KAAK+D,IAAM5C,EAAEyB,EAAI5C,KAAKgE,IAAK7C,EAAEc,EAAIjC,KAAKkE,IAAM/C,EAAEyB,EAAI5C,KAAKmE,IAChF,CACDiB,uBAAuBjE,GACnB,MAAMkE,EAAMrF,KAAKkF,WACjB,OAAKG,EAEEA,EAAIF,gBAAgBhE,GADhB,IAEd,CACDmE,kBAAkBnE,GACd,OAAO,IAAIY,EAAKZ,EAAEc,EAAIjC,KAAK+D,IAAM5C,EAAEyB,EAAI5C,KAAKgE,IAAMhE,KAAKiE,IAAK9C,EAAEc,EAAIjC,KAAKkE,IAAM/C,EAAEyB,EAAI5C,KAAKmE,IAAMnE,KAAKoE,IACtG,CACDmB,yBAAyBpE,GACrB,MAAMkE,EAAMrF,KAAKkF,WACjB,OAAKG,EAEEA,EAAIC,kBAAkBnE,GADlB,IAEd,CACDqE,cAAcC,GACV,MAAM1K,EAAOiF,KAAKmF,gBAAgBM,EAAE1K,MAC9BoH,EAASnC,KAAKsF,kBAAkBG,EAAEtD,QACxC,OAAIpH,EAAKkH,EAAI,GAAKlH,EAAK6H,EAAI,EAChB,IAAIJ,EAAKL,EAAOI,KAAKxH,GAAOA,EAAKwI,OAEnCxI,EAAKkH,EAAI,EACP,IAAIO,EAAKL,EAAOD,MAAMC,EAAOF,EAAIlH,EAAKkH,GAAIlH,EAAKwI,OAEjDxI,EAAK6H,EAAI,EACP,IAAIJ,EAAKL,EAAOU,MAAMV,EAAOS,EAAI7H,EAAK6H,GAAI7H,EAAKwI,OAEnD,IAAIf,EAAKL,EAAQpH,EAC3B,CACD2K,qBAAqBD,GACjB,MAAMJ,EAAMrF,KAAKkF,WACjB,OAAKG,EAEEA,EAAIG,cAAcC,GADd,IAEd,CACD5B,UAGI,MAAO,CACH7D,KAAK+D,IAAK/D,KAAKkE,IAAK,EACpBlE,KAAKgE,IAAKhE,KAAKmE,IAAK,EACpBnE,KAAKiE,IAAKjE,KAAKoE,IAAK,EAE3B,EAELpM,EAAQ2K,gBAAkBA,EAC1B,IAAIH,EAAqB,MACrB,MAAMA,EACF1C,YAAYqC,EAAQpH,GAChBiF,KAAKmC,OAASA,EACdnC,KAAKjF,KAAOA,CACf,CACD4K,UACI,OAAuB,GAAhB3F,KAAKgC,SAAiC,GAAjBhC,KAAK4F,QACpC,CACD5D,QACI,OAAOhC,KAAKjF,KAAKkH,CACpB,CACD2D,SACI,OAAO5F,KAAKjF,KAAK6H,CACpB,CACDiD,OACI,OAAO7F,KAAKmC,OAAOF,CACtB,CACD6D,QACI,OAAO9F,KAAK6F,OAAS7F,KAAKgC,OAC7B,CACD+D,MACI,OAAO/F,KAAKmC,OAAOS,CACtB,CACDoD,SACI,OAAOhG,KAAK+F,MAAQ/F,KAAK4F,QAC5B,CACDK,UACI,OAAOjG,KAAKmC,MACf,CACD+D,WACI,OAAOlG,KAAKmC,OAAOI,KAAK,IAAIR,EAAK/B,KAAKgC,QAAS,GAClD,CACDmE,cACI,OAAOnG,KAAKmC,OAAOI,KAAKvC,KAAKjF,KAChC,CACDqL,aACI,OAAOpG,KAAKmC,OAAOI,KAAK,IAAIR,EAAK,EAAG/B,KAAK4F,UAC5C,CACDS,WAAWlE,GACP,OAAO,IAAIK,EAAKL,EAAQnC,KAAKjF,KAChC,CACDuL,SAASvL,GACL,OAAO,IAAIyH,EAAKxC,KAAKmC,OAAQpH,EAChC,CACDwL,eAAeC,GACX,OAAO,IAAIzE,EAAKL,EAAM8E,EAAEvE,EAAGjC,KAAK6F,OAAQ7F,KAAK8F,SAAUpE,EAAM8E,EAAE5D,EAAG5C,KAAK+F,MAAO/F,KAAKgG,UACtF,CACDS,aAAaD,GACT,OAAOA,EAAElE,MAAMtC,KAAKuG,eAAeC,IAAI1N,QAC1C,CACD4N,SAASF,GACL,OAAgC,IAAzBxG,KAAKyG,aAAaD,EAC5B,CACDG,oBAAoB7D,GAChB,MAAMiD,EAAMnF,KAAKwB,IAAIpC,KAAK+F,MAAOjD,EAAMiD,OAEvC,GADenF,KAAKwB,IAAI2D,EAAKnF,KAAKC,IAAIb,KAAKgG,SAAUlD,EAAMkD,WAC9CD,GAAQ,EACjB,OAAO,EACX,MAAMF,EAAOjF,KAAKwB,IAAIpC,KAAK6F,OAAQ/C,EAAM+C,QAEzC,OADcjF,KAAKwB,IAAIyD,EAAMjF,KAAKC,IAAIb,KAAK8F,QAAShD,EAAMgD,UAC9CD,GAAS,CAGxB,CACDe,cAAc9D,GACV,MAAMmD,EAAUlE,EAAKK,IAAIpC,KAAKiG,UAAWnD,EAAMmD,WACzCE,EAAcpE,EAAKK,IAAI6D,EAASlE,EAAKlB,IAAIb,KAAKmG,cAAerD,EAAMqD,gBACzE,OAAO,IAAI3D,EAAKyD,EAASE,EAAY7D,MAAM2D,GAC9C,CACD7C,OAAON,GACH,OAAO9C,KAAKmC,OAAOiB,OAAON,EAAMX,SAAWnC,KAAKjF,KAAKqI,OAAON,EAAM/H,KACrE,CACDsI,aAAaP,GACT,OAAO9C,KAAKmC,OAAOkB,aAAaP,EAAMX,SAAWnC,KAAKjF,KAAKsI,aAAaP,EAAM/H,KACjF,CACD8L,OACI,OAAO7G,KAAKjF,KAAKkH,EAAIjC,KAAKjF,KAAK6H,CAClC,EAKL,OAHAJ,EAAKsE,MAAQ,IAAItE,EAAKT,EAAKM,KAAMN,EAAKM,MACtCG,EAAKsB,KAAO,IAAItB,EAAKT,EAAKM,KAAMN,EAAK+B,MACrCtB,EAAKuE,IAAM,IAAIvE,EAAK,IAAIT,GAAM,GAAI,GAAI,IAAIA,EAAK,EAAG,IAC3CS,CAnFc,KAqFzBxK,EAAQwK,KAAOA,C,mCCzTf,IAAIwE,EAAmBhH,MAAQA,KAAKgH,kBAAqBlP,OAAOmP,OAAU,SAASC,EAAGC,EAAGC,EAAGC,QAC7E7H,IAAP6H,IAAkBA,EAAKD,GAC3BtP,OAAOC,eAAemP,EAAGG,EAAI,CAAEC,YAAY,EAAM5M,IAAK,WAAa,OAAOyM,EAAEC,EAAK,GACpF,EAAK,SAASF,EAAGC,EAAGC,EAAGC,QACT7H,IAAP6H,IAAkBA,EAAKD,GAC3BF,EAAEG,GAAMF,EAAEC,EACb,GACGG,EAAsBvH,MAAQA,KAAKuH,qBAAwBzP,OAAOmP,OAAU,SAASC,EAAG/F,GACxFrJ,OAAOC,eAAemP,EAAG,UAAW,CAAEI,YAAY,EAAMrP,MAAOkJ,GAClE,EAAI,SAAS+F,EAAG/F,GACb+F,EAAC,QAAc/F,CAClB,GACGqG,EAAgBxH,MAAQA,KAAKwH,cAAiB,SAAUC,GACxD,GAAIA,GAAOA,EAAIC,WAAY,OAAOD,EAClC,IAAIlI,EAAS,CAAC,EACd,GAAW,MAAPkI,EAAa,IAAK,IAAIL,KAAKK,EAAS3P,OAAO6P,eAAeC,KAAKH,EAAKL,IAAIJ,EAAgBzH,EAAQkI,EAAKL,GAEzG,OADAG,EAAmBhI,EAAQkI,GACpBlI,CACV,EACGsI,EAAa7H,MAAQA,KAAK6H,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAE1E,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUpQ,GAAS,IAAMqQ,EAAKL,EAAUM,KAAKtQ,GAAU,CAAC,MAAOgB,GAAKmP,EAAOnP,EAAK,CAAE,CAC3F,SAASuP,EAASvQ,GAAS,IAAMqQ,EAAKL,EAAS,MAAUhQ,GAAU,CAAC,MAAOgB,GAAKmP,EAAOnP,EAAK,CAAE,CAC9F,SAASqP,EAAK/I,GAJlB,IAAetH,EAIasH,EAAOkJ,KAAON,EAAQ5I,EAAOtH,QAJ1CA,EAIyDsH,EAAOtH,MAJhDA,aAAiB+P,EAAI/P,EAAQ,IAAI+P,GAAE,SAAUG,GAAWA,EAAQlQ,EAAS,KAIlByQ,KAAKL,EAAWG,EAAY,CAC9GF,GAAML,EAAYA,EAAUU,MAAMb,EAASC,GAAc,KAAKQ,OACjE,GACJ,EACDzQ,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQyE,uBAAyBzE,EAAQ4Q,wBAA0B5Q,EAAQ6Q,QAAU7Q,EAAQ8Q,aAAe9Q,EAAQ+Q,MAAQ/Q,EAAQgR,gBAAa,EACjJ,MAAMxQ,EAAUD,EAAQ,KAClBE,EAAqBF,EAAQ,KAC7B0Q,EAAoBf,QAAQC,UAAUO,MAAK,IAAMlB,EAAajP,EAAQ,QAE5E0Q,EAAkBP,MAAK,SACvB,MAAMM,EACFlJ,cACIE,KAAKkJ,WAAa,EAClBlJ,KAAKE,YAAc,CACtB,CACDiJ,gBACI,OAAOnJ,KAAKkJ,UACf,CACD5I,iBACI,OAAON,KAAKE,WACf,CACDkJ,iBAAiBC,GACbrJ,KAAKE,aAAemJ,CACvB,CACDC,gBAAgBD,GACZrJ,KAAKkJ,YAAcG,CACtB,CACDE,oBAAoBzG,GAChB9C,KAAKkJ,WAAapG,EAAMoG,WACxBlJ,KAAKE,YAAc4C,EAAM5C,WAC5B,EAELlI,EAAQgR,WAAaA,EACrB,IAAID,EAAsB,MACtB,MAAMA,UAAcC,EAChBlJ,YAAY0J,GACRC,QACAzJ,KAAK3D,IAAMmN,EAAKnN,IAChB2D,KAAK9G,KAAOsQ,EAAKtQ,KACjB8G,KAAKjE,KAAOyN,EAAKzN,KACjBiE,KAAK/D,KAAOuN,EAAKvN,KACjB+D,KAAK7D,IAAMqN,EAAKrN,GACnB,CACDuH,mBAAmB1J,EAAKwP,GACpB,OAAOxP,EAAI6B,YAAY,IAAIkN,EAAMS,GACpC,EAML,OAJAT,EAAMtK,KAAO,IAAIsK,EAAM,CACnB1M,IAAK,oBACLnD,KAAM,sBAEH6P,CAlBe,KAoB1B/Q,EAAQ+Q,MAAQA,EAChB,MAAMD,UAAqBE,EACvBlJ,YAAYjC,EAAOjB,GACf6M,QACAzJ,KAAKnC,MAAQA,EACbmC,KAAKpD,OAASA,EACdoD,KAAKnD,SAAW,GAEhBmD,KAAK0J,QAAS,CACjB,CACDC,SACI,OAAO3J,KAAKnC,QAAUkL,EAAMtK,IAC/B,CACDmL,WACI,OAAO5J,KAAK0J,MACf,CACDG,SACI7J,KAAK0J,QAAS,CACjB,EAEL1R,EAAQ8Q,aAAeA,EACvB,MAAMD,EACF/I,YAAYI,EAAc,GACtBF,KAAK9G,KAAO,GACZ8G,KAAK8J,OAAS,IAAItR,EAAQuR,SAQ1B/J,KAAKgK,wBAA0B,IAAIlB,EAAaC,EAAMtK,KAAM,MAC5DuB,KAAKiK,oBAAsB,IAAInB,EAAaC,EAAMtK,KAAM,MAGxDuB,KAAKxF,QAAU,GACfwF,KAAKkK,QAAU,GACflK,KAAKmK,eAAiB,IAAI1R,EAAmB2R,kBAC7CpK,KAAKqK,mBAAqB,KAC1BrK,KAAKE,YAAcA,CACtB,CACDoK,6BACI,OAAOtK,KAAKgK,uBACf,CACDO,yBACI,OAAOvK,KAAKiK,mBACf,CACD/I,YAAYC,GACR,OAAOnB,KAAKmK,eAAeK,OAAOrJ,EACrC,CACDhD,kBAAkBsM,GACdzK,KAAKmK,eAAiBM,CACzB,CACDC,gBACI,OAAO1K,KAAKmK,eAAerG,IAC9B,CACD6G,UACI,OAAO3K,KAAK9G,IACf,CACDqC,QAAQrC,GACJ8G,KAAK9G,KAAOA,CACf,CACDoH,iBACI,OAAON,KAAKE,WACf,CACD0K,wBAII,OAHgC,OAA5B5K,KAAKqK,qBACLrK,KAAKqK,mBAAqBrK,KAAKiK,oBAAoBpN,SAASgO,QAAO,CAACC,EAAGC,IAAMD,EAAIC,EAAEzK,kBAAkB,IAElGN,KAAKqK,kBACf,CAIDW,uBACI,SAAStM,EAAM/B,GACXA,EAAKE,SAASoO,MAAK,CAACtH,EAAGC,MAAQD,EAAErD,iBAAmBsD,EAAEtD,oBACtD3D,EAAKE,SAASiC,QAAQJ,EACzB,CACDA,CAAMsB,KAAKiK,oBACd,CACDiB,mBAAmBC,EAAWC,IAC1B,SAAS1M,EAAM/B,EAAM6D,GACb7D,EAAKkB,QAAUkL,EAAMtK,MACrB0M,EAAUxO,EAAM6D,GAEpB,IAAI6K,EAAY,EAChB1O,EAAKE,SAASiC,SAAQ,SAAU/B,GAC5B2B,EAAM3B,EAAOyD,EAAQ6K,GACrBA,GAAatO,EAAMuD,gBACtB,IACG3D,EAAKkB,QAAUkL,EAAMtK,MACrB2M,EAAWzO,EAAM6D,EAAQ7D,EAAK2D,iBAErC,CACD5B,CAAMsB,KAAKiK,oBAAqB,EACnC,CACD1J,YAAY4K,EAAWC,GACnB,IAAI5N,EAAY,GACZvF,EAAQ,EACRqT,EAAc,EAClB,IAAK,IAAI7N,KAAYuC,KAAKxF,QAAS,CAE/B,IAAIkD,EAAM,KAGV,IAAKA,EAAMD,EAAUC,GAAOA,EAAIG,OAASkL,EAAMtK,OAAoC,IAA5BjB,EAAUG,QAAQD,GAAaA,EAAMA,EAAId,QAEhG,KAAOY,EAAU1E,OAAS,GAAKN,EAAQoF,OAAOJ,IAAcE,GAExD0N,EADa5N,EAAUM,MACN7F,GAGrB,MAAM+F,EAAS,GACf,IAAK,IAAIrB,EAAOc,EAAUd,GAAQA,EAAKkB,OAASkL,EAAMtK,MAAQ9B,GAAQe,EAAKf,EAAOA,EAAKC,OACnFoB,EAAOxC,KAAKmB,GAEhBqB,EAAOC,UACP,IAAK,IAAItB,KAAQqB,EACbmN,EAAUxO,EAAM1E,GAEpBuF,EAAYA,EAAU5C,OAAOoD,GAC7B/F,GAAS+H,KAAKkK,QAAQoB,IACzB,CAED,IAAK,IAAIjO,EAAIG,EAAU1E,OAAS,EAAGuE,GAAK,EAAGA,IACvC+N,EAAW5N,EAAUH,GAAIpF,EAEhC,CACDsT,aAAaC,GACTxL,KAAK8J,OAAOhL,QAAQ0M,EACvB,CACDC,mCACI,MAAMC,EAAU,IAAIjP,EACd2D,EAAQ,GACRuL,EAAgB,IAAIC,IAkB1B5L,KAAKO,aAjBL,SAAmB5D,EAAM1E,GACjB0T,EAAcE,IAAIlP,EAAKkB,OACvBuC,EAAM5E,KAAK,OAGXmQ,EAAcG,IAAInP,EAAKkB,OACvBuC,EAAM5E,KAAKmB,GACX+O,EAAQxN,WAAWvB,EAAKkB,MAAO5F,GAEtC,IACD,SAAoB0E,EAAM1E,GACtB,MAAMwF,EAAW2C,EAAMtC,MACnBL,IACAkO,EAAcI,OAAOtO,EAASI,OAC9B6N,EAAQ3N,WAAWN,EAASI,MAAO5F,GAE1C,IAED,MAAM+T,EAAmBN,EAAQrN,QA0BjC,OAzBA2N,EAAiB9S,KAAO8G,KAAK9G,KAC7B8S,EAAiB7B,eAAiBnK,KAAKmK,eAqBvCnK,KAAKuL,cAAad,IACduB,EAAiBlC,OAAOjO,YAAY4O,GAAGlB,oBAAoBkB,EAA3D,IAEGuB,CACV,CACDC,+BAA+BC,GAC3B,MAAMC,EAAapD,EAAMlN,YAAYmE,KAAK8J,OAAQoC,GAC5CR,EAAU,IAAI9C,EAGdrO,EAAQ,IACd,SAASmE,EAAM/B,GACX,GAAIA,EAAKkB,QAAUsO,EACf5R,EAAMiB,KAAKmB,QAGX,IAAK,IAAII,KAASJ,EAAKE,SACnB6B,EAAM3B,EAGjB,CACD2B,CAAMsB,KAAKgK,yBACX,IAAK,IAAIrN,KAAQpC,EAAO,CACpB,MAAM6F,EAAQ,GACd,IAAK,IAAI0K,EAAInO,EAAW,MAALmO,GAAaA,EAAEjN,QAAUkL,EAAMtK,KAAMqM,EAAIA,EAAElO,OAC1DwD,EAAM5E,KAAKsP,EAAEjN,OAEjB6N,EAAQU,uBAAuBhM,EAAOzD,EAAK2D,iBAC9C,CACD,MAAM+L,EAAMX,EAAQrN,QAGpB,OAFAgO,EAAInT,KAAO8G,KAAK9G,KAChBmT,EAAIlC,eAAiBnK,KAAKmK,eACnBkC,CACV,CACDC,uBAAuBJ,GACnB,MAAMC,EAAapD,EAAMlN,YAAYmE,KAAK8J,OAAQoC,GAC5CR,EAAU,IAAI9C,GAapB,SAAS2D,EAAU5P,GACf,GAAIA,EAAKkB,QAAUsO,GAbvB,SAAuBK,GACnB,MAAMpM,EAAQ,IACd,SAAS1B,EAAM/B,GACXyD,EAAM5E,KAAKmB,EAAKkB,OAChB6N,EAAQU,uBAAuBhM,EAAOzD,EAAKwM,iBAC3C,IAAK,IAAIpM,KAASJ,EAAKE,SACnB6B,EAAM3B,GAEVqD,EAAMtC,KACT,CACDY,CAAM8N,EACT,CAGOC,CAAc9P,QAGd,IAAK,IAAII,KAASJ,EAAKE,SACnB0P,EAAUxP,EAGrB,CACDwP,CAAUvM,KAAKgK,yBACf,MAAMqC,EAAMX,EAAQrN,QAGpB,OAFAgO,EAAInT,KAAO8G,KAAK9G,KAChBmT,EAAIlC,eAAiBnK,KAAKmK,eACnBkC,CACV,CAEDK,WACI,OAAO7E,EAAU7H,UAAM,OAAQ,GAAQ,YACnC,IAAIZ,EAAc,KAClB,IAAK,IAAIvB,KAASmC,KAAK8J,OAGfjM,EAAM3E,KAAKoG,WAAW,SACjBF,IACDA,SAAqB6J,GAAmB7J,aAE5CvB,EAAM3E,KAAOkG,EAAYvB,EAAM3E,MAG1C,GACJ,CACDyT,WAAWC,GACP,IAAK,IAAI/O,KAASmC,KAAK8J,OACnBjM,EAAM3E,KAAO0T,EAAS/O,EAAM3E,KAEnC,EAELlB,EAAQ6Q,QAAUA,EAClB,MAAMD,UAAgCC,EAClC/I,cACI2J,SAASoD,WACT7M,KAAK8M,cAAgB,IACxB,CACDC,cAAc3M,EAAO4M,EAAQC,GACzB,GAAI1P,MAAMyP,GACN,MAAM,IAAIvR,MAAM,kBACpB,IAAIkB,EAAOsQ,EAAiBjN,KAAKgK,wBAA0BhK,KAAKiK,oBAC5D0B,EAAgB,IAAIC,IACxB,IAAK,IAAIsB,KAAa9M,EAAO,CACzB,MAAMvC,EAAQkL,EAAMlN,YAAYmE,KAAK8J,OAAQoD,GACvCC,EAAOF,EACPzU,EAAQoF,OAAOjB,EAAKE,UACpBF,EAAKE,SAAS7D,MAAK+R,GAAKA,EAAElN,QAAUA,IAC1C,GAAIsP,IAASA,EAAKvD,YAAcuD,EAAKtP,OAASA,EAC1ClB,EAAOwQ,MAEN,CACD,MAAMvQ,EAASD,EACfA,EAAO,IAAImM,EAAajL,EAAOlB,GAC/BC,EAAOC,SAASrB,KAAKmB,EACxB,CACDA,EAAKyM,iBAAiB4D,GAOtBrB,EAAcG,IAAInP,EAAKkB,MAC1B,CAED,GADAlB,EAAK2M,gBAAgB0D,GACjBC,EACA,IAAK,IAAIlQ,KAASJ,EAAKE,SACnBE,EAAM8M,SAGd,GAAIoD,EAAgB,CAChBtQ,EAAKkB,MAAMyL,gBAAgB0D,GAC3B,IAAK,IAAInP,KAAS8N,EACd9N,EAAMuL,iBAAiB4D,GAEvBrQ,IAASnE,EAAQoF,OAAOoC,KAAKxF,SAC7BwF,KAAKkK,QAAQlK,KAAKkK,QAAQpR,OAAS,IAAMkU,GAGzChN,KAAKxF,QAAQgB,KAAKmB,GAClBqD,KAAKkK,QAAQ1O,KAAKwR,GAEzB,CACJ,CACDZ,uBAAuBhM,EAAO4M,GAC1B,GAAe,IAAXA,EAAJ,CAIA,GAAIA,EAAS,EACT,MAAM,IAAIvR,MAAM,sCAEpBuE,KAAK+M,cAAc3M,EAAO4M,GAAQ,GAClChN,KAAK+M,cAAc3M,EAAO4M,GAAQ,EALjC,CAMJ,CACDI,0BAA0BhN,EAAOnB,GAC7B,GAAIe,KAAK8M,cAAe,CACpB,GAAI7N,EAAYe,KAAK8M,cAAcO,iBAC/B,MAAM,IAAI5R,MAAM,oCAEpB,MAAM6R,GAAgBrO,EAAYe,KAAK8M,cAAcO,kBAAoB,EACzErN,KAAKoM,uBAAuBpM,KAAK8M,cAAc1M,MAAOkN,EAAetN,KAAK8M,cAAcS,gBACxFvN,KAAK8M,cAAgB,CAAE1M,QAAOmN,eAAgBD,EAAcD,iBAAkBpO,EACjF,MAEGe,KAAK8M,cAAgB,CAAE1M,QAAOmN,eAAgBtO,EAAWoO,iBAAkBpO,EAElF,CACDZ,QAcI,OAbI2B,KAAK8M,gBACD9M,KAAKxF,QAAQ1B,OAAS,EACtBkH,KAAKoM,uBAAuBpM,KAAK8M,cAAc1M,MAAOJ,KAAK8M,cAAcO,iBAAmBrN,KAAK8M,cAAcS,iBAK/GvN,KAAKoM,uBAAuBpM,KAAK8M,cAAc1M,MAAO,GACtDJ,KAAK7B,kBAAkB,IAAI1F,EAAmB2R,qBAGtDpK,KAAKE,YAAcU,KAAKwB,IAAIpC,KAAKE,YAAaF,KAAKkK,QAAQW,QAAO,CAAClH,EAAGC,IAAMD,EAAIC,GAAG,IACnF5D,KAAKgL,sBACEhL,IACV,EAELhI,EAAQ4Q,wBAA0BA,EAIlC,MAAMnM,UAA+BoM,EACjC/I,cACI2J,SAASoD,WACT7M,KAAKwN,iBAAmB,CAACxN,KAAKgK,yBAC9BhK,KAAKyN,kBAAoB,CAACzN,KAAKiK,qBAC/BjK,KAAK2L,cAAgB,IAAIrS,IACzB0G,KAAKI,MAAQ,GACbJ,KAAK0N,UAAY,CACpB,CACDC,mBAAmB1V,GACf,MAAMoR,EAAQpR,EAAQ+H,KAAK0N,UAC3B,IAAK,IAAI7P,KAASmC,KAAK2L,cAAcxQ,OACjC0C,EAAMuL,iBAAiBC,GAE3B,MAAM5L,EAAWjF,EAAQoF,OAAOoC,KAAKI,OACjC3C,GACAA,EAAS6L,gBAAgBD,EAEhC,CACDuE,kBAAkB3V,EAAOmI,GACrB,MAAMiJ,EAAQpR,EAAQ+H,KAAK0N,UAC3B,IAAK,IAAI/Q,KAAQyD,EACbzD,EAAKyM,iBAAiBC,GAE1B,MAAM5L,EAAWjF,EAAQoF,OAAOwC,GAC5B3C,GACAA,EAAS6L,gBAAgBD,EAEhC,CACDwE,YAAYhQ,EAAO5F,EAAOgV,GACtB,IAAI7M,EAAQ6M,EAAiBjN,KAAKwN,iBAAmBxN,KAAKyN,kBAC1DzN,KAAK4N,kBAAkB3V,EAAOmI,GAC9B,IAAI0N,EAAUtV,EAAQoF,OAAOwC,GAC7B,GAAI0N,EAAS,CACT,GAAIb,EAAgB,CAChB,MAAM5D,EAAQpR,EAAQ+H,KAAK0N,UAC3B,GAAIrE,EAAQ,EACRrJ,KAAKxF,QAAQgB,KAAKsS,GAClB9N,KAAKkK,QAAQ1O,KAAKvD,EAAQ+H,KAAK0N,gBAE9B,GAAIrE,EAAQ,EACb,MAAM,IAAI5N,MAAO,qFAAoFuE,KAAK0N,8BAA8BzV,IAE/I,CACD,MAAMkV,EAAOF,EACPzU,EAAQoF,OAAOkQ,EAAQjR,UACvBiR,EAAQjR,SAAS7D,MAAK+R,GAAKA,EAAElN,QAAUA,IAC7C,IAAIlB,EACAwQ,IAASA,EAAKvD,YAAcuD,EAAKtP,OAASA,EAC1ClB,EAAOwQ,GAGPxQ,EAAO,IAAImM,EAAajL,EAAOiQ,GAC/BA,EAAQjR,SAASrB,KAAKmB,IAE1ByD,EAAM5E,KAAKmB,EACd,CACJ,CACDuB,WAAWgP,EAAWjV,GAClB,MAAM4F,EAAQkL,EAAMlN,YAAYmE,KAAK8J,OAAQoD,GAC7ClN,KAAK2N,mBAAmB1V,GACxB+H,KAAK6N,YAAYhQ,EAAO5F,GAAO,GAC/B+H,KAAK6N,YAAYhQ,EAAO5F,GAAO,GAC/B+H,KAAKI,MAAM5E,KAAKqC,GAChB,MAAMkQ,EAAa/N,KAAK2L,cAAcjR,IAAImD,IAAU,EACpDmC,KAAK2L,cAAc3R,IAAI6D,EAAOkQ,EAAa,GAC3C/N,KAAK0N,UAAYzV,CACpB,CACD+V,YAAYnQ,EAAO5F,EAAOgV,GACtB,IAAI7M,EAAQ6M,EAAiBjN,KAAKwN,iBAAmBxN,KAAKyN,kBAE1D,GADAzN,KAAK4N,kBAAkB3V,EAAOmI,GAC1B6M,EAAgB,CAChB,MAAMgB,EAAkBjO,KAAKwN,iBAAiB1P,MAC9C,GAAuB,MAAnBmQ,EACA,MAAM,IAAIxS,MAAO,mBAAkBoC,EAAMxB,2BAE7C,GAAsB,MAAlB2D,KAAK0N,UACL,MAAM,IAAIjS,MAAO,qBAAoBoC,EAAMxB,oCAG/C,GADA4R,EAAgBpE,SACZoE,EAAgBpQ,MAAMxB,MAAQwB,EAAMxB,IACpC,MAAM,IAAIZ,MAAO,yBAAwBoC,EAAM3E,sBAAsB+U,EAAgBpQ,MAAM3E,2BAA2BjB,KAE1H,MAAMoR,EAAQpR,EAAQ+H,KAAK0N,UAC3B,GAAIrE,EAAQ,EACRrJ,KAAKxF,QAAQgB,KAAKyS,GAClBjO,KAAKkK,QAAQ1O,KAAKvD,EAAQ+H,KAAK0N,gBAE9B,GAAIrE,EAAQ,EACb,MAAM,IAAI5N,MAAO,qFAAoFuE,KAChG0N,8BAA8BzV,IAE1C,MAEG+H,KAAKyN,kBAAkB3P,KAE9B,CACDC,WAAWmP,EAAWjV,GAClB,MAAM4F,EAAQkL,EAAMlN,YAAYmE,KAAK8J,OAAQoD,GAC7ClN,KAAK2N,mBAAmB1V,GACxB+H,KAAKgO,YAAYnQ,EAAO5F,GAAO,GAC/B+H,KAAKgO,YAAYnQ,EAAO5F,GAAO,GAC/B+H,KAAKI,MAAMtC,MACX,MAAMiQ,EAAa/N,KAAK2L,cAAcjR,IAAImD,GACxB,MAAdkQ,IAEe,IAAfA,EACA/N,KAAK2L,cAAcI,OAAOlO,GAG1BmC,KAAK2L,cAAc3R,IAAI6D,EAAOkQ,EAAa,GAE/C/N,KAAK0N,UAAYzV,EACjB+H,KAAKE,YAAcU,KAAKwB,IAAIpC,KAAKE,YAAaF,KAAK0N,WACtD,CACDrP,QAGI,GAAI2B,KAAKwN,iBAAiB1U,OAAS,GAAKkH,KAAKyN,kBAAkB3U,OAAS,EACpE,MAAM,IAAI2C,MAAM,iEAGpB,OADAuE,KAAKgL,sBACEhL,IACV,EAELhI,EAAQyE,uBAAyBA,C,2BCtjBjC3E,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQkW,aAAelW,EAAQmW,WAAanW,EAAQoW,mBAAqBpW,EAAQqW,yBAA2BrW,EAAQsW,2BAA6BtW,EAAQuW,KAAOvW,EAAQwW,aAAexW,EAAQyW,SAAWzW,EAAQ0W,MAAQ1W,EAAQ2W,cAAgB3W,EAAQ4W,QAAU5W,EAAQ6W,SAAW7W,EAAQkD,UAAYlD,EAAQ8W,MAAQ9W,EAAQ+R,SAAW/R,EAAQ+W,WAAa/W,EAAQgX,UAAYhX,EAAQ6D,YAAc7D,EAAQyB,OAASzB,EAAQ4F,YAAS,EAI/a5F,EAAQ4F,OAHR,SAAgBlE,GACZ,OAAOA,EAAGA,EAAGZ,OAAS,IAAM,IAC/B,EAUDd,EAAQyB,OARR,SAAgBC,EAAI2C,GAMhB3C,EAAGuR,MALH,SAAoBtH,EAAGC,GACnB,MAAMqL,EAAO5S,EAAIsH,GACXuL,EAAO7S,EAAIuH,GACjB,OAAOqL,EAAOC,GAAQ,EAAID,EAAOC,EAAO,EAAI,CAC/C,GAEJ,EAODlX,EAAQ6D,YALR,SAAqBgD,EAAKuI,EAAG+H,GAGzB,OAFKtQ,EAAIgN,IAAIzE,IACTvI,EAAI7E,IAAIoN,EAAG+H,EAAS/H,IACjBvI,EAAInE,IAAI0M,EAClB,EAODpP,EAAQgX,UALR,SAAmBnQ,EAAKuI,EAAG+H,GACvB,OAAKtQ,EAAIgN,IAAIzE,GAENvI,EAAInE,IAAI0M,GADJ+H,EAAS/H,EAEvB,EAQDpP,EAAQ+W,WANR,SAAoBlQ,EAAKuI,GACrB,IAAKvI,EAAIgN,IAAIzE,GACT,MAAM,IAAI3L,MAAO,gBAAe2L,KAEpC,OAAOvI,EAAInE,IAAI0M,EAClB,EAED,MAAM2C,EACFjK,cACIE,KAAKnB,IAAM,IAAIvF,GAClB,CACDuC,YAAY6I,GACR,MAAMrI,EAAMqI,EAAErI,IAEd,OADiB2D,KAAKnB,IAAInE,IAAI2B,KAG9B2D,KAAKnB,IAAI7E,IAAIqC,EAAKqI,GACXA,EACV,CACD5F,QAAQ0M,GACJxL,KAAKnB,IAAIC,QAAQ0M,EACpB,CACD,CAAC4D,OAAOC,YACJ,OAAOrP,KAAKnB,IAAIyQ,QACnB,EA0CL,SAASZ,EAAMzM,GACX,OAAOA,EAAIrB,KAAK2O,MAAMtN,EACzB,CAsBD,SAASqM,EAA2B3K,EAAGC,GACnC,IAAK,IAAIvH,KAAOsH,EACZ,GAAIA,EAAEtH,KAASuH,EAAEvH,GACb,OAAO,EAEf,IAAK,IAAIA,KAAOuH,EACZ,GAAID,EAAEtH,KAASuH,EAAEvH,GACb,OAAO,EAEf,OAAO,CACV,CA0CD,SAAS8R,EAAWqB,GAChB,IAAIrC,EAAO,KACX,MAAO,KACS,MAARA,IACAA,EAAO,CAAE5N,OAAQiQ,MAEdrC,EAAK5N,OAEnB,CA5HDvH,EAAQ+R,SAAWA,EAMnB/R,EAAQ8W,MALR,UAAgBW,EAAIhF,GAChB,IAAK,IAAI/F,KAAK+K,QACJhF,EAAE/F,EAEf,EAOD1M,EAAQkD,UALR,SAAmBuU,EAAIhF,GACnB,IAAK,IAAI/F,KAAK+K,EACVhF,EAAE/F,EAET,EASD1M,EAAQ6W,SAPR,SAAkBY,EAAIhF,EAAGiF,GACrB,IAAIC,EAAQD,EACZ,IAAK,IAAIhL,KAAK+K,EACVE,EAAQlF,EAAEkF,EAAOjL,GAErB,OAAOiL,CACV,EAKD3X,EAAQ4W,QAHR,SAAiBtK,EAAGtC,GAChB,OAAO,IAAIpJ,MAAMgI,KAAKwB,IAAIJ,EAAQsC,EAAExL,OAAQ,GAAK,GAAG8W,KAAK,KAAOtL,CACnE,EAgBDtM,EAAQ2W,cAdR,SAAuBkB,GACnB,IAAIC,EAAoB,GAAED,EAAQE,QAAQ,MAW1C,OAVgB,MAAZF,EACAC,EAAmB,OACdD,EAAU,GACfC,EAAmB,OACdD,EAAU,IACfC,EAAmB,SACdD,EAAU,EACfC,EAAoB,GAAED,EAAQE,QAAQ,MACjCF,EAAU,KACfC,EAAoB,GAAED,EAAQE,QAAQ,OACnCD,CACV,EAKD9X,EAAQ0W,MAAQA,EAIhB1W,EAAQyW,SAHR,SAAkBxM,GACd,OAAO,EAAMrB,KAAK2C,IAAImL,EAAMzM,GAAK,IAAO,CAC3C,EAeDjK,EAAQwW,aAbR,SAAsBwB,EAAIC,EAAIxF,EAAGyF,EAAQC,EAAkB,GAEvD,IADAtV,QAAQ6F,QAAQnD,MAAM4S,KAAqB5S,MAAM2S,MACpC,CACT,GAAID,EAAKD,GAAMG,EACX,MAAO,CAACH,EAAIC,GAChB,MAAMG,GAAOH,EAAKD,GAAM,EACZvF,EAAE2F,GACJF,EACNF,EAAKI,EAELH,EAAKG,CACZ,CACJ,EAGDpY,EAAQuW,KADR,YAAiBtU,GAAS,EAa1BjC,EAAQsW,2BAA6BA,EAoBrCtW,EAAQqW,yBAnBR,SAAkCmB,GAC9B,IAAIrC,EAAO,KACX,OAAQlT,IACJ,IAAIsF,EACJ,OAAY,MAAR4N,GACA5N,EAASiQ,EAAGvV,GACZkT,EAAO,CAAElT,OAAMsF,UACRA,IAEF+O,EAA2BnB,EAAKlT,KAAMA,KAI3CkT,EAAKlT,KAAOA,EACZkT,EAAK5N,OAASiQ,EAAGvV,IAJVkT,EAAK5N,OAMf,CAER,EAqBDvH,EAAQoW,mBAnBR,SAA4BoB,GACxB,IAAIrC,EAAO,KACX,OAAQlT,IACJ,IAAIsF,EACJ,OAAY,MAAR4N,GACA5N,EAASiQ,EAAGvV,GACZkT,EAAO,CAAElT,OAAMsF,UACRA,IAEF4N,EAAKlT,OAASA,IAInBkT,EAAKlT,KAAOA,EACZkT,EAAK5N,OAASiQ,EAAGvV,IAJVkT,EAAK5N,OAMf,CAER,EAWDvH,EAAQmW,WAAaA,EACrB,MAAMkC,EAAoBlC,GAAW,KACjC,MACM9B,EAAM,IAAI/S,IAChB,IAAK,IAAI+D,EAAI,EAAGA,EAAIiT,GAAiBjT,IACjCgP,EAAIrS,IAHS,mEAGIuW,OAAOlT,GAAIA,GAGhC,OADAgP,EAAIrS,IAAI,KAAM,GACPqS,CAAP,IA2FJrU,EAAQkW,aArFR,SAAsBsC,GAElB,MAAMC,EAAcJ,IAWpB,GAAIG,EAAQ1X,OAAS,GAAM,EACvB,MAAM,IAAI2C,MAAO,mFAAkF+U,EAAQ1X,UAE/G,MAAM4X,EAAeF,EAAQ1X,OAAS,EACtC,IAAI6X,EAyBQA,EAJRH,EAAQ1X,QAAU,GACyB,MAAvC0X,EAAQD,OAAOC,EAAQ1X,OAAS,GACW,MAAvC0X,EAAQD,OAAOC,EAAQ1X,OAAS,GAEL,EAAf4X,EAAmB,EAIJ,EAAfA,EAAmB,EAUZ,EAAfA,EAEhB,MAAME,EAAQ,IAAIC,WAAWF,GAC7B,IAAIG,EAAS,EACb,IAAK,IAAIzT,EAAI,EAAGA,EAAIqT,EAAcrT,IAAK,CACnC,MAAM0T,EAAOP,EAAQD,OAAW,EAAJlT,EAAQ,GAC9B2T,EAAOR,EAAQD,OAAW,EAAJlT,EAAQ,GAC9B4T,EAAOT,EAAQD,OAAW,EAAJlT,EAAQ,GAC9B6T,EAAOV,EAAQD,OAAW,EAAJlT,EAAQ,GAC9B8T,EAAUV,EAAY/V,IAAIqW,GAC1BK,EAAUX,EAAY/V,IAAIsW,GAC1BK,EAAUZ,EAAY/V,IAAIuW,GAC1BK,EAAUb,EAAY/V,IAAIwW,GAChC,GAAe,MAAXC,GAA8B,MAAXC,GAA8B,MAAXC,GAA8B,MAAXC,EACzD,MAAM,IAAI7V,MAAO,8BAAiC,EAAJ4B,QAAgB,EAAJA,EAAQ,MAAMmT,EAAQe,UAAc,EAAJlU,EAAW,EAAJA,EAAQ,MAE7GuT,EAAME,KAAaK,GAAW,EAAMC,GAAW,EAClC,MAATH,IACAL,EAAME,MAAwB,GAAVM,IAAiB,EAAMC,GAAW,GAE7C,MAATH,IACAN,EAAME,MAAwB,EAAVO,IAAgB,EAAKC,EAEhD,CACD,GAAIR,IAAWH,EACX,MAAM,IAAIlV,MAAO,sBAAqBkV,6BAAqCG,MAE/E,OAAOF,CACV,C,6BCpRD9Y,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtDD,EAAQwZ,cAAgBxZ,EAAQoG,cAAgBpG,EAAQoS,uBAAoB,EAC5E,MAAM5R,EAAUD,EAAQ,KASxBP,EAAQoS,kBARR,MACItK,cACIE,KAAK8D,KAAO,MACf,CACD0G,OAAOrJ,GACH,OAAOA,EAAEsQ,gBACZ,GAmCLzZ,EAAQoG,cAhCR,MACI0B,YAAYgE,GACR9D,KAAK8D,KAAOA,EAER9D,KAAK0R,WADI,gBAAT5N,EACkB,KACJ,iBAATA,EACa,KACJ,iBAATA,EACa,KAEA,CACzB,CACD6N,eAAexQ,GACX,MAAMmD,EAAInD,EAAInB,KAAK0R,WACnB,GAAIpN,EAAI,IAAM,EAAG,CACb,MAAMsN,EAAUhR,KAAK2O,MAAMjL,EAAI,IACzBuN,EAAUjR,KAAK2O,MAAMjL,EAAc,GAAVsN,GAAcE,WAC7C,MAAQ,GAAEF,KAAWpZ,EAAQoW,QAAQiD,EAAS,IACjD,CACD,OAAIvN,EAAI,GAAK,EACD,GAAEA,EAAEyL,QAAQ,MACpBzL,EAAI,MAAQ,EACJ,IAAGA,EAAI,MAAMyL,QAAQ,OAC7BzL,EAAI,MAAQ,EACJ,IAAGA,EAAI,MAAMyL,QAAQ,OAErB,IAAGzL,EAAI,MAAMyL,QAAQ,MACpC,CACDvF,OAAOrJ,GACH,MAAQ,GAAEA,EAAI,EAAI,IAAM,KAAKnB,KAAK2R,eAAe/Q,KAAK2C,IAAIpC,KAC7D,GAoBLnJ,EAAQwZ,cAjBR,MACI1R,cACIE,KAAK8D,KAAO,OACf,CACD0G,OAAOrJ,GACH,OAAIA,EAAI,KACI,GAAEA,EAAE4O,QAAQ,QACxB5O,GAAK,MACG,KACI,GAAEA,EAAE4O,QAAQ,SACxB5O,GAAK,MACG,KACI,GAAEA,EAAE4O,QAAQ,QAEhB,IADR5O,GAAK,MACO4O,QAAQ,OACvB,E,kCC5DL,IAAI/I,EAAmBhH,MAAQA,KAAKgH,kBAAqBlP,OAAOmP,OAAU,SAASC,EAAGC,EAAGC,EAAGC,QAC7E7H,IAAP6H,IAAkBA,EAAKD,GAC3BtP,OAAOC,eAAemP,EAAGG,EAAI,CAAEC,YAAY,EAAM5M,IAAK,WAAa,OAAOyM,EAAEC,EAAK,GACpF,EAAK,SAASF,EAAGC,EAAGC,EAAGC,QACT7H,IAAP6H,IAAkBA,EAAKD,GAC3BF,EAAEG,GAAMF,EAAEC,EACb,GACG2K,EAAgB/R,MAAQA,KAAK+R,cAAiB,SAAS5K,EAAGnP,GAC1D,IAAK,IAAIwO,KAAKW,EAAa,YAANX,GAAoBxO,EAAQ2P,eAAenB,IAAIQ,EAAgBhP,EAASmP,EAAGX,EACnG,EACD1O,OAAOC,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtD8Z,EAAaxZ,EAAQ,KAAoBP,GACzC+Z,EAAaxZ,EAAQ,KAAqBP,E,sBCbzC,oBAASyG,EAAMuT,GACZ,aAKIC,EAA6B,CAAC,aAAxB,4BAMN,SAA0BC,GAG9B,IAAIC,EAA8B,eAC9BC,EAAyB,iCACzBC,EAA4B,8BAEhC,MAAO,CAOHC,MAAO,SAAiCC,GACpC,QAAgC,IAArBA,EAAMC,iBAAkE,IAA7BD,EAAM,mBACxD,OAAOvS,KAAKyS,WAAWF,GACpB,GAAIA,EAAMnS,OAASmS,EAAMnS,MAAMsS,MAAMN,GACxC,OAAOpS,KAAK2S,YAAYJ,GACrB,GAAIA,EAAMnS,MACb,OAAOJ,KAAK4S,gBAAgBL,GAE5B,MAAM,IAAI9W,MAAM,kCAEvB,EAGDoX,gBAAiB,SAA2CC,GAExD,IAA8B,IAA1BA,EAAQnV,QAAQ,KAChB,MAAO,CAACmV,GAGZ,IACIC,EADS,+BACMC,KAAKF,EAAQG,QAAQ,QAAS,KACjD,MAAO,CAACF,EAAM,GAAIA,EAAM,SAAMvT,EAAWuT,EAAM,SAAMvT,EACxD,EAEDmT,YAAa,SAAuCJ,GAKhD,OAJeA,EAAMnS,MAAM8S,MAAM,MAAMC,QAAO,SAASlX,GACnD,QAASA,EAAKyW,MAAMN,EACvB,GAAEpS,MAEanB,KAAI,SAAS5C,GACrBA,EAAK0B,QAAQ,WAAa,IAE1B1B,EAAOA,EAAKgX,QAAQ,aAAc,QAAQA,QAAQ,+BAAgC,KAEtF,IAAIG,EAAgBnX,EAAKgX,QAAQ,OAAQ,IAAIA,QAAQ,eAAgB,KAIjEI,EAAWD,EAAcV,MAAM,4BAK/BY,GAFJF,EAAgBC,EAAWD,EAAcH,QAAQI,EAAS,GAAI,IAAMD,GAEzCF,MAAM,OAAOvT,MAAM,GAE1C4T,EAAgBvT,KAAK6S,gBAAgBQ,EAAWA,EAAS,GAAKC,EAAOxV,OACrEhC,EAAewX,EAAO1D,KAAK,WAAQpQ,EACnCpG,EAAW,CAAC,OAAQ,eAAeuE,QAAQ4V,EAAc,KAAO,OAAI/T,EAAY+T,EAAc,GAElG,OAAO,IAAIrB,EAAW,CAClBpW,aAAcA,EACd1C,SAAUA,EACV8C,WAAYqX,EAAc,GAC1BnX,aAAcmX,EAAc,GAC5BxT,OAAQ9D,GAEf,GAAE+D,KACN,EAED4S,gBAAiB,SAA2CL,GAKxD,OAJeA,EAAMnS,MAAM8S,MAAM,MAAMC,QAAO,SAASlX,GACnD,OAAQA,EAAKyW,MAAML,EACtB,GAAErS,MAEanB,KAAI,SAAS5C,GAMzB,GAJIA,EAAK0B,QAAQ,YAAc,IAC3B1B,EAAOA,EAAKgX,QAAQ,mDAAoD,SAGjD,IAAvBhX,EAAK0B,QAAQ,OAAsC,IAAvB1B,EAAK0B,QAAQ,KAEzC,OAAO,IAAIuU,EAAW,CAClBpW,aAAcG,IAGlB,IAAIuX,EAAoB,6BACpBC,EAAUxX,EAAKyW,MAAMc,GACrB1X,EAAe2X,GAAWA,EAAQ,GAAKA,EAAQ,QAAKjU,EACpD+T,EAAgBvT,KAAK6S,gBAAgB5W,EAAKgX,QAAQO,EAAmB,KAEzE,OAAO,IAAItB,EAAW,CAClBpW,aAAcA,EACd1C,SAAUma,EAAc,GACxBrX,WAAYqX,EAAc,GAC1BnX,aAAcmX,EAAc,GAC5BxT,OAAQ9D,GAGnB,GAAE+D,KACN,EAEDyS,WAAY,SAAsCxZ,GAC9C,OAAKA,EAAEuZ,YAAevZ,EAAEya,QAAQ/V,QAAQ,OAAS,GAC7C1E,EAAEya,QAAQR,MAAM,MAAMpa,OAASG,EAAEuZ,WAAWU,MAAM,MAAMpa,OACjDkH,KAAK2T,YAAY1a,GAChBA,EAAEmH,MAGHJ,KAAK4T,aAAa3a,GAFlB+G,KAAK6T,aAAa5a,EAIhC,EAED0a,YAAa,SAAuC1a,GAKhD,IAJA,IAAI6a,EAAS,oCACTC,EAAQ9a,EAAEya,QAAQR,MAAM,MACxB3T,EAAS,GAEJlC,EAAI,EAAG2W,EAAMD,EAAMjb,OAAQuE,EAAI2W,EAAK3W,GAAK,EAAG,CACjD,IAAIqV,EAAQoB,EAAOd,KAAKe,EAAM1W,IAC1BqV,GACAnT,EAAO/D,KAAK,IAAI0W,EAAW,CACvB9Y,SAAUsZ,EAAM,GAChBxW,WAAYwW,EAAM,GAClB3S,OAAQgU,EAAM1W,KAGzB,CAED,OAAOkC,CACV,EAEDsU,aAAc,SAAwC5a,GAKlD,IAJA,IAAI6a,EAAS,6DACTC,EAAQ9a,EAAEuZ,WAAWU,MAAM,MAC3B3T,EAAS,GAEJlC,EAAI,EAAG2W,EAAMD,EAAMjb,OAAQuE,EAAI2W,EAAK3W,GAAK,EAAG,CACjD,IAAIqV,EAAQoB,EAAOd,KAAKe,EAAM1W,IAC1BqV,GACAnT,EAAO/D,KACH,IAAI0W,EAAW,CACXpW,aAAc4W,EAAM,SAAMlT,EAC1BpG,SAAUsZ,EAAM,GAChBxW,WAAYwW,EAAM,GAClB3S,OAAQgU,EAAM1W,KAI7B,CAED,OAAOkC,CACV,EAGDqU,aAAc,SAAwCrB,GAKlD,OAJeA,EAAMnS,MAAM8S,MAAM,MAAMC,QAAO,SAASlX,GACnD,QAASA,EAAKyW,MAAMP,KAAiClW,EAAKyW,MAAM,oBACnE,GAAE1S,MAEanB,KAAI,SAAS5C,GACzB,IAMIgY,EANAX,EAASrX,EAAKiX,MAAM,KACpBK,EAAgBvT,KAAK6S,gBAAgBS,EAAOxV,OAC5CoW,EAAgBZ,EAAOa,SAAW,GAClCrY,EAAeoY,EACdjB,QAAQ,iCAAkC,MAC1CA,QAAQ,aAAc,UAAOzT,EAE9B0U,EAAaxB,MAAM,iBACnBuB,EAAUC,EAAajB,QAAQ,qBAAsB,OAEzD,IAAIhZ,OAAoBuF,IAAZyU,GAAqC,8BAAZA,OACjCzU,EAAYyU,EAAQf,MAAM,KAE9B,OAAO,IAAIhB,EAAW,CAClBpW,aAAcA,EACd7B,KAAMA,EACNb,SAAUma,EAAc,GACxBrX,WAAYqX,EAAc,GAC1BnX,aAAcmX,EAAc,GAC5BxT,OAAQ9D,GAEf,GAAE+D,KACN,EAER,GAnMa,8BANb,G,sBCED,SAASoU,EAAWnS,EAAGyR,GACrB,GAAS,MAALzR,EACF,OAAOA,EAET,IAAIsQ,EAAQ,IAAI9W,WAAkB+D,IAAZkU,EAAwBA,EAAU,kBAAoBzR,GAE5E,MADAsQ,EAAM8B,YAAc,EACd9B,CACP,CAED+B,EAAOtc,QAAUoc,EACjBE,EAAOtc,QAAPsc,QAAyBF,EAEzBtc,OAAOC,eAAeuc,EAAOtc,QAAS,aAAtCF,CAAqDG,OAAO,G,oBCd3D,oBAASwG,EAAMuT,GACZ,aAKIC,EAAqB,QAAf,4BAMN,WAMJ,SAASsC,EAAYC,GACjB,OAAOA,EAAIjE,OAAO,GAAGkE,cAAgBD,EAAIjD,UAAU,EACtD,CAED,SAASmD,EAAQlO,GACb,OAAO,WACH,OAAOxG,KAAKwG,EACf,CACJ,CAED,IAAImO,EAAe,CAAC,gBAAiB,SAAU,WAAY,cACvDC,EAAe,CAAC,eAAgB,cAChCC,EAAc,CAAC,WAAY,eAAgB,UAG3CC,EAAQH,EAAa/Z,OAAOga,EAAcC,EAF7B,CAAC,SAIlB,SAAS3C,EAAW6C,GAChB,GAAKA,EACL,IAAK,IAAI1X,EAAI,EAAGA,EAAIyX,EAAMhc,OAAQuE,SACRmC,IAAlBuV,EAAID,EAAMzX,KACV2C,KAAK,MAAQuU,EAAYO,EAAMzX,KAAK0X,EAAID,EAAMzX,IAGzD,CAED6U,EAAW8C,UAAY,CACnBC,QAAS,WACL,OAAOjV,KAAK/F,IACf,EACDib,QAAS,SAAS/T,GACd,GAA0C,mBAAtCrJ,OAAOkd,UAAUlD,SAASlK,KAAKzG,GAC/B,MAAM,IAAIgU,UAAU,yBAExBnV,KAAK/F,KAAOkH,CACf,EAEDiU,cAAe,WACX,OAAOpV,KAAKqV,UACf,EACDC,cAAe,SAASnU,GACpB,GAAIA,aAAa+Q,EACblS,KAAKqV,WAAalU,MACf,MAAIA,aAAarJ,QAGpB,MAAM,IAAIqd,UAAU,+CAFpBnV,KAAKqV,WAAa,IAAInD,EAAW/Q,EAGpC,CACJ,EAED2Q,SAAU,WACN,IAAI1Y,EAAW4G,KAAKuV,eAAiB,GACjCrZ,EAAa8D,KAAKwV,iBAAmB,GACrCpZ,EAAe4D,KAAKyV,mBAAqB,GACzC3Z,EAAekE,KAAK0V,mBAAqB,GAC7C,OAAI1V,KAAK2V,YACDvc,EACO,WAAaA,EAAW,IAAM8C,EAAa,IAAME,EAAe,IAEpE,UAAYF,EAAa,IAAME,EAEtCN,EACOA,EAAe,KAAO1C,EAAW,IAAM8C,EAAa,IAAME,EAAe,IAE7EhD,EAAW,IAAM8C,EAAa,IAAME,CAC9C,GAGL8V,EAAW0D,WAAa,SAAgCpB,GACpD,IAAIqB,EAAiBrB,EAAI7W,QAAQ,KAC7BmY,EAAetB,EAAIuB,YAAY,KAE/Bja,EAAe0Y,EAAIjD,UAAU,EAAGsE,GAChC5b,EAAOua,EAAIjD,UAAUsE,EAAiB,EAAGC,GAAc5C,MAAM,KAC7D8C,EAAiBxB,EAAIjD,UAAUuE,EAAe,GAElD,GAAoC,IAAhCE,EAAerY,QAAQ,KACvB,IAAIoV,EAAQ,gCAAgCC,KAAKgD,EAAgB,IAC7D5c,EAAW2Z,EAAM,GACjB7W,EAAa6W,EAAM,GACnB3W,EAAe2W,EAAM,GAG7B,OAAO,IAAIb,EAAW,CAClBpW,aAAcA,EACd7B,KAAMA,QAAQuF,EACdpG,SAAUA,EACV8C,WAAYA,QAAcsD,EAC1BpD,aAAcA,QAAgBoD,GAErC,EAED,IAAK,IAAInC,EAAI,EAAGA,EAAIsX,EAAa7b,OAAQuE,IACrC6U,EAAW8C,UAAU,MAAQT,EAAYI,EAAatX,KAAOqX,EAAQC,EAAatX,IAClF6U,EAAW8C,UAAU,MAAQT,EAAYI,EAAatX,KAAQ,SAASmJ,GACnE,OAAO,SAASrF,GACZnB,KAAKwG,GAAKyP,QAAQ9U,EACrB,CACJ,CAJ6D,CAI3DwT,EAAatX,IAGpB,IAAK,IAAI6Y,EAAI,EAAGA,EAAItB,EAAa9b,OAAQod,IACrChE,EAAW8C,UAAU,MAAQT,EAAYK,EAAasB,KAAOxB,EAAQE,EAAasB,IAClFhE,EAAW8C,UAAU,MAAQT,EAAYK,EAAasB,KAAQ,SAAS1P,GACnE,OAAO,SAASrF,GACZ,GA7GO2J,EA6GQ3J,EA5Gf5D,MAAM4Y,WAAWrL,MAAOhK,SAASgK,GA6G7B,MAAM,IAAIqK,UAAU3O,EAAI,qBA9GxC,IAAmBsE,EAgHP9K,KAAKwG,GAAK4P,OAAOjV,EACpB,CACJ,CAP6D,CAO3DyT,EAAasB,IAGpB,IAAK,IAAI9O,EAAI,EAAGA,EAAIyN,EAAY/b,OAAQsO,IACpC8K,EAAW8C,UAAU,MAAQT,EAAYM,EAAYzN,KAAOsN,EAAQG,EAAYzN,IAChF8K,EAAW8C,UAAU,MAAQT,EAAYM,EAAYzN,KAAQ,SAASZ,GAClE,OAAO,SAASrF,GACZnB,KAAKwG,GAAK6P,OAAOlV,EACpB,CACJ,CAJ4D,CAI1D0T,EAAYzN,IAGnB,OAAO8K,CACV,GAvIa,8BANb,G,UCOD,IAAIoE,EAAW,SAAUte,GACvB,aAEA,IAEIwH,EAFA+W,EAAKze,OAAOkd,UACZwB,EAASD,EAAG5O,eAEZ8O,EAA4B,mBAAXrH,OAAwBA,OAAS,CAAC,EACnDsH,EAAiBD,EAAQpH,UAAY,aACrCsH,EAAsBF,EAAQG,eAAiB,kBAC/CC,EAAoBJ,EAAQK,aAAe,gBAE/C,SAAS7E,EAAO8C,EAAK1Y,EAAKpE,GAOxB,OANAH,OAAOC,eAAegd,EAAK1Y,EAAK,CAC9BpE,MAAOA,EACPqP,YAAY,EACZyP,cAAc,EACdC,UAAU,IAELjC,EAAI1Y,EACZ,CACD,IAEE4V,EAAO,CAAC,EAAG,GACZ,CAAC,MAAOgF,GACPhF,EAAS,SAAS8C,EAAK1Y,EAAKpE,GAC1B,OAAO8c,EAAI1Y,GAAOpE,CACnB,CACF,CAED,SAASif,EAAKC,EAASC,EAASC,EAAMC,GAEpC,IAAIC,EAAiBH,GAAWA,EAAQpC,qBAAqBwC,EAAYJ,EAAUI,EAC/EvP,EAAYnQ,OAAOmP,OAAOsQ,EAAevC,WACzCyC,EAAU,IAAIC,EAAQJ,GAAe,IAMzC,OAFArP,EAAU0P,QAsMZ,SAA0BR,EAASE,EAAMI,GACvC,IAAIG,EAAQC,EAEZ,OAAO,SAAgBC,EAAQC,GAC7B,GAAIH,IAAUI,EACZ,MAAM,IAAIvc,MAAM,gCAGlB,GAAImc,IAAUK,EAAmB,CAC/B,GAAe,UAAXH,EACF,MAAMC,EAKR,OAAOG,GACR,CAKD,IAHAT,EAAQK,OAASA,EACjBL,EAAQM,IAAMA,IAED,CACX,IAAII,EAAWV,EAAQU,SACvB,GAAIA,EAAU,CACZ,IAAIC,EAAiBC,EAAoBF,EAAUV,GACnD,GAAIW,EAAgB,CAClB,GAAIA,IAAmBE,EAAkB,SACzC,OAAOF,CACR,CACF,CAED,GAAuB,SAAnBX,EAAQK,OAGVL,EAAQc,KAAOd,EAAQe,MAAQf,EAAQM,SAElC,GAAuB,UAAnBN,EAAQK,OAAoB,CACrC,GAAIF,IAAUC,EAEZ,MADAD,EAAQK,EACFR,EAAQM,IAGhBN,EAAQgB,kBAAkBhB,EAAQM,IAEnC,KAA6B,WAAnBN,EAAQK,QACjBL,EAAQiB,OAAO,SAAUjB,EAAQM,KAGnCH,EAAQI,EAER,IAAIW,EAASC,EAASzB,EAASE,EAAMI,GACrC,GAAoB,WAAhBkB,EAAOE,KAAmB,CAO5B,GAJAjB,EAAQH,EAAQhP,KACZwP,EACAa,EAEAH,EAAOZ,MAAQO,EACjB,SAGF,MAAO,CACLrgB,MAAO0gB,EAAOZ,IACdtP,KAAMgP,EAAQhP,KAGjB,CAA0B,UAAhBkQ,EAAOE,OAChBjB,EAAQK,EAGRR,EAAQK,OAAS,QACjBL,EAAQM,IAAMY,EAAOZ,IAExB,CACF,CACF,CAlRqBgB,CAAiB5B,EAASE,EAAMI,GAE7CxP,CACR,CAaD,SAAS2Q,EAASpN,EAAIuJ,EAAKgD,GACzB,IACE,MAAO,CAAEc,KAAM,SAAUd,IAAKvM,EAAG5D,KAAKmN,EAAKgD,GAC5C,CAAC,MAAOd,GACP,MAAO,CAAE4B,KAAM,QAASd,IAAKd,EAC9B,CACF,CAlBDjf,EAAQkf,KAAOA,EAoBf,IAAIW,EAAyB,iBACzBiB,EAAyB,iBACzBd,EAAoB,YACpBC,EAAoB,YAIpBK,EAAmB,CAAC,EAMxB,SAASd,IAAc,CACvB,SAASwB,IAAsB,CAC/B,SAASC,IAA+B,CAIxC,IAAIC,EAAoB,CAAC,EACzBA,EAAkBxC,GAAkB,WAClC,OAAO1W,IACR,EAED,IAAImZ,EAAWrhB,OAAOshB,eAClBC,EAA0BF,GAAYA,EAASA,EAAS7J,EAAO,MAC/D+J,GACAA,IAA4B9C,GAC5BC,EAAO5O,KAAKyR,EAAyB3C,KAGvCwC,EAAoBG,GAGtB,IAAIC,EAAKL,EAA2BjE,UAClCwC,EAAUxC,UAAYld,OAAOmP,OAAOiS,GAWtC,SAASK,EAAsBvE,GAC7B,CAAC,OAAQ,QAAS,UAAUlW,SAAQ,SAASgZ,GAC3C7F,EAAO+C,EAAW8C,GAAQ,SAASC,GACjC,OAAO/X,KAAK2X,QAAQG,EAAQC,EAC7B,GACF,GACF,CA+BD,SAASyB,EAAcvR,EAAWwR,GAChC,SAASC,EAAO5B,EAAQC,EAAK5P,EAASC,GACpC,IAAIuQ,EAASC,EAAS3Q,EAAU6P,GAAS7P,EAAW8P,GACpD,GAAoB,UAAhBY,EAAOE,KAEJ,CACL,IAAItZ,EAASoZ,EAAOZ,IAChB9f,EAAQsH,EAAOtH,MACnB,OAAIA,GACiB,iBAAVA,GACPue,EAAO5O,KAAK3P,EAAO,WACdwhB,EAAYtR,QAAQlQ,EAAM0hB,SAASjR,MAAK,SAASzQ,GACtDyhB,EAAO,OAAQzhB,EAAOkQ,EAASC,EAChC,IAAE,SAAS6O,GACVyC,EAAO,QAASzC,EAAK9O,EAASC,EAC/B,IAGIqR,EAAYtR,QAAQlQ,GAAOyQ,MAAK,SAASkR,GAI9Cra,EAAOtH,MAAQ2hB,EACfzR,EAAQ5I,EACT,IAAE,SAASgT,GAGV,OAAOmH,EAAO,QAASnH,EAAOpK,EAASC,EACxC,GACF,CAzBCA,EAAOuQ,EAAOZ,IA0BjB,CAED,IAAI8B,EAgCJ7Z,KAAK2X,QA9BL,SAAiBG,EAAQC,GACvB,SAAS+B,IACP,OAAO,IAAIL,GAAY,SAAStR,EAASC,GACvCsR,EAAO5B,EAAQC,EAAK5P,EAASC,EAC9B,GACF,CAED,OAAOyR,EAaLA,EAAkBA,EAAgBnR,KAChCoR,EAGAA,GACEA,GACP,CAKF,CA4GD,SAASzB,EAAoBF,EAAUV,GACrC,IAAIK,EAASK,EAAS9I,SAASoI,EAAQK,QACvC,GAAIA,IAAWtY,EAAW,CAKxB,GAFAiY,EAAQU,SAAW,KAEI,UAAnBV,EAAQK,OAAoB,CAE9B,GAAIK,EAAS9I,SAAT,SAGFoI,EAAQK,OAAS,SACjBL,EAAQM,IAAMvY,EACd6Y,EAAoBF,EAAUV,GAEP,UAAnBA,EAAQK,QAGV,OAAOQ,EAIXb,EAAQK,OAAS,QACjBL,EAAQM,IAAM,IAAI5C,UAChB,iDACH,CAED,OAAOmD,CACR,CAED,IAAIK,EAASC,EAASd,EAAQK,EAAS9I,SAAUoI,EAAQM,KAEzD,GAAoB,UAAhBY,EAAOE,KAIT,OAHApB,EAAQK,OAAS,QACjBL,EAAQM,IAAMY,EAAOZ,IACrBN,EAAQU,SAAW,KACZG,EAGT,IAAI9O,EAAOmP,EAAOZ,IAElB,OAAMvO,EAOFA,EAAKf,MAGPgP,EAAQU,EAAS4B,YAAcvQ,EAAKvR,MAGpCwf,EAAQlP,KAAO4P,EAAS6B,QAQD,WAAnBvC,EAAQK,SACVL,EAAQK,OAAS,OACjBL,EAAQM,IAAMvY,GAUlBiY,EAAQU,SAAW,KACZG,GANE9O,GA3BPiO,EAAQK,OAAS,QACjBL,EAAQM,IAAM,IAAI5C,UAAU,oCAC5BsC,EAAQU,SAAW,KACZG,EA+BV,CAqBD,SAAS2B,EAAaC,GACpB,IAAIC,EAAQ,CAAEC,OAAQF,EAAK,IAEvB,KAAKA,IACPC,EAAME,SAAWH,EAAK,IAGpB,KAAKA,IACPC,EAAMG,WAAaJ,EAAK,GACxBC,EAAMI,SAAWL,EAAK,IAGxBla,KAAKwa,WAAWhf,KAAK2e,EACtB,CAED,SAASM,EAAcN,GACrB,IAAIxB,EAASwB,EAAMO,YAAc,CAAC,EAClC/B,EAAOE,KAAO,gBACPF,EAAOZ,IACdoC,EAAMO,WAAa/B,CACpB,CAED,SAASjB,EAAQJ,GAIftX,KAAKwa,WAAa,CAAC,CAAEJ,OAAQ,SAC7B9C,EAAYxY,QAAQmb,EAAcja,MAClCA,KAAK2a,OAAM,EACZ,CA6BD,SAASrL,EAAOsL,GACd,GAAIA,EAAU,CACZ,IAAIC,EAAiBD,EAASlE,GAC9B,GAAImE,EACF,OAAOA,EAAejT,KAAKgT,GAG7B,GAA6B,mBAAlBA,EAASrS,KAClB,OAAOqS,EAGT,IAAKrd,MAAMqd,EAAS9hB,QAAS,CAC3B,IAAIuE,GAAK,EAAGkL,EAAO,SAASA,IAC1B,OAASlL,EAAIud,EAAS9hB,QACpB,GAAI0d,EAAO5O,KAAKgT,EAAUvd,GAGxB,OAFAkL,EAAKtQ,MAAQ2iB,EAASvd,GACtBkL,EAAKE,MAAO,EACLF,EAOX,OAHAA,EAAKtQ,MAAQuH,EACb+I,EAAKE,MAAO,EAELF,CACR,EAED,OAAOA,EAAKA,KAAOA,CACpB,CACF,CAGD,MAAO,CAAEA,KAAM2P,EAChB,CAGD,SAASA,IACP,MAAO,CAAEjgB,MAAOuH,EAAWiJ,MAAM,EAClC,CA8MD,OA5mBAuQ,EAAkBhE,UAAYsE,EAAGxZ,YAAcmZ,EAC/CA,EAA2BnZ,YAAckZ,EACzCA,EAAkB8B,YAAc7I,EAC9BgH,EACApC,EACA,qBAaF7e,EAAQ+iB,oBAAsB,SAASC,GACrC,IAAIC,EAAyB,mBAAXD,GAAyBA,EAAOlb,YAClD,QAAOmb,IACHA,IAASjC,GAG2B,uBAAnCiC,EAAKH,aAAeG,EAAK/hB,MAE/B,EAEDlB,EAAQkjB,KAAO,SAASF,GAQtB,OAPIljB,OAAOqjB,eACTrjB,OAAOqjB,eAAeH,EAAQ/B,IAE9B+B,EAAOI,UAAYnC,EACnBhH,EAAO+I,EAAQnE,EAAmB,sBAEpCmE,EAAOhG,UAAYld,OAAOmP,OAAOqS,GAC1B0B,CACR,EAMDhjB,EAAQqjB,MAAQ,SAAStD,GACvB,MAAO,CAAE4B,QAAS5B,EACnB,EAqEDwB,EAAsBC,EAAcxE,WACpCwE,EAAcxE,UAAU2B,GAAuB,WAC7C,OAAO3W,IACR,EACDhI,EAAQwhB,cAAgBA,EAKxBxhB,EAAQsjB,MAAQ,SAASnE,EAASC,EAASC,EAAMC,EAAamC,QACxC,IAAhBA,IAAwBA,EAAcvR,SAE1C,IAAIqT,EAAO,IAAI/B,EACbtC,EAAKC,EAASC,EAASC,EAAMC,GAC7BmC,GAGF,OAAOzhB,EAAQ+iB,oBAAoB3D,GAC/BmE,EACAA,EAAKhT,OAAOG,MAAK,SAASnJ,GACxB,OAAOA,EAAOkJ,KAAOlJ,EAAOtH,MAAQsjB,EAAKhT,MAC1C,GACN,EAqKDgR,EAAsBD,GAEtBrH,EAAOqH,EAAIzC,EAAmB,aAO9ByC,EAAG5C,GAAkB,WACnB,OAAO1W,IACR,EAEDsZ,EAAGxH,SAAW,WACZ,MAAO,oBACR,EAiCD9Z,EAAQmD,KAAO,SAASqgB,GACtB,IAAIrgB,EAAO,GACX,IAAK,IAAIkB,KAAOmf,EACdrgB,EAAKK,KAAKa,GAMZ,OAJAlB,EAAK8C,UAIE,SAASsK,IACd,KAAOpN,EAAKrC,QAAQ,CAClB,IAAIuD,EAAMlB,EAAK2C,MACf,GAAIzB,KAAOmf,EAGT,OAFAjT,EAAKtQ,MAAQoE,EACbkM,EAAKE,MAAO,EACLF,CAEV,CAMD,OADAA,EAAKE,MAAO,EACLF,CACR,CACF,EAoCDvQ,EAAQsX,OAASA,EAMjBoI,EAAQ1C,UAAY,CAClBlV,YAAa4X,EAEbiD,MAAO,SAASc,GAcd,GAbAzb,KAAK0b,KAAO,EACZ1b,KAAKuI,KAAO,EAGZvI,KAAKuY,KAAOvY,KAAKwY,MAAQhZ,EACzBQ,KAAKyI,MAAO,EACZzI,KAAKmY,SAAW,KAEhBnY,KAAK8X,OAAS,OACd9X,KAAK+X,IAAMvY,EAEXQ,KAAKwa,WAAW1b,QAAQ2b,IAEnBgB,EACH,IAAK,IAAIviB,KAAQ8G,KAEQ,MAAnB9G,EAAKqX,OAAO,IACZiG,EAAO5O,KAAK5H,KAAM9G,KACjBqE,OAAOrE,EAAKyG,MAAM,MACrBK,KAAK9G,GAAQsG,EAIpB,EAEDmc,KAAM,WACJ3b,KAAKyI,MAAO,EAEZ,IACImT,EADY5b,KAAKwa,WAAW,GACLE,WAC3B,GAAwB,UAApBkB,EAAW/C,KACb,MAAM+C,EAAW7D,IAGnB,OAAO/X,KAAK6b,IACb,EAEDpD,kBAAmB,SAASqD,GAC1B,GAAI9b,KAAKyI,KACP,MAAMqT,EAGR,IAAIrE,EAAUzX,KACd,SAAS+b,EAAOC,EAAKC,GAYnB,OAXAtD,EAAOE,KAAO,QACdF,EAAOZ,IAAM+D,EACbrE,EAAQlP,KAAOyT,EAEXC,IAGFxE,EAAQK,OAAS,OACjBL,EAAQM,IAAMvY,KAGNyc,CACX,CAED,IAAK,IAAI5e,EAAI2C,KAAKwa,WAAW1hB,OAAS,EAAGuE,GAAK,IAAKA,EAAG,CACpD,IAAI8c,EAAQna,KAAKwa,WAAWnd,GACxBsb,EAASwB,EAAMO,WAEnB,GAAqB,SAAjBP,EAAMC,OAIR,OAAO2B,EAAO,OAGhB,GAAI5B,EAAMC,QAAUpa,KAAK0b,KAAM,CAC7B,IAAIQ,EAAW1F,EAAO5O,KAAKuS,EAAO,YAC9BgC,EAAa3F,EAAO5O,KAAKuS,EAAO,cAEpC,GAAI+B,GAAYC,EAAY,CAC1B,GAAInc,KAAK0b,KAAOvB,EAAME,SACpB,OAAO0B,EAAO5B,EAAME,UAAU,GACzB,GAAIra,KAAK0b,KAAOvB,EAAMG,WAC3B,OAAOyB,EAAO5B,EAAMG,WAGvB,MAAM,GAAI4B,GACT,GAAIlc,KAAK0b,KAAOvB,EAAME,SACpB,OAAO0B,EAAO5B,EAAME,UAAU,OAG3B,KAAI8B,EAMT,MAAM,IAAI1gB,MAAM,0CALhB,GAAIuE,KAAK0b,KAAOvB,EAAMG,WACpB,OAAOyB,EAAO5B,EAAMG,WAKvB,CACF,CACF,CACF,EAED5B,OAAQ,SAASG,EAAMd,GACrB,IAAK,IAAI1a,EAAI2C,KAAKwa,WAAW1hB,OAAS,EAAGuE,GAAK,IAAKA,EAAG,CACpD,IAAI8c,EAAQna,KAAKwa,WAAWnd,GAC5B,GAAI8c,EAAMC,QAAUpa,KAAK0b,MACrBlF,EAAO5O,KAAKuS,EAAO,eACnBna,KAAK0b,KAAOvB,EAAMG,WAAY,CAChC,IAAI8B,EAAejC,EACnB,KACD,CACF,CAEGiC,IACU,UAATvD,GACS,aAATA,IACDuD,EAAahC,QAAUrC,GACvBA,GAAOqE,EAAa9B,aAGtB8B,EAAe,MAGjB,IAAIzD,EAASyD,EAAeA,EAAa1B,WAAa,CAAC,EAIvD,OAHA/B,EAAOE,KAAOA,EACdF,EAAOZ,IAAMA,EAETqE,GACFpc,KAAK8X,OAAS,OACd9X,KAAKuI,KAAO6T,EAAa9B,WAClBhC,GAGFtY,KAAKqc,SAAS1D,EACtB,EAED0D,SAAU,SAAS1D,EAAQ4B,GACzB,GAAoB,UAAhB5B,EAAOE,KACT,MAAMF,EAAOZ,IAcf,MAXoB,UAAhBY,EAAOE,MACS,aAAhBF,EAAOE,KACT7Y,KAAKuI,KAAOoQ,EAAOZ,IACM,WAAhBY,EAAOE,MAChB7Y,KAAK6b,KAAO7b,KAAK+X,IAAMY,EAAOZ,IAC9B/X,KAAK8X,OAAS,SACd9X,KAAKuI,KAAO,OACa,WAAhBoQ,EAAOE,MAAqB0B,IACrCva,KAAKuI,KAAOgS,GAGPjC,CACR,EAEDgE,OAAQ,SAAShC,GACf,IAAK,IAAIjd,EAAI2C,KAAKwa,WAAW1hB,OAAS,EAAGuE,GAAK,IAAKA,EAAG,CACpD,IAAI8c,EAAQna,KAAKwa,WAAWnd,GAC5B,GAAI8c,EAAMG,aAAeA,EAGvB,OAFAta,KAAKqc,SAASlC,EAAMO,WAAYP,EAAMI,UACtCE,EAAcN,GACP7B,CAEV,CACF,EAED,MAAS,SAAS8B,GAChB,IAAK,IAAI/c,EAAI2C,KAAKwa,WAAW1hB,OAAS,EAAGuE,GAAK,IAAKA,EAAG,CACpD,IAAI8c,EAAQna,KAAKwa,WAAWnd,GAC5B,GAAI8c,EAAMC,SAAWA,EAAQ,CAC3B,IAAIzB,EAASwB,EAAMO,WACnB,GAAoB,UAAhB/B,EAAOE,KAAkB,CAC3B,IAAI0D,EAAS5D,EAAOZ,IACpB0C,EAAcN,EACf,CACD,OAAOoC,CACR,CACF,CAID,MAAM,IAAI9gB,MAAM,wBACjB,EAED+gB,cAAe,SAAS5B,EAAUb,EAAYC,GAa5C,OAZAha,KAAKmY,SAAW,CACd9I,SAAUC,EAAOsL,GACjBb,WAAYA,EACZC,QAASA,GAGS,SAAhBha,KAAK8X,SAGP9X,KAAK+X,IAAMvY,GAGN8Y,CACR,GAOItgB,CAER,CA/sBc,CAotBgBsc,EAAOtc,SAGtC,IACEykB,mBAAqBnG,CACtB,CAAC,MAAOoG,GAUPjd,SAAS,IAAK,yBAAdA,CAAwC6W,EACzC,C,GC1uBGqG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBrd,IAAjBsd,EACH,OAAOA,EAAa9kB,QAGrB,IAAIsc,EAASqI,EAAyBE,GAAY,CAGjD7kB,QAAS,CAAC,GAOX,OAHA+kB,EAAoBF,GAAUjV,KAAK0M,EAAOtc,QAASsc,EAAQA,EAAOtc,QAAS4kB,GAGpEtI,EAAOtc,OACf,CCrBA4kB,EAAoB9R,EAAKwJ,IACxB,IAAI0I,EAAS1I,GAAUA,EAAO5M,WAC7B,IAAO4M,EAAiB,QACxB,IAAM,EAEP,OADAsI,EAAoBK,EAAED,EAAQ,CAAErZ,EAAGqZ,IAC5BA,CAAM,ECLdJ,EAAoBK,EAAI,CAACjlB,EAASklB,KACjC,IAAI,IAAI7gB,KAAO6gB,EACXN,EAAoB1V,EAAEgW,EAAY7gB,KAASugB,EAAoB1V,EAAElP,EAASqE,IAC5EvE,OAAOC,eAAeC,EAASqE,EAAK,CAAEiL,YAAY,EAAM5M,IAAKwiB,EAAW7gB,IAE1E,ECNDugB,EAAoB1V,EAAI,CAAC6N,EAAKoI,IAAUrlB,OAAOkd,UAAUrN,eAAeC,KAAKmN,EAAKoI,G,yECA3E,MCcMC,GDkU2BC,SARd,OAUxB,IAEkCA,SArBV,OAuBxB,ICxUmC,IAGxBC,EAA8B,ECL5B,MAAMC,UAA4B9hB,OCZjD,SAAS+hB,EAAeC,EAAWC,GAC/B,GAAID,EAAU3kB,SAAW4kB,EAAW5kB,OAChC,OAAO,EAEX,IAAK,IAAIuE,EAAI,EAAGA,EAAIogB,EAAU3kB,OAAQuE,IAClC,GAAIogB,EAAUpgB,KAAOqgB,EAAWrgB,GAC5B,OAAO,EAGf,OAAO,CACV,CCqCM,MAAMsgB,EDnCb,SAAoBC,EAAUC,GAE1B,IAAIC,OADY,IAAZD,IAAsBA,EAAUL,GAEpC,IACIO,EADAC,EAAW,GAEXC,GAAa,EAejB,OAdA,WAEI,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKtR,UAAU/T,OAAQqlB,IACpCD,EAAQC,GAAMtR,UAAUsR,GAE5B,OAAIF,GAAcH,IAAa9d,MAAQ6d,EAAQK,EAASF,KAGxDD,EAAaH,EAASjV,MAAM3I,KAAMke,GAClCD,GAAa,EACbH,EAAW9d,KACXge,EAAWE,GALAH,CAOd,CAEJ,CCkBmCK,EAjCpC,SACEC,EACAnkB,EACAokB,EAAwB,GAExB,MAAMC,EAAWrkB,EAAKskB,sBAAsB9jB,IAAI2jB,GAChD,GAAgB,MAAZE,GAAwC,IAApBA,EAASzlB,OAC/B,MAAM2C,MAAO,2CAA0C4iB,MAGzD,MAAMI,EAAgBF,EAASA,EAASzlB,OAAS,GAC3C4lB,EAAWD,EAAYxf,UAAYwf,EAAYE,SAErD,GAAID,EAAWJ,EACb,MAAO,CAAC,EAAG,GAGb,IAAIjkB,EAAYikB,EAChB,IAAK,IAAIpf,EAAQ,EAAGA,EAAQqf,EAASzlB,OAAQoG,IAAS,CACpD,MAAM0f,EAAUL,EAASrf,GACzB,GAAI0f,EAAQ3f,WAAaqf,EAAc,CACrCjkB,EAAYukB,EAAQ3f,UACpB,KACD,CACF,CAED,MAAO,CAAC5E,EAAWqkB,EACpB,I,sBCuBD,MAGMG,EAEF,oHAFEA,EAQF,uKAKG,SAASC,EACdC,GAEA,MAAMC,EAAc3B,SAAS0B,EAAmB,IAIhD,GAAIC,EAAc,EAChB,MAAO,GAGT,MAAMC,EAAQ,GACd,IAAIC,EAAc,EAClB,KAAOA,GAAe9B,GACf,GAAK8B,EAAeF,GACvBC,EAAMzjB,KAAK0jB,GAEbA,IAEF,OAAOD,CACR,CAmBD,IAAIE,EAAkB,KAkBtB,SAASC,EACPvG,EACAxe,EACA4kB,EACAI,EACAzH,GAEA,MAAM,SAACyG,EAAD,aAAWiB,GAAgB1H,EAC3B2H,EAhBR,SAAkBnf,GAChB,GAAIA,EAAMtH,OAAS,EAAG,CACpB,MAAM,MAACymB,EAAD,KAAQ1G,GAAQzY,EAAMA,EAAMtH,OAAS,GAC3C,MAAgB,gBAAT+f,EAAyB0G,EAAQA,EAAQ,CACjD,CACD,OAAO,CACR,CAUeC,CAASF,GAEjBV,EAAwB,CAC5B/F,OACAwF,WACAkB,QACAN,QACAhgB,UAAW5E,EACXskB,SAAU,GAGZ/G,EAAM0H,aAAa9jB,KAAK,CAAC+jB,QAAOX,UAASvkB,YAAWwe,SAGpD,MAAM0F,EAAWc,EAAoBb,sBAAsB9jB,IAAI2jB,GAC/C,MAAZE,EACFA,EAAS/iB,KAAKojB,GAEdS,EAAoBb,sBAAsBxkB,IAAI4d,EAAMyG,SAAU,CAACO,IAIjEK,EAAMngB,SAAQ2gB,IACVJ,EAAoBK,sBAAsBhlB,IAC1C+kB,GACuBjkB,KAAKojB,EAF9B,GAIH,CAED,SAASe,EACP9G,EACA6F,EACAW,EACAjf,GAEA,GAAqB,IAAjBA,EAAMtH,OAOR,YANA+B,QAAQ0X,MACN,+DACAsG,EACA6F,GAMJ,MAAMvR,EAAO/M,EAAMA,EAAMtH,OAAS,GAC9BqU,EAAK0L,OAASA,GAChBhe,QAAQ0X,MACN,gEACAsG,EACA6F,EACAvR,EAAK0L,MAKT,MAAM,QAAC+F,EAAD,UAAUvkB,GAAa+F,EAAMtC,MAC9B8gB,GACH/jB,QAAQ0X,MAAM,iDAAkDsG,GAIlE+F,EAAQD,SAAWD,EAAWrkB,CAC/B,CAilBD,SAASulB,EAAoChI,GACA,OAAvCA,EAAMiI,8BACRhlB,QAAQ0X,MACN,+DACAqF,EAAMiI,6BAGX,CAED,SAASC,EACPlI,EACAiB,GAE2C,OAAvCjB,EAAMiI,6BACRhlB,QAAQ0X,MACL,2BAA0BsG,+CAEpBjB,EAAMiI,6BAA6BhH,OAASA,GACrDhe,QAAQ0X,MACL,2BAA0BsG,yBAA4BjB,EAAMiI,6BAA6BhH,mBAG/F,CA2KD,SAASkH,EAAgBC,GACvB,MAAMzN,EAAQ,IAAI9W,MAClB8W,EAAMnS,MAAQ4f,EAEd,MAAMlW,EAASmW,IAAAA,MAAuB1N,GAEtC,OAAyB,IAAlBzI,EAAOhR,OAAegR,EAAO,GAAK,IAC1C,CAEcwR,eAAe4E,EAC5BC,GAEA,MAAMC,EAzDR,SAA8BC,GAC5B,IAAIC,EACJ,IACEA,GAAaloB,EAAAA,EAAAA,0BAAyBioB,EAAS,iBAChD,CAAC,MAAO9N,GAEP,MAAMgO,EAAiB,IAAIhD,EAAoBhL,EAAMmB,SAErD,MADA6M,EAAengB,MAAQmS,EAAMnS,MACvBmgB,CACP,CAED,MAAMjlB,EAAUglB,EAAWtlB,SAAS,GA+BpC,OA7B6B,IAAIwlB,EAAAA,WAAqB,CAEpDlgB,eAAgBhF,EAAQgF,eAAemgB,KAAKnlB,GAE5CiF,YAAajF,EAAQiF,YAAYkgB,KAAKnlB,GAEtC4F,YAAa5F,EAAQ4F,YAAYuf,KAAKnlB,GACtC0F,uBAAwB,IAAM,IAGoBD,YAAYlC,KAAI6hB,GAClEA,EAAM7hB,KACJ,EACE2B,QACAC,MACA9D,MACEkB,OAAQ3E,OAAM6C,OAAME,OAAME,YAJ9B,CAOEjD,OACA+F,UAAWuB,EAAQ,IACnBme,UAAWle,EAAMD,GAAS,IAC1BmgB,UAAW5kB,EACX6kB,aAAc3kB,EACd4kB,eAAgB1kB,OAMvB,CAcoB2kB,CAAqBX,GAElCT,EAA6D,IAAIpmB,IACvE,IAAK,IAAImmB,EAAkB,EAAGA,EAAOrC,EAAuBqC,IAC1DC,EAAsB1lB,IAAIylB,EAAM,IAGlC,MAAMsB,EAA6B,CACjCvC,sBAAuB,IAAIllB,IAC3B0nB,kBAAmB,GACnBrC,SAAU,EACVyB,aACAa,6BAA8B,IAAI3nB,IAClC4nB,eAAgB,IAAI5nB,IACpBomB,wBACAyB,aAAc,GACdC,gBAAiB,GACjBC,qBAAsB,GACtBC,aAAc,KACdC,iBAAkB,GAClBC,UAAW,GACXC,eAAgB,EAChBpnB,UAAW,EACXqnB,eAAgB,GAChBC,aAAc,IAeVC,GAVNzB,EAAWA,EAAShN,OAAO8C,SAAShL,MAAK,CAACtH,EAAGC,IAAOD,EAAEjK,GAAKkK,EAAElK,GAAK,GAAK,KAUlCmoB,WACnCloB,GAAwB,YAAfA,EAAMT,OAEjB,IAA6B,IAAzB0oB,EACF,OAAOb,EAMTA,EAAa1mB,UAAY8lB,EAASyB,GAAqB3nB,KAAKC,KAAKG,UACjE0mB,EAAapC,UACVwB,EAASA,EAASrnB,OAAS,GAAGY,GAAKqnB,EAAa1mB,WAAa,IAEhE,MAAMud,EAAwB,CAC5BkK,wBAAyB,GACzBzD,SAAU,EACVwB,6BAA8B,KAC9BkC,gCAAiC,KACjCC,6BAA8B,IAAIpW,IAClC0T,aAAc,GACd2C,iBAAkB,GAClBC,oCAAoC,EACpCC,oBAAqB,GACrBC,0BAA2B,KAC3BC,2BAA4B,GAC5BC,2CAA4C,GAC5CC,6BAA8B,IAAIjpB,IAClCkpB,WAAY,EACZC,yBAA0B,IAAInpB,KAKhC,GAFA6mB,EAASrhB,SAAQnF,GA7oBnB,SACEA,EAEA0lB,EAEAzH,GAEA,MAAM,IAAC8K,EAAD,KAAMxpB,EAAN,GAAYQ,EAAZ,GAAgBipB,GAAMhpB,EAEtBU,GAAaX,EAAK2lB,EAAoBhlB,WAAa,IAEzD,OAAQqoB,GACN,IAAK,2CAhGT,SACE/oB,EACAsF,EACA8hB,EACAnJ,GAEA,MAEMgL,EAAW,CACfhd,OAAQ,EACRid,MAAO,KACPC,YAAc,yBALQnpB,EAAMM,KAAK2oB,WAMjC3jB,YACA+C,MAAO,GAIT,IAAI+gB,EAAc,KAClBnL,EAAMkK,wBAAwBtmB,KAC5B,IAAI0M,SAAQC,IACV4a,EAAY5a,CAAZ,KAMJ6a,MAAMJ,EAASE,aACZpa,MAAKua,GAAYA,EAASC,SAC1Bxa,MAAKwa,IAEJC,kBAAkBD,GAAMxa,MAAK0a,IAC3BR,EAAShd,OAASwd,EAAOxd,OACzBgd,EAAS5gB,MAAQohB,EAAOphB,MAExB+gB,GAAW,GAJb,IAQJhC,EAAaS,UAAUhmB,KAAKonB,EAC7B,CA0DKS,CAAkB1pB,EAAOU,EAAWglB,EAAqBzH,GACzD,MACF,IAAK,oBACH,OAAQ1e,GACN,IAAK,iBAxNb,SACES,EACAsF,EACA8hB,EACAnJ,GAEA,MAAM1d,EAAOP,EAAMM,KAAKC,KAClB2e,EAAO3e,EAAK2e,KAElB,GAAIA,EAAKvZ,WAAW,UAAW,CAC7B,MAAMgkB,EAAappB,EAAKopB,WACxB,GAAIA,GACeA,EAAWA,EAAWxqB,OAAS,GACnCkD,IAAIunB,SAAS,eAExB,MAGL,CAGD,GACW,SAAT1K,GACS,UAATA,GACS,UAATA,GACAA,EAAKvZ,WAAW,UAChBuZ,EAAKvZ,WAAW,QAChBuZ,EAAKvZ,WAAW,UAChBuZ,EAAKvZ,WAAW,WAChB,CACA,MAAMqf,EAAWhlB,EAAM6pB,IAAM,IAE7B,IAAIjE,EAAQ,EAEZ,KAAO3H,EAAMqK,iBAAiBnpB,OAAS,GAAG,CACxC,MAAM2qB,EACJ7L,EAAMqK,iBAAiBrK,EAAMqK,iBAAiBnpB,OAAS,GAGzD,GAAImG,EAFiBwkB,EAAgBxkB,UAAYwkB,EAAgB9E,SAEnC,CAC5BY,EAAQkE,EAAgBlE,MAAQ,EAChC,KACD,CACC3H,EAAMqK,iBAAiBnkB,KAE1B,CAED,MAAM4lB,EAAc,CAClBnE,QACAZ,WACA1f,YACA4Z,OACA8K,QAAS,MAGX5C,EAAaI,aAAa3lB,KAAKkoB,GAI/B9L,EAAMqK,iBAAiBzmB,KAAKkoB,EAC7B,CACF,CA4JSE,CAAqBjqB,EAAOU,EAAWglB,EAAqBzH,GAC5D,MACF,IAAK,kBA5Jb,SACEje,EACAsF,EACA8hB,EACAnJ,GAEA,MAAMiM,EAAYlqB,EAAMM,KAAKC,KAAK2pB,UAC5BC,EAAiBlM,EAAM2K,6BAA6B7nB,IAAImpB,GACxC,MAAlBC,IACFA,EAAeC,gBAAkB9kB,EACiB,IAA9C6kB,EAAeE,6BACjBF,EAAeE,2BAA6B/kB,GAEG,IAA7C6kB,EAAeG,4BACjBH,EAAeG,0BAA4BhlB,GAI7C2Y,EAAM2K,6BAA6BxW,OAAOpS,EAAMM,KAAKC,KAAK2pB,WAE7D,CAyISK,CAAsBvqB,EAAOU,EAAWglB,EAAqBzH,GAC7D,MACF,IAAK,wBAzIb,SACEje,EACAsF,EACA8hB,EACAnJ,GAEA,MAAMiM,EAAYlqB,EAAMM,KAAKC,KAAK2pB,UAC5BC,EAAiBlM,EAAM2K,6BAA6B7nB,IAAImpB,GACxC,MAAlBC,IACgD,IAA9CA,EAAeE,6BACjBF,EAAeE,2BAA6B/kB,GAE9C6kB,EAAeG,0BAA4BhlB,EAC3C6kB,EAAeC,gBAAkB9kB,EAEpC,CA2HSklB,CACExqB,EACAU,EACAglB,EACAzH,GAEF,MACF,IAAK,2BAhIb,SACEje,EACAsF,EACA8hB,EACAnJ,GAEA,MAAMiM,EAAYlqB,EAAMM,KAAKC,KAAK2pB,UAC5BC,EAAiBlM,EAAM2K,6BAA6B7nB,IAAImpB,GACxC,MAAlBC,IACFA,EAAeM,yBAA2BnlB,EAE7C,CAsHSolB,CACE1qB,EACAU,EACAglB,EACAzH,GAEF,MACF,IAAK,uBAlFb,SACEje,EACAsF,EACA8hB,EACAnJ,GAEA,MAAM1d,EAAOP,EAAMM,KAAKC,KAClB2pB,EAAY3pB,EAAK2pB,UAEjBS,EAAkB,IAAI1rB,MAC1Bgf,EAAM2K,6BAA6BxnB,KAAO,GAC1CwpB,MAAK,GACP3M,EAAM2K,6BAA6BzjB,SAAQ,EAAEygB,YAC3C+E,EAAgB/E,IAAS,CAAzB,IAGF,IAAIA,EAAQ,EACZ,IAAK,IAAIliB,EAAI,EAAGA,EAAIinB,EAAgBxrB,OAAQuE,IAC1C,GAAIinB,EAAgBjnB,GAAI,CACtBkiB,EAAQliB,EACR,KACD,CAGH,MAAMymB,EAAiC,CACrCvE,QACAwE,gBAAiB,EACjBC,2BAA4B,EAC5BC,0BAA2B,EAC3BJ,YACAW,cAAetqB,EAAKsqB,cACpBC,SAAUvqB,EAAKuqB,SACfC,qBAAsBzlB,EACtBmlB,yBAA0B,EAC1BpoB,IAAK9B,EAAK8B,KAGZ4b,EAAM2K,6BAA6BvoB,IAAI6pB,EAAWC,GAElD/C,EAAaK,gBAAgB5lB,KAAKsoB,GAClCA,EAAeY,qBAAuBzlB,CACvC,CA0CS0lB,CACEhrB,EACAU,EACAglB,EACAzH,GAIN,MACF,IAAK,oBACH,GAAI1e,EAAKoG,WAAW,oBAAqB,CACvC,MAAOgiB,GAAgBpoB,EAAKyG,MAAM,IAAIuT,MAAM,KAC5CmM,EAAoBiC,aAAeA,CACpC,MAAM,GAAIpoB,EAAKoG,WAAW,uBAAwB,CACjD,MAAOslB,GAAiB1rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C,GADAiM,EAAkB9B,SAASuH,EAAe,IACtCzF,IAAoB7B,EACtB,MAAM,IAAIC,EACP,mCAAkCqH,+CAGxC,MAAM,GAAI1rB,EAAKoG,WAAW,wBAAyB,CAClD,MAAOulB,GAAyB3rB,EAAKyG,MAAM,IAAIuT,MAAM,MAhY7D,SACE6N,EACA8D,GAIA,GAAyC,IAArC9D,EAAaG,eAAenmB,KAAY,CAC1C,MAAM+pB,EAAkBD,EAAsB3R,MAAM,KACpD,IAAK,IAAI6R,EAAY,EAAGA,EAAYD,EAAgBhsB,OAAQisB,IAI1DhE,EAAaG,eAAelnB,IAAI+qB,EAAWD,EAAgBC,GAE9D,CACF,CAkXOC,CAAqB3F,EAAqBwF,EAC3C,MAAM,GAAI3rB,EAAKoG,WAAW,iBAiVjC,SACEpG,EACAmB,EACAglB,EACAzH,GAEA,GAAI1e,EAAKoG,WAAW,6BAA8B,CAChD,MAAO2lB,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C0M,EAAoChI,GAEpCA,EAAMiI,6BAA+B,CACnCoF,gBACAhmB,UAAW5E,EACXskB,SAAU,EACV9F,KAAM,SACN8K,QAAS,KAEZ,MAAM,GAAa,4BAATzqB,GAGT,GAFA4mB,EAAkClI,EAAO,UAEE,OAAvCA,EAAMiI,6BAAuC,CAC/C,MAAMqF,EAAmBtN,EAAMiI,6BAC/BqF,EAAiBvG,SAAWtkB,EAAY6qB,EAAiBjmB,UAEzD2Y,EAAMiI,6BAA+B,KAErCR,EAAoB2B,kBAAkBxlB,KAAK0pB,EAC5C,OACI,GAAIhsB,EAAKoG,WAAW,0CAA2C,CACpE,MAAO2lB,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C0M,EAAoChI,GAEpCA,EAAMiI,6BAA+B,CACnCoF,gBACAhmB,UAAW5E,EACXskB,SAAU,EACV9F,KAAM,sBACN8K,QAAS,KAEZ,MAAM,GAAa,yCAATzqB,GAGT,GAFA4mB,EAAkClI,EAAO,uBAEE,OAAvCA,EAAMiI,6BAAuC,CAC/C,MAAMqF,EAAmBtN,EAAMiI,6BAC/BqF,EAAiBvG,SAAWtkB,EAAY6qB,EAAiBjmB,UAEzD2Y,EAAMiI,6BAA+B,KAErCR,EAAoB2B,kBAAkBxlB,KAAK0pB,EAC5C,OACI,GAAIhsB,EAAKoG,WAAW,4CAA6C,CACtE,MAAO2lB,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C0M,EAAoChI,GAEpCA,EAAMiI,6BAA+B,CACnCoF,gBACAhmB,UAAW5E,EACXskB,SAAU,EACV9F,KAAM,wBACN8K,QAAS,KAEZ,MAAM,GAAa,2CAATzqB,GAGT,GAFA4mB,EAAkClI,EAAO,yBAEE,OAAvCA,EAAMiI,6BAAuC,CAC/C,MAAMqF,EAAmBtN,EAAMiI,6BAC/BqF,EAAiBvG,SAAWtkB,EAAY6qB,EAAiBjmB,UAEzD2Y,EAAMiI,6BAA+B,KAErCR,EAAoB2B,kBAAkBxlB,KAAK0pB,EAC5C,OACI,GAAIhsB,EAAKoG,WAAW,2CAA4C,CACrE,MAAO2lB,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C0M,EAAoChI,GAEpCA,EAAMiI,6BAA+B,CACnCoF,gBACAhmB,UAAW5E,EACXskB,SAAU,EACV9F,KAAM,uBACN8K,QAAS,KAEZ,MAAM,GAAa,0CAATzqB,GAGT,GAFA4mB,EAAkClI,EAAO,wBAEE,OAAvCA,EAAMiI,6BAAuC,CAC/C,MAAMqF,EAAmBtN,EAAMiI,6BAC/BqF,EAAiBvG,SAAWtkB,EAAY6qB,EAAiBjmB,UAEzD2Y,EAAMiI,6BAA+B,KAErCR,EAAoB2B,kBAAkBxlB,KAAK0pB,EAC5C,OACI,GAAIhsB,EAAKoG,WAAW,6CAA8C,CACvE,MAAO2lB,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE7C0M,EAAoChI,GAEpCA,EAAMiI,6BAA+B,CACnCoF,gBACAhmB,UAAW5E,EACXskB,SAAU,EACV9F,KAAM,yBACN8K,QAAS,KAEZ,MAAM,GAAa,4CAATzqB,IACT4mB,EAAkClI,EAAO,0BAEE,OAAvCA,EAAMiI,8BAAuC,CAC/C,MAAMqF,EAAmBtN,EAAMiI,6BAC/BqF,EAAiBvG,SAAWtkB,EAAY6qB,EAAiBjmB,UAEzD2Y,EAAMiI,6BAA+B,KAErCR,EAAoB2B,kBAAkBxlB,KAAK0pB,EAC5C,CAEJ,CA1cOC,CACEjsB,EACAmB,EACAglB,EACAzH,QAEG,GAAI1e,EAAKoG,WAAW,sBAAuB,CAChD,MAAOyf,GAAqB7lB,EAAKyG,MAAM,IAAIuT,MAAM,KAEjDmM,EAAoBkC,iBAAiB/lB,KAAK,CACxCqd,KAAM,kBACNoG,MAAOH,EAAoCC,GAC3C9f,UAAW5E,EACXspB,QAAS,MAEZ,MAAM,GAAIzqB,EAAKoG,WAAW,6BAA8B,CACvD,MAAOyf,EAAmBkG,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE1DkS,EAAmB,CACvBvM,KAAM,wBACNoG,MAAOH,EAAoCC,GAC3CkG,gBACAhmB,UAAW5E,EACXspB,QAAS,MAKP/L,EAAM0H,aAAatmB,MAAK,EAAE6f,UAAmB,WAATA,MACtCjB,EAAMwK,0BAA4BgD,GAGpC/F,EAAoBkC,iBAAiB/lB,KAAK4pB,EAC3C,MAAM,GAAIlsB,EAAKoG,WAAW,4BAA6B,CACtD,MAAOyf,EAAmBkG,GAAiB/rB,EAAKyG,MAAM,IAAIuT,MAAM,KAE1DmS,EAAmB,CACvBxM,KAAM,wBACNoG,MAAOH,EAAoCC,GAC3CkG,gBACAhmB,UAAW5E,EACXspB,QAAS,MAKP/L,EAAM0H,aAAatmB,MAAK,EAAE6f,UAAmB,WAATA,MACtCjB,EAAMwK,0BAA4BiD,GAGpChG,EAAoBkC,iBAAiB/lB,KAAK6pB,EAC3C,MAAM,GAAInsB,EAAKoG,WAAW,YAAa,CACtC,MAAO2lB,EAAeK,EAAO5R,GAAWxa,EAAKyG,MAAM,GAAGuT,MAAM,KAE5DmM,EAAoBsC,aAAanmB,KAAK,CACpCypB,gBACAvR,UACA4R,MAASA,EACTrmB,UAAW5E,EACXwe,KAAM,gBAET,MAAM,GAAI3f,EAAKoG,WAAW,uBAAwB,CACjD,MAAOvF,EAAIkrB,EAAeK,EAAOvG,EAAmBwG,GAAersB,EAChEyG,MAAM,IACNuT,MAAM,KACH+L,EAAQH,EAAoCC,GAE5CuF,EAAkB,IAAI1rB,MAC1Bgf,EAAM6K,yBAAyB1nB,KAAO,GACtCwpB,MAAK,GACP3M,EAAM6K,yBAAyB3jB,SAAQ,EAAEygB,YACvC+E,EAAgB/E,IAAS,CAAzB,IAGF,IAAIA,EAAQ,EACZ,IAAK,IAAIliB,EAAI,EAAGA,EAAIinB,EAAgBxrB,OAAQuE,IAC1C,GAAIinB,EAAgBjnB,GAAI,CACtBkiB,EAAQliB,EACR,KACD,CAQH,MAAMmoB,EAAgB,CACpBP,gBACA1F,QACAZ,SAAU,KACV5kB,KACAurB,MAASA,EACTC,YAAaA,GAAe,KAC5BE,WAAY,aACZxmB,UAAW5E,EACXwe,KAAM,WACN8K,QAAS,MAGG,WAAV2B,GAKF1N,EAAM0K,2CAA2C9mB,KAAK,CACpDgqB,EACAvG,IAIJI,EAAoBqC,eAAelmB,KAAKgqB,GACxC5N,EAAM6K,yBAAyBzoB,IAAID,EAAIyrB,EACxC,MAAM,GAAItsB,EAAKoG,WAAW,wBAAyB,CAClD,MAAOvF,GAAMb,EAAKyG,MAAM,IAAIuT,MAAM,KAC5BsS,EAAgB5N,EAAM6K,yBAAyB/nB,IAAIX,GACpC,MAAjByrB,IACF5N,EAAM6K,yBAAyB1W,OAAOhS,GAEtCyrB,EAAc7G,SAAWtkB,EAAYmrB,EAAcvmB,UACnDumB,EAAcC,WAAa,WAE9B,MAAM,GAAIvsB,EAAKoG,WAAW,wBAAyB,CAClD,MAAOvF,GAAMb,EAAKyG,MAAM,IAAIuT,MAAM,KAC5BsS,EAAgB5N,EAAM6K,yBAAyB/nB,IAAIX,GACpC,MAAjByrB,IACF5N,EAAM6K,yBAAyB1W,OAAOhS,GAEtCyrB,EAAc7G,SAAWtkB,EAAYmrB,EAAcvmB,UACnDumB,EAAcC,WAAa,WAE9B,MAAM,GAAIvsB,EAAKoG,WAAW,mBAAoB,CACzCsY,EAAMsK,qCACRtK,EAAMsK,oCAAqC,EAC3CtK,EAAMyG,SAAazG,EAAM4K,cAKa,OAApC5K,EAAMwK,4BACRxK,EAAMyK,2BAA2B7mB,KAAK,CACpCoc,EAAMwK,0BACNxK,EAAMyG,WAERzG,EAAMwK,0BAA4B,MAGpC,MAAOrD,GAAqB7lB,EAAKyG,MAAM,IAAIuT,MAAM,MA1azD,SACE2F,EACAzY,GAEA,MAAMslB,EAAYtlB,EAAMtH,OAAS,EACjC,GAAI4sB,GAAa,EAAG,CAClB,MAAMvY,EAAO/M,EAAMslB,GACnB,QAAsBlmB,IAAlB2N,EAAKuR,UAA0BvR,EAAK0L,OAASA,EAC/C,MAAM,IAAI0E,EACP,oBAAmB1E,sBAAyB1L,EAAK0L,mBAGvD,CACF,CA+ZO8M,CAAkB,SAAU/N,EAAM0H,cACM,gBAtgBhD,SAAqBlf,GACnB,GAAIA,EAAMtH,OAAS,EAAG,CACpB,MAAM,KAAC+f,GAAQzY,EAAMA,EAAMtH,OAAS,GACpC,OAAO+f,CACR,CACD,OAAO,IACR,CAggBW+M,CAAYhO,EAAM0H,eACpBF,EACE,cACA/kB,EACAykB,EAAoCC,GACpCM,EACAzH,GAGJwH,EACE,SACA/kB,EACAykB,EAAoCC,GACpCM,EACAzH,GAGF,IAAK,IAAIva,EAAI,EAAGA,EAAIua,EAAMqK,iBAAiBnpB,OAAQuE,IAAK,CACtD,MAAMqmB,EAAc9L,EAAMqK,iBAAiB5kB,GAC1BqmB,EAAYzkB,UAAYykB,EAAY/E,SAOtCtkB,GACbud,EAAMuK,oBAAoB3mB,KAAK,CAACkoB,EAAa9L,EAAMyG,UAEtD,CACF,MAAM,GACLnlB,EAAKoG,WAAW,kBAChBpG,EAAKoG,WAAW,kBAEhBqgB,EACE,SACAtlB,EACAglB,EACAzH,EAAM0H,mBAEH,GAAIpmB,EAAKoG,WAAW,mBAAoB,CAC7CsY,EAAMsK,oCAAqC,EAC3C,MAAOnD,GAAqB7lB,EAAKyG,MAAM,IAAIuT,MAAM,KAEjDkM,EACE,SACA/kB,EACAykB,EAAoCC,GACpCM,EACAzH,EAEH,MAAM,GAAI1e,EAAKoG,WAAW,iBACzBqgB,EACE,SACAtlB,EACAglB,EACAzH,EAAM0H,cAERK,EACE,cACAtlB,EACAglB,EACAzH,EAAM0H,mBAEH,GAAIpmB,EAAKoG,WAAW,2BAA4B,CACrD,MAAOyf,GAAqB7lB,EAAKyG,MAAM,IAAIuT,MAAM,KAEjDkM,EACE,iBACA/kB,EACAykB,EAAoCC,GACpCM,EACAzH,EAEH,MAAM,GAAI1e,EAAKoG,WAAW,yBACzBqgB,EACE,iBACAtlB,EACAglB,EACAzH,EAAM0H,mBAEH,GAAIpmB,EAAKoG,WAAW,4BAA6B,CACtD,MAAOyf,GAAqB7lB,EAAKyG,MAAM,IAAIuT,MAAM,KAEjDkM,EACE,kBACA/kB,EACAykB,EAAoCC,GACpCM,EACAzH,EAEH,MAAM,GAAI1e,EAAKoG,WAAW,0BACzBqgB,EACE,kBACAtlB,EACAglB,EACAzH,EAAM0H,mBAEH,GAAIpmB,EAAKoG,WAAW,kCAAmC,CAC5D,MAAMumB,EAAkB3sB,EAAKyG,MAAM,IAEnC,IAAKiY,EAAMoK,6BAA6BnW,IAAIga,GAAkB,CAC5DjO,EAAMoK,6BAA6BlW,IAAI+Z,GAEvC,MAAMC,EAAwB/F,EAAgB8F,GAE9CjO,EAAMmK,gCAAkC+D,CACzC,CACF,MAAM,GAAI5sB,EAAKoG,WAAW,iCAAkC,CAC3D,MAAMymB,EAAiB7sB,EAAKyG,MAAM,IAElC,IAAKiY,EAAMoK,6BAA6BnW,IAAIka,GAAiB,CAC3DnO,EAAMoK,6BAA6BlW,IAAIia,GAEvC,MAAMC,EAAuBjG,EAAgBgG,GAE7C,GAC2B,OAAzBC,GAC0C,OAA1CpO,EAAMmK,gCACN,CACA,MAAM+D,EAAwBlO,EAAMmK,gCAEpCnK,EAAMmK,gCAAkC,KAExC,MAAMkE,EAAQ,CAACH,EAAuBE,GAChCE,EAAS7G,EAAoB4B,6BAA6BvmB,IAC9DorB,EAAsB1sB,UAEV,MAAV8sB,EACF7G,EAAoB4B,6BAA6BjnB,IAC/C8rB,EAAsB1sB,SACtB,CAAC6sB,IAGHC,EAAO1qB,KAAKyqB,EAEf,CACF,CACF,MAAM,GAAW,MAAPtD,GAAqB,MAAPA,EAEvBtD,EAAoBgC,qBAAqB7lB,KAAK,CAC5CtC,OACA+F,UAAW5E,SAER,GAAW,MAAPsoB,QAEJ,GAAW,MAAPA,QAEJ,GAAW,MAAPA,GAAqB,MAAPA,EAIvB,MAAM,IAAIpF,EACP,sBAAqB4I,KAAKC,UACzBzsB,mDAMX,CAsR2B0sB,CAAqB1sB,EAAOonB,EAAcnJ,KAE5C,OAApBuH,EAA0B,CAC5B,GAC2C,IAAzC4B,EAAaQ,iBAAiBzoB,QACc,IAA5CioB,EAAavC,sBAAsBzjB,KAMnC,MAAM,IAAIwiB,EACR,wJAKJ,MAAM,IAAIA,EACP,2EAEJ,CAGD,MAAM,aAAC+B,GAAgB1H,EAwDvB,GAvDI0H,EAAaxmB,OAAS,GACxB+B,QAAQ0X,MAAM,gCAAiC+M,GAIjD1H,EAAMuK,oBAAoBrjB,SAAQ,EAAE4kB,EAAarF,MAG/C,MAAOhkB,EAAWqkB,GAAYf,EAC5BU,EACA0C,EACA2C,EAAYzkB,WAEVyf,EAAWrkB,EA1hCqB,KA2hClCqpB,EAAYC,QAAU9E,EACvB,IAEHjH,EAAMyK,2BAA2BvjB,SAAQ,EAAEwnB,EAAiBjI,MAE1D,MAAOhkB,EAAWqkB,GAAYf,EAAcU,EAAU0C,GAClDrC,EAAWrkB,EAhiCsB,IAqiChCisB,EAAgBrH,MAAMsH,MACrB9G,GAAkD,eAA1CsB,EAAaG,eAAexmB,IAAI+kB,IAQ7C,IAEH7H,EAAM0K,2CAA2CxjB,SAC/C,EAAE0mB,EAAevG,MAGZA,EAAMsH,MACL9G,GAAkD,eAA1CsB,EAAaG,eAAexmB,IAAI+kB,OAG1C+F,EAAc7B,QAAU9E,EACzB,UAMC3W,QAAQse,IAAI5O,EAAMkK,yBAIpBf,EAAaS,UAAU1oB,OAAS,EAAG,CAGrC,MAAM8pB,EAAW7B,EAAaS,UAAU,GAExCT,EAAaU,eAAiB7gB,KAAKC,IACjC+hB,EAAShd,OJ3nCoB,GI8nChC,CAED,OAAOmb,CACR,C,qBC3oCM,MAAM0F,ECSNnL,eAA0Bvf,GAC/B,IACE,MAAM2qB,OCRoB3qB,KAC5B,IAAKA,EAAK7C,KAAKytB,SAAS,SACtB,MAAM,IAAIpJ,EACR,mEAIJ,MAAMqJ,EAAa,IAAIC,WAEvB,OAAO,IAAI3e,SAAQ,CAACC,EAASC,KAC3Bwe,EAAWE,OAAS,KAClB,MAAMvnB,EAAS6U,IAAWwS,EAAWrnB,QACf,iBAAXA,GACT4I,EAAQ5I,GAEV6I,EAAO,IAAImV,EAAoB,uCAA/B,EAGFqJ,EAAWG,QAAU,IAAM3e,EAAOwe,EAAWrU,OAE7CqU,EAAWI,WAAWjrB,EAAtB,GAXF,EDDyBkrB,CAAclrB,GAC/B5C,EAA0BgtB,KAAK7T,MAAMoU,GAC3C,GAAsB,IAAlBvtB,EAAOL,OACT,MAAM,IAAIykB,EAAoB,oCAKhC,MAAO,CACL2J,OAAQ,UACRC,oBAJ0BjH,EAAe/mB,GAM5C,CAAC,MAAOoZ,GACP,OAAIA,aAAiBgL,EACZ,CACL2J,OAAQ,wBACR3U,SAGK,CACL2U,OAAQ,mBACR3U,QAGL,CACF,E", "sources": ["webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/import/chrome.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/import/v8cpuFormatter.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/demangle-cpp.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/flamechart.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/math.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/profile.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/utils.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/lib/value-formatters.js", "webpack://react-devtools-core/../../node_modules/@elg/speedscope/dist/library/library.js", "webpack://react-devtools-core/../../node_modules/error-stack-parser/error-stack-parser.js", "webpack://react-devtools-core/../../node_modules/nullthrows/nullthrows.js", "webpack://react-devtools-core/../../node_modules/stackframe/stackframe.js", "webpack://react-devtools-core/../react-devtools-timeline/node_modules/regenerator-runtime/runtime.js", "webpack://react-devtools-core/webpack/bootstrap", "webpack://react-devtools-core/webpack/runtime/compat get default export", "webpack://react-devtools-core/webpack/runtime/define property getters", "webpack://react-devtools-core/webpack/runtime/hasOwnProperty shorthand", "webpack://react-devtools-core/../react-devtools-shared/src/devtools/constants.js", "webpack://react-devtools-core/../react-devtools-timeline/src/constants.js", "webpack://react-devtools-core/../react-devtools-timeline/src/import-worker/InvalidProfileError.js", "webpack://react-devtools-core/../../node_modules/memoize-one/dist/memoize-one.esm.js", "webpack://react-devtools-core/../react-devtools-timeline/src/utils/getBatchRange.js", "webpack://react-devtools-core/../react-devtools-timeline/src/import-worker/preprocessData.js", "webpack://react-devtools-core/../react-devtools-timeline/src/import-worker/importFile.worker.js", "webpack://react-devtools-core/../react-devtools-timeline/src/import-worker/importFile.js", "webpack://react-devtools-core/../react-devtools-timeline/src/import-worker/readInputData.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.importFromOldV8CPUProfile = exports.importFromChromeCPUProfile = exports.importFromChromeTimeline = exports.isChromeTimeline = void 0;\nconst profile_1 = require(\"../lib/profile\");\nconst utils_1 = require(\"../lib/utils\");\nconst value_formatters_1 = require(\"../lib/value-formatters\");\nconst v8cpuFormatter_1 = require(\"./v8cpuFormatter\");\nfunction isChromeTimeline(rawProfile) {\n    if (!Array.isArray(rawProfile))\n        return false;\n    if (rawProfile.length < 1)\n        return false;\n    const first = rawProfile[0];\n    if (!('pid' in first && 'tid' in first && 'ph' in first && 'cat' in first))\n        return false;\n    if (!rawProfile.find(e => e.name === 'CpuProfile' || e.name === 'Profile' || e.name === 'ProfileChunk'))\n        return false;\n    return true;\n}\nexports.isChromeTimeline = isChromeTimeline;\nfunction importFromChromeTimeline(events, fileName) {\n    // It seems like sometimes Chrome timeline files contain multiple CpuProfiles?\n    // For now, choose the first one in the list.\n    const cpuProfileByID = new Map();\n    // Maps profile IDs (like \"0x3\") to pid/tid pairs formatted as `${pid}:${tid}`\n    const pidTidById = new Map();\n    // Maps pid/tid pairs to thread names\n    const threadNameByPidTid = new Map();\n    // The events aren't necessarily recorded in chronological order. Sort them so\n    // that they are.\n    utils_1.sortBy(events, e => e.ts);\n    for (let event of events) {\n        if (event.name === 'CpuProfile') {\n            const pidTid = `${event.pid}:${event.tid}`;\n            const id = event.id || pidTid;\n            cpuProfileByID.set(id, event.args.data.cpuProfile);\n            pidTidById.set(id, pidTid);\n        }\n        if (event.name === 'Profile') {\n            const pidTid = `${event.pid}:${event.tid}`;\n            cpuProfileByID.set(event.id || pidTid, Object.assign({ startTime: 0, endTime: 0, nodes: [], samples: [], timeDeltas: [] }, event.args.data));\n            if (event.id) {\n                pidTidById.set(event.id, `${event.pid}:${event.tid}`);\n            }\n        }\n        if (event.name === 'thread_name') {\n            threadNameByPidTid.set(`${event.pid}:${event.tid}`, event.args.name);\n        }\n        if (event.name === 'ProfileChunk') {\n            const pidTid = `${event.pid}:${event.tid}`;\n            const cpuProfile = cpuProfileByID.get(event.id || pidTid);\n            if (cpuProfile) {\n                const chunk = event.args.data;\n                if (chunk.cpuProfile) {\n                    if (chunk.cpuProfile.nodes) {\n                        cpuProfile.nodes = cpuProfile.nodes.concat(chunk.cpuProfile.nodes);\n                    }\n                    if (chunk.cpuProfile.samples) {\n                        cpuProfile.samples = cpuProfile.samples.concat(chunk.cpuProfile.samples);\n                    }\n                }\n                if (chunk.timeDeltas) {\n                    cpuProfile.timeDeltas = cpuProfile.timeDeltas.concat(chunk.timeDeltas);\n                }\n                if (chunk.startTime != null) {\n                    cpuProfile.startTime = chunk.startTime;\n                }\n                if (chunk.endTime != null) {\n                    cpuProfile.endTime = chunk.endTime;\n                }\n            }\n            else {\n                console.warn(`Ignoring ProfileChunk for undeclared Profile with id ${event.id || pidTid}`);\n            }\n        }\n    }\n    if (cpuProfileByID.size > 0) {\n        const profiles = [];\n        let indexToView = 0;\n        utils_1.itForEach(cpuProfileByID.keys(), profileId => {\n            let threadName = null;\n            let pidTid = pidTidById.get(profileId);\n            if (pidTid) {\n                threadName = threadNameByPidTid.get(pidTid) || null;\n                if (threadName) {\n                }\n            }\n            const profile = importFromChromeCPUProfile(cpuProfileByID.get(profileId));\n            if (threadName && cpuProfileByID.size > 1) {\n                profile.setName(`${fileName} - ${threadName}`);\n                if (threadName === 'CrRendererMain') {\n                    indexToView = profiles.length;\n                }\n            }\n            else {\n                profile.setName(`${fileName}`);\n            }\n            profiles.push(profile);\n        });\n        return { name: fileName, indexToView, profiles };\n    }\n    else {\n        throw new Error('Could not find CPU profile in Timeline');\n    }\n}\nexports.importFromChromeTimeline = importFromChromeTimeline;\nconst callFrameToFrameInfo = new Map();\nfunction frameInfoForCallFrame(callFrame) {\n    return utils_1.getOrInsert(callFrameToFrameInfo, callFrame, callFrame => {\n        const name = callFrame.functionName || '(anonymous)';\n        const file = callFrame.url;\n        const line = callFrame.lineNumber;\n        const col = callFrame.columnNumber;\n        return {\n            key: `${name}:${file}:${line}:${col}`,\n            name,\n            file,\n            line,\n            col,\n        };\n    });\n}\nfunction shouldIgnoreFunction(callFrame) {\n    const { functionName, url } = callFrame;\n    if (url === 'native dummy.js') {\n        // I'm not really sure what this is about, but this seems to be used\n        // as a way of avoiding edge cases in V8's implementation.\n        // See: https://github.com/v8/v8/blob/b8626ca4/tools/js2c.py#L419-L424\n        return true;\n    }\n    return functionName === '(root)' || functionName === '(idle)';\n}\nfunction shouldPlaceOnTopOfPreviousStack(functionName) {\n    return functionName === '(garbage collector)' || functionName === '(program)';\n}\nfunction importFromChromeCPUProfile(chromeProfile) {\n    const profile = new profile_1.CallTreeProfileBuilder(chromeProfile.endTime - chromeProfile.startTime);\n    const nodeById = new Map();\n    for (let node of chromeProfile.nodes) {\n        nodeById.set(node.id, node);\n    }\n    for (let node of chromeProfile.nodes) {\n        if (typeof node.parent === 'number') {\n            node.parent = nodeById.get(node.parent);\n        }\n        if (!node.children)\n            continue;\n        for (let childId of node.children) {\n            const child = nodeById.get(childId);\n            if (!child)\n                continue;\n            child.parent = node;\n        }\n    }\n    const samples = [];\n    const sampleTimes = [];\n    // The first delta is relative to the profile startTime.\n    // Ref: https://github.com/v8/v8/blob/44bd8fd7/src/inspector/js_protocol.json#L1485\n    let elapsed = chromeProfile.timeDeltas[0];\n    // Prevents negative time deltas from causing bad data. See\n    // https://github.com/jlfwong/speedscope/pull/305 for details.\n    let lastValidElapsed = elapsed;\n    let lastNodeId = NaN;\n    // The chrome CPU profile format doesn't collapse identical samples. We'll do that\n    // here to save a ton of work later doing mergers.\n    for (let i = 0; i < chromeProfile.samples.length; i++) {\n        const nodeId = chromeProfile.samples[i];\n        if (nodeId != lastNodeId) {\n            samples.push(nodeId);\n            if (elapsed < lastValidElapsed) {\n                sampleTimes.push(lastValidElapsed);\n            }\n            else {\n                sampleTimes.push(elapsed);\n                lastValidElapsed = elapsed;\n            }\n        }\n        if (i === chromeProfile.samples.length - 1) {\n            if (!isNaN(lastNodeId)) {\n                samples.push(lastNodeId);\n                if (elapsed < lastValidElapsed) {\n                    sampleTimes.push(lastValidElapsed);\n                }\n                else {\n                    sampleTimes.push(elapsed);\n                    lastValidElapsed = elapsed;\n                }\n            }\n        }\n        else {\n            const timeDelta = chromeProfile.timeDeltas[i + 1];\n            elapsed += timeDelta;\n            lastNodeId = nodeId;\n        }\n    }\n    let prevStack = [];\n    for (let i = 0; i < samples.length; i++) {\n        const value = sampleTimes[i];\n        const nodeId = samples[i];\n        let stackTop = nodeById.get(nodeId);\n        if (!stackTop)\n            continue;\n        // Find lowest common ancestor of the current stack and the previous one\n        let lca = null;\n        // This is O(n^2), but n should be relatively small here (stack height),\n        // so hopefully this isn't much of a problem\n        for (lca = stackTop; lca && prevStack.indexOf(lca) === -1; lca = shouldPlaceOnTopOfPreviousStack(lca.callFrame.functionName)\n            ? utils_1.lastOf(prevStack)\n            : lca.parent || null) { }\n        // Close frames that are no longer open\n        while (prevStack.length > 0 && utils_1.lastOf(prevStack) != lca) {\n            const closingNode = prevStack.pop();\n            const frame = frameInfoForCallFrame(closingNode.callFrame);\n            profile.leaveFrame(frame, value);\n        }\n        // Open frames that are now becoming open\n        const toOpen = [];\n        for (let node = stackTop; node && node != lca && !shouldIgnoreFunction(node.callFrame); \n        // Place Chrome internal functions on top of the previous call stack\n        node = shouldPlaceOnTopOfPreviousStack(node.callFrame.functionName)\n            ? utils_1.lastOf(prevStack)\n            : node.parent || null) {\n            toOpen.push(node);\n        }\n        toOpen.reverse();\n        for (let node of toOpen) {\n            profile.enterFrame(frameInfoForCallFrame(node.callFrame), value);\n        }\n        prevStack = prevStack.concat(toOpen);\n    }\n    // Close frames that are open at the end of the trace\n    for (let i = prevStack.length - 1; i >= 0; i--) {\n        profile.leaveFrame(frameInfoForCallFrame(prevStack[i].callFrame), utils_1.lastOf(sampleTimes));\n    }\n    profile.setValueFormatter(new value_formatters_1.TimeFormatter('microseconds'));\n    return profile.build();\n}\nexports.importFromChromeCPUProfile = importFromChromeCPUProfile;\nfunction importFromOldV8CPUProfile(content) {\n    return importFromChromeCPUProfile(v8cpuFormatter_1.chromeTreeToNodes(content));\n}\nexports.importFromOldV8CPUProfile = importFromOldV8CPUProfile;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.chromeTreeToNodes = void 0;\nfunction treeToArray(root) {\n    const nodes = [];\n    function visit(node) {\n        nodes.push({\n            id: node.id,\n            callFrame: {\n                columnNumber: 0,\n                functionName: node.functionName,\n                lineNumber: node.lineNumber,\n                scriptId: node.scriptId,\n                url: node.url,\n            },\n            hitCount: node.hitCount,\n            children: node.children.map(child => child.id),\n        });\n        node.children.forEach(visit);\n    }\n    visit(root);\n    return nodes;\n}\nfunction timestampsToDeltas(timestamps, startTime) {\n    return timestamps.map((timestamp, index) => {\n        const lastTimestamp = index === 0 ? startTime * 1000000 : timestamps[index - 1];\n        return timestamp - lastTimestamp;\n    });\n}\n/**\n * Convert the old tree-based format to the new flat-array based format\n */\nfunction chromeTreeToNodes(content) {\n    // Note that both startTime and endTime are now in microseconds\n    return {\n        samples: content.samples,\n        startTime: content.startTime * 1000000,\n        endTime: content.endTime * 1000000,\n        nodes: treeToArray(content.head),\n        timeDeltas: timestampsToDeltas(content.timestamps, content.startTime),\n    };\n}\nexports.chromeTreeToNodes = chromeTreeToNodes;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.demangleCpp = void 0;\nlet cppfilt;\nconst cache = new Map();\n// This function converts a mangled C++ name such as \"__ZNK7Support6ColorFeqERKS0_\"\n// into a human-readable symbol (in this case \"Support::ColorF::==(Support::ColorF&)\")\nfunction demangleCpp(name) {\n    if (name.startsWith('__Z')) {\n        let result = cache.get(name);\n        if (result !== undefined) {\n            name = result;\n        }\n        else {\n            if (!cppfilt) {\n                cppfilt = new Function('exports', code)();\n            }\n            result = cppfilt(name.slice(1));\n            result = result === '(null)' ? name : result;\n            cache.set(name, result);\n            name = result;\n        }\n    }\n    return name;\n}\nexports.demangleCpp = demangleCpp;\n// This was taken from https://d.fuqu.jp/c++filtjs/\nconst code = `\nreturn function(){function r(r){eval.call(null,r)}function a(r){throw print(r+\":\\\\n\"+(new Error).stack),ke=!0,\"Assertion: \"+r}function e(r,e){r||a(\"Assertion failed: \"+e)}function i(r,a,i,v){function t(r,a){if(\"string\"==a){var e=Oe;return le.stackAlloc(r.length+1),A(r,e),e}return r}function f(r,a){return\"string\"==a?s(r):r}try{func=ce.Module[\"_\"+r]}catch(r){}e(func,\"Cannot call unknown function \"+r+\" (perhaps LLVM optimizations or closure removed it?)\");var _=0,n=v?v.map(function(r){return t(r,i[_++])}):[];return f(func.apply(null,n),a)}function v(r,a,e){return function(){return i(r,a,e,Array.prototype.slice.call(arguments))}}function t(r,e,i,v){switch(i=i||\"i8\",\"*\"===i[i.length-1]&&(i=\"i32\"),i){case\"i1\":Ae[r]=e;break;case\"i8\":Ae[r]=e;break;case\"i16\":ye[r>>1]=e;break;case\"i32\":Se[r>>2]=e;break;case\"i64\":Se[r>>2]=e;break;case\"float\":Ce[r>>2]=e;break;case\"double\":ze[0]=e,Se[r>>2]=xe[0],Se[r+4>>2]=xe[1];break;default:a(\"invalid type for setValue: \"+i)}}function f(r,e,i){switch(e=e||\"i8\",\"*\"===e[e.length-1]&&(e=\"i32\"),e){case\"i1\":return Ae[r];case\"i8\":return Ae[r];case\"i16\":return ye[r>>1];case\"i32\":return Se[r>>2];case\"i64\":return Se[r>>2];case\"float\":return Ce[r>>2];case\"double\":return xe[0]=Se[r>>2],xe[1]=Se[r+4>>2],ze[0];default:a(\"invalid type for setValue: \"+e)}return null}function _(r,a,e){var i,v;\"number\"==typeof r?(i=!0,v=r):(i=!1,v=r.length);var f=\"string\"==typeof a?a:null,_=[Jr,le.stackAlloc,le.staticAlloc][void 0===e?we:e](Math.max(v,f?1:a.length));if(i)return Fa(_,0,v),_;for(var s,n=0;n<v;){var o=r[n];\"function\"==typeof o&&(o=le.getFunctionIndex(o)),s=f||a[n],0!==s?(\"i64\"==s&&(s=\"i32\"),t(_+n,o,s),n+=le.getNativeTypeSize(s)):n++}return _}function s(r,a){for(var e,i=\"undefined\"==typeof a,v=\"\",t=0,f=String.fromCharCode(0);;){if(e=String.fromCharCode(ge[r+t]),i&&e==f)break;if(v+=e,t+=1,!i&&t==a)break}return v}function n(r){for(var a=\"\",e=0;e<r.length;e++)a+=String.fromCharCode(r[e]);return a}function o(r){return r+4095>>12<<12}function l(){for(;Le<=Ie;)Le=o(2*Le);var r=Ae,a=new ArrayBuffer(Le);Ae=new Int8Array(a),ye=new Int16Array(a),Se=new Int32Array(a),ge=new Uint8Array(a),me=new Uint16Array(a),Me=new Uint32Array(a),Ce=new Float32Array(a),Re=new Float64Array(a),Ae.set(r)}function b(r){for(;r.length>0;){var a=r.shift(),e=a.func;\"number\"==typeof e&&(e=pe[e]),e(void 0===a.arg?null:a.arg)}}function k(){b(Ve)}function u(){b(Be),be.print()}function c(r,a){return Array.prototype.slice.call(Ae.subarray(r,r+a))}function h(r,a){for(var e=new Uint8Array(a),i=0;i<a;++i)e[i]=Ae[r+i];return e.buffer}function d(r){for(var a=0;Ae[r+a];)a++;return a}function w(r,a){var e=d(r);a&&e++;var i=c(r,e);return a&&(i[e-1]=0),i}function p(r,a){for(var e=[],i=0;i<r.length;){var v=r.charCodeAt(i);v>255&&(v&=255),e.push(v),i+=1}return a||e.push(0),e}function E(r){for(var a=[],e=0;e<r.length;e++){var i=r[e];i>255&&(i&=255),a.push(String.fromCharCode(i))}return a.join(\"\")}function A(r,a,e){for(var i=0;i<r.length;){var v=r.charCodeAt(i);v>255&&(v&=255),Ae[a+i]=v,i+=1}e||(Ae[a+i]=0)}function g(r,a,e,i){return r>=0?r:a<=32?2*Math.abs(1<<a-1)+r:Math.pow(2,a)+r}function y(r,a,e,i){if(r<=0)return r;var v=a<=32?Math.abs(1<<a-1):Math.pow(2,a-1);return r>=v&&(a<=32||r>v)&&(r=-2*v+r),r}function m(r,a,e){if(0==(0|r)|0==(0|a)|0==(0|e))var i=0;else{Se[r>>2]=0,Se[r+4>>2]=a,Se[r+8>>2]=e;var i=1}var i;return i}function S(r,a,e){if(0==(0|r)|(0|a)<0|0==(0|e))var i=0;else{Se[r>>2]=41,Se[r+4>>2]=a,Se[r+8>>2]=e;var i=1}var i;return i}function M(r,a,e){if(0==(0|r)|0==(0|e))var i=0;else{Se[r>>2]=6,Se[r+4>>2]=a,Se[r+8>>2]=e;var i=1}var i;return i}function C(r,a,e){if(0==(0|r)|0==(0|e))var i=0;else{Se[r>>2]=7,Se[r+4>>2]=a,Se[r+8>>2]=e;var i=1}var i;return i}function R(r,a){var e,i=0==(0|a);do if(i)var v=0;else{var e=(r+32|0)>>2,t=Se[e];if((0|t)>=(0|Se[r+36>>2])){var v=0;break}var f=(t<<2)+Se[r+28>>2]|0;Se[f>>2]=a;var _=Se[e]+1|0;Se[e]=_;var v=1}while(0);var v;return v}function T(r,a){var e,e=(r+12|0)>>2,i=Se[e],v=i+1|0;Se[e]=v;var t=Ae[i]<<24>>24==95;do if(t){var f=i+2|0;if(Se[e]=f,Ae[v]<<24>>24!=90){var _=0;break}var s=O(r,a),_=s}else var _=0;while(0);var _;return _}function O(r,a){var e=r+12|0,i=Ae[Se[e>>2]];r:do if(i<<24>>24==71||i<<24>>24==84)var v=Tr(r),t=v;else{var f=Ar(r),_=0==(0|f)|0==(0|a);do if(!_){if(0!=(1&Se[r+8>>2]|0))break;var s=Me[f>>2],n=(s-25|0)>>>0<3;a:do if(n)for(var o=f;;){var o,l=Me[o+4>>2],b=Me[l>>2];if((b-25|0)>>>0>=3){var k=l,u=b;break a}var o=l}else var k=f,u=s;while(0);var u,k;if(2!=(0|u)){var t=k;break r}var c=k+8|0,h=Me[c>>2],d=(Se[h>>2]-25|0)>>>0<3;a:do if(d)for(var w=h;;){var w,p=Me[w+4>>2];if((Se[p>>2]-25|0)>>>0>=3){var E=p;break a}var w=p}else var E=h;while(0);var E;Se[c>>2]=E;var t=k;break r}while(0);var A=Ae[Se[e>>2]];if(A<<24>>24==0||A<<24>>24==69){var t=f;break}var g=Or(f),y=Sr(r,g),m=D(r,3,f,y),t=m}while(0);var t;return t}function N(r){var a,e,i=Oe;Oe+=4;var v=i,e=v>>2,a=(r+12|0)>>2,t=Me[a],f=Ae[t],_=f<<24>>24;r:do if(f<<24>>24==114||f<<24>>24==86||f<<24>>24==75){var s=I(r,v,0);if(0==(0|s)){var n=0;break}var o=N(r);Se[s>>2]=o;var l=Se[e],b=R(r,l);if(0==(0|b)){var n=0;break}var n=Se[e]}else{do{if(97==(0|_)||98==(0|_)||99==(0|_)||100==(0|_)||101==(0|_)||102==(0|_)||103==(0|_)||104==(0|_)||105==(0|_)||106==(0|_)||108==(0|_)||109==(0|_)||110==(0|_)||111==(0|_)||115==(0|_)||116==(0|_)||118==(0|_)||119==(0|_)||120==(0|_)||121==(0|_)||122==(0|_)){var k=ai+20*(_-97)|0,u=P(r,k);Se[e]=u;var c=r+48|0,h=Se[c>>2]+Se[Se[u+4>>2]+4>>2]|0;Se[c>>2]=h;var d=Se[a]+1|0;Se[a]=d;var n=u;break r}if(117==(0|_)){Se[a]=t+1|0;var w=L(r),p=D(r,34,w,0);Se[e]=p;var E=p}else if(70==(0|_)){var A=F(r);Se[e]=A;var E=A}else if(48==(0|_)||49==(0|_)||50==(0|_)||51==(0|_)||52==(0|_)||53==(0|_)||54==(0|_)||55==(0|_)||56==(0|_)||57==(0|_)||78==(0|_)||90==(0|_)){var g=X(r);Se[e]=g;var E=g}else if(65==(0|_)){var y=j(r);Se[e]=y;var E=y}else if(77==(0|_)){var m=U(r);Se[e]=m;var E=m}else if(84==(0|_)){var S=x(r);if(Se[e]=S,Ae[Se[a]]<<24>>24!=73){var E=S;break}var M=R(r,S);if(0==(0|M)){var n=0;break r}var C=Se[e],T=z(r),O=D(r,4,C,T);Se[e]=O;var E=O}else if(83==(0|_)){var B=ge[t+1|0];if((B-48&255&255)<10|B<<24>>24==95|(B-65&255&255)<26){var H=V(r,0);if(Se[e]=H,Ae[Se[a]]<<24>>24!=73){var n=H;break r}var K=z(r),Y=D(r,4,H,K);Se[e]=Y;var E=Y}else{var G=X(r);if(Se[e]=G,0==(0|G)){var E=0;break}if(21==(0|Se[G>>2])){var n=G;break r}var E=G}}else if(80==(0|_)){Se[a]=t+1|0;var W=N(r),Z=D(r,29,W,0);Se[e]=Z;var E=Z}else if(82==(0|_)){Se[a]=t+1|0;var Q=N(r),q=D(r,30,Q,0);Se[e]=q;var E=q}else if(67==(0|_)){Se[a]=t+1|0;var $=N(r),J=D(r,31,$,0);Se[e]=J;var E=J}else if(71==(0|_)){Se[a]=t+1|0;var rr=N(r),ar=D(r,32,rr,0);Se[e]=ar;var E=ar}else{if(85!=(0|_)){var n=0;break r}Se[a]=t+1|0;var er=L(r);Se[e]=er;var ir=N(r),vr=Se[e],tr=D(r,28,ir,vr);Se[e]=tr;var E=tr}}while(0);var E,fr=R(r,E);if(0==(0|fr)){var n=0;break}var n=Se[e]}while(0);var n;return Oe=i,n}function I(r,a,e){for(var i,v=r+12|0,t=0!=(0|e),f=t?25:22,i=(r+48|0)>>2,_=t?26:23,s=t?27:24,n=a;;){var n,o=Se[v>>2],l=Ae[o];if(l<<24>>24!=114&&l<<24>>24!=86&&l<<24>>24!=75){var b=n;break}var k=o+1|0;if(Se[v>>2]=k,l<<24>>24==114){var u=Se[i]+9|0;Se[i]=u;var c=f}else if(l<<24>>24==86){var h=Se[i]+9|0;Se[i]=h;var c=_}else{var d=Se[i]+6|0;Se[i]=d;var c=s}var c,w=D(r,c,0,0);if(Se[n>>2]=w,0==(0|w)){var b=0;break}var n=w+4|0}var b;return b}function P(r,a){var e=0==(0|a);do if(e)var i=0;else{var v=J(r);if(0==(0|v)){var i=0;break}Se[v>>2]=33,Se[v+4>>2]=a;var i=v}while(0);var i;return i}function D(r,a,e,i){var v,t;do{if(1==(0|a)||2==(0|a)||3==(0|a)||4==(0|a)||10==(0|a)||28==(0|a)||37==(0|a)||43==(0|a)||44==(0|a)||45==(0|a)||46==(0|a)||47==(0|a)||48==(0|a)||49==(0|a)||50==(0|a)){if(0==(0|e)|0==(0|i)){var f=0;t=7;break}t=5;break}if(8==(0|a)||9==(0|a)||11==(0|a)||12==(0|a)||13==(0|a)||14==(0|a)||15==(0|a)||16==(0|a)||17==(0|a)||18==(0|a)||19==(0|a)||20==(0|a)||29==(0|a)||30==(0|a)||31==(0|a)||32==(0|a)||34==(0|a)||38==(0|a)||39==(0|a)||42==(0|a)){if(0==(0|e)){var f=0;t=7;break}t=5;break}if(36==(0|a)){if(0==(0|i)){var f=0;t=7;break}t=5;break}if(35==(0|a)||22==(0|a)||23==(0|a)||24==(0|a)||25==(0|a)||26==(0|a)||27==(0|a))t=5;else{var f=0;t=7}}while(0);do if(5==t){var _=J(r),v=_>>2;if(0==(0|_)){var f=0;break}Se[v]=a,Se[v+1]=e,Se[v+2]=i;var f=_}while(0);var f;return f}function L(r){var a=sr(r);if((0|a)<1)var e=0;else{var i=Rr(r,a);Se[r+44>>2]=i;var e=i}var e;return e}function F(r){var a,a=(r+12|0)>>2,e=Se[a],i=e+1|0;if(Se[a]=i,Ae[e]<<24>>24==70){if(Ae[i]<<24>>24==89){var v=e+2|0;Se[a]=v}var t=Sr(r,1),f=Se[a],_=f+1|0;Se[a]=_;var s=Ae[f]<<24>>24==69?t:0,n=s}else var n=0;var n;return n}function X(r){var a=Ar(r);return a}function j(r){var a,a=(r+12|0)>>2,e=Se[a],i=e+1|0;Se[a]=i;var v=Ae[e]<<24>>24==65;do if(v){var t=Ae[i];if(t<<24>>24==95)var f=0;else if((t-48&255&255)<10){for(var _=i;;){var _,s=_+1|0;if(Se[a]=s,(Ae[s]-48&255&255)>=10)break;var _=s}var n=s-i|0,o=lr(r,i,n);if(0==(0|o)){var l=0;break}var f=o}else{var b=nr(r);if(0==(0|b)){var l=0;break}var f=b}var f,k=Se[a],u=k+1|0;if(Se[a]=u,Ae[k]<<24>>24!=95){var l=0;break}var c=N(r),h=D(r,36,f,c),l=h}else var l=0;while(0);var l;return l}function U(r){var a=Oe;Oe+=4;var e=a,i=r+12|0,v=Se[i>>2],t=v+1|0;Se[i>>2]=t;var f=Ae[v]<<24>>24==77;r:do if(f){var _=N(r),s=I(r,e,1);if(0==(0|s)){var n=0;break}var o=N(r);Se[s>>2]=o;var l=(0|s)==(0|e);do if(!l){if(35==(0|Se[o>>2]))break;var b=Se[e>>2],k=R(r,b);if(0==(0|k)){var n=0;break r}}while(0);var u=Se[e>>2],c=D(r,37,_,u),n=c}else var n=0;while(0);var n;return Oe=a,n}function x(r){var a,a=(r+12|0)>>2,e=Se[a],i=e+1|0;Se[a]=i;var v=Ae[e]<<24>>24==84;do if(v){if(Ae[i]<<24>>24==95)var t=0,f=i;else{var _=sr(r);if((0|_)<0){var s=0;break}var t=_+1|0,f=Se[a]}var f,t;if(Se[a]=f+1|0,Ae[f]<<24>>24!=95){var s=0;break}var n=r+40|0,o=Se[n>>2]+1|0;Se[n>>2]=o;var l=Er(r,t),s=l}else var s=0;while(0);var s;return s}function z(r){var a,e=Oe;Oe+=4;var i=e,v=r+44|0,t=Se[v>>2],a=(r+12|0)>>2,f=Se[a],_=f+1|0;Se[a]=_;var s=Ae[f]<<24>>24==73;r:do if(s){Se[i>>2]=0;for(var n=i;;){var n,o=_r(r);if(0==(0|o)){var l=0;break r}var b=D(r,39,o,0);if(Se[n>>2]=b,0==(0|b)){var l=0;break r}var k=Se[a];if(Ae[k]<<24>>24==69)break;var n=b+8|0}var u=k+1|0;Se[a]=u,Se[v>>2]=t;var l=Se[i>>2]}else var l=0;while(0);var l;return Oe=e,l}function V(r,a){var e,e=(r+12|0)>>2,i=Se[e],v=i+1|0;Se[e]=v;var t=Ae[i]<<24>>24==83;r:do if(t){var f=i+2|0;Se[e]=f;var _=ge[v];if(_<<24>>24==95)var s=0;else{if(!((_-48&255&255)<10|(_-65&255&255)<26)){var n=8&Se[r+8>>2],o=n>>>3,l=0!=(0|n)|0==(0|a);do if(l)var b=o;else{if((Ae[f]-67&255&255)>=2){var b=o;break}var b=1}while(0);for(var b,k=0|ei;;){var k;if(k>>>0>=(ei+196|0)>>>0){var u=0;break r}if(_<<24>>24==Ae[0|k]<<24>>24)break;var k=k+28|0}var c=Se[k+20>>2];if(0!=(0|c)){var h=Se[k+24>>2],d=fr(r,c,h);Se[r+44>>2]=d}if(0==(0|b))var w=k+8|0,p=k+4|0;else var w=k+16|0,p=k+12|0;var p,w,E=Se[w>>2],A=Se[p>>2],g=r+48|0,y=Se[g>>2]+E|0;Se[g>>2]=y;var m=fr(r,A,E),u=m;break}for(var S=_,M=0,C=f;;){var C,M,S;if((S-48&255&255)<10)var R=36*M-48|0;else{if((S-65&255&255)>=26){var u=0;break r}var R=36*M-55|0}var R,T=(S<<24>>24)+R|0;if((0|T)<0){var u=0;break r}var O=C+1|0;Se[e]=O;var N=ge[C];if(N<<24>>24==95)break;var S=N,M=T,C=O}var s=T+1|0}var s;if((0|s)>=(0|Se[r+32>>2])){var u=0;break}var I=r+40|0,P=Se[I>>2]+1|0;Se[I>>2]=P;var u=Se[Se[r+28>>2]+(s<<2)>>2]}else var u=0;while(0);var u;return u}function B(r,a,e,i){var v,t,f,_,s=Oe;Oe+=28;var n,o=s,_=o>>2;Se[_]=r;var l=e+1|0,f=(o+12|0)>>2;Se[f]=l;var b=Jr(l),t=(o+4|0)>>2;if(Se[t]=b,0==(0|b))var k=0,u=1;else{var v=(o+8|0)>>2;Se[v]=0,Se[_+4]=0,Se[_+5]=0;var c=o+24|0;Se[c>>2]=0,H(o,a);var h=Me[t],d=0==(0|h);do{if(!d){var w=Me[v];if(w>>>0>=Me[f]>>>0){n=5;break}Se[v]=w+1|0,Ae[h+w|0]=0,n=6;break}n=5}while(0);5==n&&Y(o,0);var p=Se[t],E=0==(0|p)?Se[c>>2]:Se[f],k=p,u=E}var u,k;return Se[i>>2]=u,Oe=s,k}function H(r,a){var e,i,v,t,f,_,s,n,o,l,b,k,u,c,h,d,w,p,E,A,g,y,m,S,M,C,R,T,O,N,I,P,D,L,F,X,j,U,x,z,V,B,K,G,W,J,vr,tr,fr,_r,sr,nr,or,lr,br,kr,ur,cr,hr,dr,wr,pr=a>>2,Er=r>>2,Ar=Oe;Oe+=184;var gr,yr=Ar,wr=yr>>2,mr=Ar+64,dr=mr>>2,Sr=Ar+72,Mr=Ar+88,Cr=Ar+104,hr=Cr>>2,Rr=Ar+168,Tr=0==(0|a);r:do if(Tr)Z(r);else{var cr=(r+4|0)>>2,Or=Me[cr];if(0==(0|Or))break;var Nr=0|a,Ir=Me[Nr>>2];a:do{if(0==(0|Ir)){if(0!=(4&Se[Er]|0)){var Pr=Se[pr+1],Dr=Se[pr+2];q(r,Pr,Dr);break r}var ur=(r+8|0)>>2,Lr=Me[ur],Fr=a+8|0,Xr=Me[Fr>>2];if((Xr+Lr|0)>>>0>Me[Er+3]>>>0){var jr=Se[pr+1];Q(r,jr,Xr);break r}var Ur=Or+Lr|0,xr=Se[pr+1];Pa(Ur,xr,Xr,1);var zr=Se[ur]+Se[Fr>>2]|0;Se[ur]=zr;break r}if(1==(0|Ir)||2==(0|Ir)){var Vr=Se[pr+1];H(r,Vr);var Br=0==(4&Se[Er]|0),Hr=Me[cr],Kr=0!=(0|Hr);e:do if(Br){do if(Kr){var kr=(r+8|0)>>2,Yr=Me[kr];if((Yr+2|0)>>>0>Me[Er+3]>>>0)break;var Gr=Hr+Yr|0;oe=14906,Ae[Gr]=255&oe,oe>>=8,Ae[Gr+1]=255&oe;var Wr=Se[kr]+2|0;Se[kr]=Wr;break e}while(0);Q(r,0|He.__str120,2)}else{do if(Kr){var Zr=r+8|0,Qr=Me[Zr>>2];if(Qr>>>0>=Me[Er+3]>>>0)break;Se[Zr>>2]=Qr+1|0,Ae[Hr+Qr|0]=46;break e}while(0);Y(r,46)}while(0);var qr=Se[pr+2];H(r,qr);break r}if(3==(0|Ir)){for(var br=(r+20|0)>>2,$r=Me[br],lr=(r+16|0)>>2,Jr=a,ra=0,aa=$r;;){var aa,ra,Jr,ea=Me[Jr+4>>2];if(0==(0|ea)){var ia=ra,va=0;gr=33;break}if(ra>>>0>3){Z(r);break r}var ta=(ra<<4)+yr|0;Se[ta>>2]=aa,Se[br]=ta,Se[((ra<<4)+4>>2)+wr]=ea,Se[((ra<<4)+8>>2)+wr]=0;var fa=Me[lr];Se[((ra<<4)+12>>2)+wr]=fa;var _a=ra+1|0,sa=0|ea,na=Me[sa>>2];if((na-25|0)>>>0>=3){gr=25;break}var Jr=ea,ra=_a,aa=ta}e:do if(25==gr){if(4==(0|na)){Se[dr]=fa,Se[lr]=mr,Se[dr+1]=ea;var oa=Se[sa>>2],la=mr}else var oa=na,la=fa;var la,oa;if(2!=(0|oa)){var ia=_a,va=sa;break}for(var ba=_a,ka=ea+8|0;;){var ka,ba,ua=Me[ka>>2];if((Se[ua>>2]-25|0)>>>0>=3){var ia=ba,va=sa;break e}if(ba>>>0>3)break;var ca=(ba<<4)+yr|0,ha=ba-1|0,da=(ha<<4)+yr|0,or=ca>>2,nr=da>>2;Se[or]=Se[nr],Se[or+1]=Se[nr+1],Se[or+2]=Se[nr+2],Se[or+3]=Se[nr+3],Se[ca>>2]=da,Se[br]=ca,Se[((ha<<4)+4>>2)+wr]=ua,Se[((ha<<4)+8>>2)+wr]=0,Se[((ha<<4)+12>>2)+wr]=la;var ba=ba+1|0,ka=ua+4|0}Z(r);break r}while(0);var va,ia,wa=Se[pr+2];if(H(r,wa),4==(0|Se[va>>2])){var pa=Se[dr];Se[lr]=pa}var Ea=0==(0|ia);e:do if(!Ea)for(var Aa=r+8|0,ga=r+12|0,ya=ia;;){var ya,ma=ya-1|0;if(0==(0|Se[((ma<<4)+8>>2)+wr])){var Sa=Me[cr],Ma=0==(0|Sa);do{if(!Ma){var Ca=Me[Aa>>2];if(Ca>>>0>=Me[ga>>2]>>>0){gr=41;break}Se[Aa>>2]=Ca+1|0,Ae[Sa+Ca|0]=32,gr=42;break}gr=41}while(0);41==gr&&Y(r,32);var Ra=Se[((ma<<4)+4>>2)+wr];$(r,Ra)}if(0==(0|ma))break e;var ya=ma}while(0);Se[br]=$r;break r}if(4==(0|Ir)){var sr=(r+20|0)>>2,Ta=Se[sr];Se[sr]=0;var Oa=Se[pr+1];H(r,Oa);var Na=Me[cr],Ia=0==(0|Na);do{if(!Ia){var _r=(r+8|0)>>2,Da=Me[_r],La=0==(0|Da);do if(!La){if(Ae[Na+(Da-1)|0]<<24>>24!=60)break;Da>>>0<Me[Er+3]>>>0?(Se[_r]=Da+1|0,Ae[Na+Da|0]=32):Y(r,32)}while(0);var Fa=Me[cr];if(0==(0|Fa)){gr=54;break}var Xa=Me[_r];if(Xa>>>0>=Me[Er+3]>>>0){gr=54;break}Se[_r]=Xa+1|0,Ae[Fa+Xa|0]=60,gr=55;break}gr=54}while(0);54==gr&&Y(r,60);var ja=Se[pr+2];H(r,ja);var Ua=Me[cr],xa=0==(0|Ua);do{if(!xa){var fr=(r+8|0)>>2,za=Me[fr],Va=0==(0|za);do if(!Va){if(Ae[Ua+(za-1)|0]<<24>>24!=62)break;za>>>0<Me[Er+3]>>>0?(Se[fr]=za+1|0,Ae[Ua+za|0]=32):Y(r,32)}while(0);var Ba=Me[cr];if(0==(0|Ba)){gr=64;break}var Ha=Me[fr];if(Ha>>>0>=Me[Er+3]>>>0){gr=64;break}Se[fr]=Ha+1|0,Ae[Ba+Ha|0]=62,gr=65;break}gr=64}while(0);64==gr&&Y(r,62),Se[sr]=Ta;break r}if(5==(0|Ir)){var tr=(r+16|0)>>2,Ka=Me[tr];if(0==(0|Ka)){Z(r);break r}for(var Ya=Se[pr+1],Ga=Se[Ka+4>>2];;){var Ga,Ya,Wa=Se[Ga+8>>2];if(0==(0|Wa))break;if(39!=(0|Se[Wa>>2])){Z(r);break r}if((0|Ya)<1){if(0!=(0|Ya))break;var Za=Se[Ka>>2];Se[tr]=Za;var Qa=Se[Wa+4>>2];H(r,Qa),Se[tr]=Ka;break r}var Ya=Ya-1|0,Ga=Wa}Z(r);break r}if(6==(0|Ir)){var qa=Se[pr+2];H(r,qa);break r}if(7==(0|Ir)){var $a=r+8|0,Ja=Me[$a>>2];Ja>>>0<Me[Er+3]>>>0?(Se[$a>>2]=Ja+1|0,Ae[Or+Ja|0]=126):Y(r,126);var re=Se[pr+2];H(r,re);break r}if(8==(0|Ir)){var vr=(r+8|0)>>2,ae=Me[vr];if((ae+11|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str121,11);else{for(var ee=Or+ae|0,ie=0|He.__str121,ve=ee,te=ie+11;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var fe=Se[vr]+11|0;Se[vr]=fe}var _e=Se[pr+1];H(r,_e);break r}if(9==(0|Ir)){var J=(r+8|0)>>2,se=Me[J];if((se+8|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str122,8);else{var ne=Or+se|0,le=0|ne;oe=542397526,Ae[le]=255&oe,oe>>=8,Ae[le+1]=255&oe,oe>>=8,Ae[le+2]=255&oe,oe>>=8,Ae[le+3]=255&oe;var be=ne+4|0;oe=544370534,Ae[be]=255&oe,oe>>=8,Ae[be+1]=255&oe,oe>>=8,Ae[be+2]=255&oe,oe>>=8,Ae[be+3]=255&oe;var ke=Se[J]+8|0;Se[J]=ke}var ue=Se[pr+1];H(r,ue);break r}if(10==(0|Ir)){var W=(r+8|0)>>2,ce=Me[W],he=r+12|0;if((ce+24|0)>>>0>Me[he>>2]>>>0)Q(r,0|He.__str123,24);else{var de=Or+ce|0;Pa(de,0|He.__str123,24,1);var we=Se[W]+24|0;Se[W]=we}var pe=Se[pr+1];H(r,pe);var Ee=Me[cr],ge=0==(0|Ee);do{if(!ge){var ye=Me[W];if((ye+4|0)>>>0>Me[he>>2]>>>0){gr=96;break}var me=Ee+ye|0;oe=762210605,Ae[me]=255&oe,oe>>=8,Ae[me+1]=255&oe,oe>>=8,Ae[me+2]=255&oe,oe>>=8,Ae[me+3]=255&oe;var Ce=Se[W]+4|0;Se[W]=Ce,gr=97;break}gr=96}while(0);96==gr&&Q(r,0|He.__str124,4);var Re=Se[pr+2];H(r,Re);break r}if(11==(0|Ir)){var G=(r+8|0)>>2,Te=Me[G];if((Te+13|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str125,13);else{for(var Ne=Or+Te|0,ie=0|He.__str125,ve=Ne,te=ie+13;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var Ie=Se[G]+13|0;Se[G]=Ie}var Pe=Se[pr+1];H(r,Pe);break r}if(12==(0|Ir)){var K=(r+8|0)>>2,De=Me[K];if((De+18|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str126,18);else{for(var Le=Or+De|0,ie=0|He.__str126,ve=Le,te=ie+18;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var Fe=Se[K]+18|0;Se[K]=Fe}var Xe=Se[pr+1];H(r,Xe);break r}if(13==(0|Ir)){var B=(r+8|0)>>2,je=Me[B];if((je+16|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str127,16);else{for(var Ue=Or+je|0,ie=0|He.__str127,ve=Ue,te=ie+16;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var xe=Se[B]+16|0;Se[B]=xe}var ze=Se[pr+1];H(r,ze);break r}if(14==(0|Ir)){var V=(r+8|0)>>2,Ve=Me[V];if((Ve+21|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str128,21);else{var Be=Or+Ve|0;Pa(Be,0|He.__str128,21,1);var Ke=Se[V]+21|0;Se[V]=Ke}var Ye=Se[pr+1];H(r,Ye);break r}if(15==(0|Ir)){var z=(r+8|0)>>2,Ge=Me[z];if((Ge+17|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str129,17);else{for(var We=Or+Ge|0,ie=0|He.__str129,ve=We,te=ie+17;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var Ze=Se[z]+17|0;Se[z]=Ze}var Qe=Se[pr+1];H(r,Qe);break r}if(16==(0|Ir)){var x=(r+8|0)>>2,qe=Me[x];if((qe+26|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str130,26);else{var $e=Or+qe|0;Pa($e,0|He.__str130,26,1);var Je=Se[x]+26|0;Se[x]=Je}var ri=Se[pr+1];H(r,ri);break r}if(17==(0|Ir)){var U=(r+8|0)>>2,ai=Me[U];if((ai+15|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str131,15);else{for(var ei=Or+ai|0,ie=0|He.__str131,ve=ei,te=ie+15;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var ii=Se[U]+15|0;Se[U]=ii}var vi=Se[pr+1];H(r,vi);break r}if(18==(0|Ir)){var j=(r+8|0)>>2,ti=Me[j];if((ti+19|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str132,19);else{for(var fi=Or+ti|0,ie=0|He.__str132,ve=fi,te=ie+19;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var _i=Se[j]+19|0;Se[j]=_i}var si=Se[pr+1];H(r,si);break r}if(19==(0|Ir)){var X=(r+8|0)>>2,ni=Me[X];if((ni+24|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str133,24);else{var oi=Or+ni|0;Pa(oi,0|He.__str133,24,1);var li=Se[X]+24|0;Se[X]=li}var bi=Se[pr+1];H(r,bi);break r}if(20==(0|Ir)){var F=(r+8|0)>>2,ki=Me[F];if((ki+17|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str134,17);else{for(var ui=Or+ki|0,ie=0|He.__str134,ve=ui,te=ie+17;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var ci=Se[F]+17|0;Se[F]=ci}var hi=Se[pr+1];H(r,hi);break r}if(21==(0|Ir)){var L=(r+8|0)>>2,di=Me[L],wi=a+8|0,pi=Me[wi>>2];if((pi+di|0)>>>0>Me[Er+3]>>>0){var Ei=Se[pr+1];Q(r,Ei,pi);break r}var Ai=Or+di|0,gi=Se[pr+1];Pa(Ai,gi,pi,1);var yi=Se[L]+Se[wi>>2]|0;Se[L]=yi;break r}if(22==(0|Ir)||23==(0|Ir)||24==(0|Ir)){for(var mi=r+20|0;;){var mi,Si=Me[mi>>2];if(0==(0|Si))break a;if(0==(0|Se[Si+8>>2])){var Mi=Me[Se[Si+4>>2]>>2];if((Mi-22|0)>>>0>=3)break a;if((0|Mi)==(0|Ir))break}var mi=0|Si}var Ci=Se[pr+1];H(r,Ci);break r}if(25!=(0|Ir)&&26!=(0|Ir)&&27!=(0|Ir)&&28!=(0|Ir)&&29!=(0|Ir)&&30!=(0|Ir)&&31!=(0|Ir)&&32!=(0|Ir)){if(33==(0|Ir)){var D=(r+8|0)>>2,Ri=Me[D],P=(a+4|0)>>2,I=Me[P]>>2;if(0==(4&Se[Er]|0)){var Ti=Me[I+1];if((Ti+Ri|0)>>>0>Me[Er+3]>>>0){var Oi=Se[I];Q(r,Oi,Ti);break r}var Ni=Or+Ri|0,Ii=Se[I];Pa(Ni,Ii,Ti,1);var Pi=Se[D]+Se[Se[P]+4>>2]|0;Se[D]=Pi;break r}var Di=Me[I+3];if((Di+Ri|0)>>>0>Me[Er+3]>>>0){var Li=Se[I+2];Q(r,Li,Di);break r}var Fi=Or+Ri|0,Xi=Se[I+2];Pa(Fi,Xi,Di,1);var ji=Se[D]+Se[Se[P]+12>>2]|0;Se[D]=ji;break r}if(34==(0|Ir)){var Ui=Se[pr+1];H(r,Ui);break r}if(35==(0|Ir)){var N=(0|r)>>2;if(0!=(32&Se[N]|0)){var xi=Se[Er+5];rr(r,a,xi)}var zi=a+4|0,Vi=0==(0|Se[zi>>2]);e:do if(!Vi){var O=(r+20|0)>>2,Bi=Se[O],Hi=0|Mr;Se[Hi>>2]=Bi,Se[O]=Mr,Se[Mr+4>>2]=a;var Ki=Mr+8|0;Se[Ki>>2]=0;var Yi=Se[Er+4];Se[Mr+12>>2]=Yi;var Gi=Se[zi>>2];H(r,Gi);var Wi=Se[Hi>>2];if(Se[O]=Wi,0!=(0|Se[Ki>>2]))break r;if(0!=(32&Se[N]|0))break;var Zi=Me[cr],Qi=0==(0|Zi);do if(!Qi){var qi=r+8|0,$i=Me[qi>>2];if($i>>>0>=Me[Er+3]>>>0)break;Se[qi>>2]=$i+1|0,Ae[Zi+$i|0]=32;break e}while(0);Y(r,32)}while(0);if(0!=(32&Se[N]|0))break r;var Ji=Se[Er+5];rr(r,a,Ji);break r}if(36==(0|Ir)){var T=(r+20|0)>>2,rv=Me[T],av=0|Cr;Se[hr]=rv,Se[T]=av,Se[hr+1]=a;var ev=Cr+8|0;Se[ev>>2]=0;var iv=Se[Er+4];Se[hr+3]=iv;for(var vv=rv,tv=1;;){var tv,vv;if(0==(0|vv))break;if((Se[Se[vv+4>>2]>>2]-22|0)>>>0>=3)break;var fv=vv+8|0;if(0==(0|Se[fv>>2])){if(tv>>>0>3){Z(r);break r}var _v=(tv<<4)+Cr|0,R=_v>>2,C=vv>>2;Se[R]=Se[C],Se[R+1]=Se[C+1],Se[R+2]=Se[C+2],Se[R+3]=Se[C+3];var sv=Se[T];Se[_v>>2]=sv,Se[T]=_v,Se[fv>>2]=1;var nv=tv+1|0}else var nv=tv;var nv,vv=Se[vv>>2],tv=nv}var ov=Se[pr+2];if(H(r,ov),Se[T]=rv,0!=(0|Se[ev>>2]))break r;if(tv>>>0>1){for(var lv=tv;;){var lv,bv=lv-1|0,kv=Se[((bv<<4)+4>>2)+hr];if($(r,kv),bv>>>0<=1)break;var lv=bv}var uv=Se[T]}else var uv=rv;var uv;ar(r,a,uv);break r}if(37==(0|Ir)){var M=(r+20|0)>>2,cv=Se[M],hv=0|Rr;Se[hv>>2]=cv,Se[M]=Rr,Se[Rr+4>>2]=a;var dv=Rr+8|0;Se[dv>>2]=0;var wv=Se[Er+4];Se[Rr+12>>2]=wv;var pv=a+4|0,Ev=Se[pr+2];H(r,Ev);var Av=0==(0|Se[dv>>2]);e:do if(Av){var gv=Me[cr],yv=0==(0|gv);do{if(!yv){var mv=r+8|0,Sv=Me[mv>>2];if(Sv>>>0>=Me[Er+3]>>>0){gr=187;break}Se[mv>>2]=Sv+1|0,Ae[gv+Sv|0]=32,gr=188;break}gr=187}while(0);187==gr&&Y(r,32);var Mv=Se[pv>>2];H(r,Mv);var Cv=Me[cr],Rv=0==(0|Cv);do if(!Rv){var S=(r+8|0)>>2,Tv=Me[S];if((Tv+3|0)>>>0>Me[Er+3]>>>0)break;var Ov=Cv+Tv|0;Ae[Ov]=Ae[0|He.__str135],Ae[Ov+1]=Ae[(0|He.__str135)+1],Ae[Ov+2]=Ae[(0|He.__str135)+2];var Nv=Se[S]+3|0;Se[S]=Nv;break e}while(0);Q(r,0|He.__str135,3)}while(0);var Iv=Se[hv>>2];Se[M]=Iv;break r}if(38==(0|Ir)||39==(0|Ir)){var Pv=Se[pr+1];H(r,Pv);var Dv=a+8|0;if(0==(0|Se[Dv>>2]))break r;var Lv=Me[cr],Fv=0==(0|Lv);do{if(!Fv){var m=(r+8|0)>>2,Xv=Me[m];if((Xv+2|0)>>>0>Me[Er+3]>>>0){gr=197;break}var jv=Lv+Xv|0;oe=8236,Ae[jv]=255&oe,oe>>=8,Ae[jv+1]=255&oe;var Uv=Se[m]+2|0;Se[m]=Uv,gr=198;break}gr=197}while(0);197==gr&&Q(r,0|He.__str136,2);var xv=Se[Dv>>2];H(r,xv);break r}if(40==(0|Ir)){var y=(r+8|0)>>2,zv=Me[y],g=(r+12|0)>>2;if((zv+8|0)>>>0>Me[g]>>>0)Q(r,0|He.__str137,8);else{var Vv=Or+zv|0,le=0|Vv;oe=1919250543,Ae[le]=255&oe,oe>>=8,Ae[le+1]=255&oe,oe>>=8,Ae[le+2]=255&oe,oe>>=8,Ae[le+3]=255&oe;var be=Vv+4|0;oe=1919906913,Ae[be]=255&oe,oe>>=8,Ae[be+1]=255&oe,oe>>=8,Ae[be+2]=255&oe,oe>>=8,Ae[be+3]=255&oe;var Bv=Se[y]+8|0;Se[y]=Bv}var A=(a+4|0)>>2,Hv=(Ae[Se[Se[A]+4>>2]]-97&255&255)<26;e:do if(Hv){var Kv=Me[cr],Yv=0==(0|Kv);do if(!Yv){var Gv=Me[y];if(Gv>>>0>=Me[g]>>>0)break;Se[y]=Gv+1|0,Ae[Kv+Gv|0]=32;break e}while(0);Y(r,32)}while(0);var Wv=Me[cr],Zv=0==(0|Wv);do{if(!Zv){var Qv=Me[y],qv=Me[A],$v=Me[qv+8>>2];if(($v+Qv|0)>>>0>Me[g]>>>0){var Jv=qv,rt=$v;break}var at=Wv+Qv|0,et=Se[qv+4>>2];Pa(at,et,$v,1);var it=Se[y]+Se[Se[A]+8>>2]|0;Se[y]=it;break r}var vt=Me[A],Jv=vt,rt=Se[vt+8>>2]}while(0);var rt,Jv,tt=Se[Jv+4>>2];Q(r,tt,rt);break r}if(41==(0|Ir)){var E=(r+8|0)>>2,ft=Me[E];if((ft+9|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str10180,9);else{for(var _t=Or+ft|0,ie=0|He.__str10180,ve=_t,te=ie+9;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var st=Se[E]+9|0;Se[E]=st}var nt=Se[pr+2];H(r,nt);break r}if(42==(0|Ir)){var p=(r+8|0)>>2,ot=Me[p];if((ot+9|0)>>>0>Me[Er+3]>>>0)Q(r,0|He.__str10180,9);else{for(var lt=Or+ot|0,ie=0|He.__str10180,ve=lt,te=ie+9;ie<te;ie++,ve++)Ae[ve]=Ae[ie];var bt=Se[p]+9|0;Se[p]=bt}er(r,a);break r}if(43==(0|Ir)){var kt=a+4|0,ut=Se[kt>>2],ct=42==(0|Se[ut>>2]);e:do if(ct){var w=(r+8|0)>>2,ht=Me[w],dt=r+12|0;ht>>>0<Me[dt>>2]>>>0?(Se[w]=ht+1|0,Ae[Or+ht|0]=40):Y(r,40);var wt=Se[kt>>2];er(r,wt);var pt=Me[cr],Et=0==(0|pt);do if(!Et){var At=Me[w];if(At>>>0>=Me[dt>>2]>>>0)break;Se[w]=At+1|0,Ae[pt+At|0]=41;break e}while(0);Y(r,41)}else ir(r,ut);while(0);var gt=Me[cr],yt=0==(0|gt);do{if(!yt){var mt=r+8|0,St=Me[mt>>2];if(St>>>0>=Me[Er+3]>>>0){gr=232;break}Se[mt>>2]=St+1|0,Ae[gt+St|0]=40,gr=233;break}gr=232}while(0);232==gr&&Y(r,40);var Mt=Se[pr+2];H(r,Mt);var Ct=Me[cr],Rt=0==(0|Ct);do if(!Rt){var Tt=r+8|0,Ot=Me[Tt>>2];if(Ot>>>0>=Me[Er+3]>>>0)break;Se[Tt>>2]=Ot+1|0,Ae[Ct+Ot|0]=41;break r}while(0);Y(r,41);break r}if(44==(0|Ir)){var d=(a+8|0)>>2;if(45==(0|Se[Se[d]>>2])){var h=(a+4|0)>>2,Nt=Se[h],It=40==(0|Se[Nt>>2]);do if(It){var Pt=Se[Nt+4>>2];if(1!=(0|Se[Pt+8>>2]))break;if(Ae[Se[Pt+4>>2]]<<24>>24!=62)break;var Dt=r+8|0,Lt=Me[Dt>>2];Lt>>>0<Me[Er+3]>>>0?(Se[Dt>>2]=Lt+1|0,Ae[Or+Lt|0]=40):Y(r,40)}while(0);var Ft=Me[cr],Xt=0==(0|Ft);do{if(!Xt){var jt=r+8|0,Ut=Me[jt>>2];if(Ut>>>0>=Me[Er+3]>>>0){gr=248;break}Se[jt>>2]=Ut+1|0,Ae[Ft+Ut|0]=40,gr=249;break}gr=248}while(0);248==gr&&Y(r,40);var xt=Se[Se[d]+4>>2];H(r,xt);var zt=Me[cr],Vt=0==(0|zt);do{if(!Vt){var c=(r+8|0)>>2,Bt=Me[c];if((Bt+2|0)>>>0>Me[Er+3]>>>0){gr=252;break}var Ht=zt+Bt|0;oe=8233,Ae[Ht]=255&oe,oe>>=8,Ae[Ht+1]=255&oe;var Kt=Se[c]+2|0;Se[c]=Kt,gr=253;break}gr=252}while(0);252==gr&&Q(r,0|He.__str139,2);var Yt=Se[h];ir(r,Yt);var Gt=Me[cr],Wt=0==(0|Gt);do{if(!Wt){var u=(r+8|0)>>2,Zt=Me[u];if((Zt+2|0)>>>0>Me[Er+3]>>>0){gr=256;break}var Qt=Gt+Zt|0;oe=10272,Ae[Qt]=255&oe,oe>>=8,Ae[Qt+1]=255&oe;var qt=Se[u]+2|0;Se[u]=qt,gr=257;break}gr=256}while(0);256==gr&&Q(r,0|He.__str140,2);var $t=Se[Se[d]+8>>2];H(r,$t);var Jt=Me[cr],rf=0==(0|Jt);do{if(!rf){var af=r+8|0,ef=Me[af>>2];if(ef>>>0>=Me[Er+3]>>>0){gr=260;break}Se[af>>2]=ef+1|0,Ae[Jt+ef|0]=41,gr=261;break}gr=260}while(0);260==gr&&Y(r,41);var vf=Se[h];if(40!=(0|Se[vf>>2]))break r;var tf=Se[vf+4>>2];if(1!=(0|Se[tf+8>>2]))break r;if(Ae[Se[tf+4>>2]]<<24>>24!=62)break r;var ff=Me[cr],_f=0==(0|ff);do if(!_f){var sf=r+8|0,nf=Me[sf>>2];if(nf>>>0>=Me[Er+3]>>>0)break;Se[sf>>2]=nf+1|0,Ae[ff+nf|0]=41;break r}while(0);Y(r,41);break r}Z(r);break r}if(45==(0|Ir)){Z(r);break r}if(46==(0|Ir)){var of=a+4|0,k=(a+8|0)>>2,lf=Se[k],bf=47==(0|Se[lf>>2]);do if(bf){if(48!=(0|Se[Se[lf+8>>2]>>2]))break;var b=(r+8|0)>>2,kf=Me[b],l=(r+12|0)>>2;kf>>>0<Me[l]>>>0?(Se[b]=kf+1|0,Ae[Or+kf|0]=40):Y(r,40);var uf=Se[Se[k]+4>>2];H(r,uf);var cf=Me[cr],hf=0==(0|cf);do{if(!hf){var df=Me[b];if((df+2|0)>>>0>Me[l]>>>0){gr=278;break}var wf=cf+df|0;oe=8233,Ae[wf]=255&oe,oe>>=8,Ae[wf+1]=255&oe;var pf=Se[b]+2|0;Se[b]=pf,gr=279;break}gr=278}while(0);278==gr&&Q(r,0|He.__str139,2);var Ef=Se[of>>2];ir(r,Ef);var Af=Me[cr],gf=0==(0|Af);do{if(!gf){var yf=Me[b];if((yf+2|0)>>>0>Me[l]>>>0){gr=282;break}var mf=Af+yf|0;oe=10272,Ae[mf]=255&oe,oe>>=8,Ae[mf+1]=255&oe;var Sf=Se[b]+2|0;Se[b]=Sf,gr=283;break}gr=282}while(0);282==gr&&Q(r,0|He.__str140,2);var Mf=Se[Se[Se[k]+8>>2]+4>>2];H(r,Mf);var Cf=Me[cr],Rf=0==(0|Cf);do{if(!Rf){var Tf=Me[b];if((Tf+5|0)>>>0>Me[l]>>>0){gr=286;break}var Of=Cf+Tf|0;Ae[Of]=Ae[0|He.__str141],Ae[Of+1]=Ae[(0|He.__str141)+1],Ae[Of+2]=Ae[(0|He.__str141)+2],Ae[Of+3]=Ae[(0|He.__str141)+3],Ae[Of+4]=Ae[(0|He.__str141)+4];var Nf=Se[b]+5|0;Se[b]=Nf,gr=287;break}gr=286}while(0);286==gr&&Q(r,0|He.__str141,5);var If=Se[Se[Se[k]+8>>2]+8>>2];H(r,If);var Pf=Me[cr],Df=0==(0|Pf);do if(!Df){var Lf=Me[b];if(Lf>>>0>=Me[l]>>>0)break;Se[b]=Lf+1|0,Ae[Pf+Lf|0]=41;break r}while(0);Y(r,41);break r}while(0);Z(r);break r}if(47==(0|Ir)||48==(0|Ir)){Z(r);break r}if(49==(0|Ir)||50==(0|Ir)){var Ff=a+4|0,Xf=Se[Ff>>2],jf=33==(0|Se[Xf>>2]);do{if(jf){var Uf=Me[Se[Xf+4>>2]+16>>2];if(1==(0|Uf)||2==(0|Uf)||3==(0|Uf)||4==(0|Uf)||5==(0|Uf)||6==(0|Uf)){var xf=a+8|0;if(0!=(0|Se[Se[xf>>2]>>2])){var zf=Uf;break}if(50==(0|Ir)){var Vf=r+8|0,Bf=Me[Vf>>2];Bf>>>0<Me[Er+3]>>>0?(Se[Vf>>2]=Bf+1|0,Ae[Or+Bf|0]=45):Y(r,45)}var Hf=Se[xf>>2];if(H(r,Hf),2==(0|Uf)){var Kf=Me[cr],Yf=0==(0|Kf);do if(!Yf){var Gf=r+8|0,Wf=Me[Gf>>2];if(Wf>>>0>=Me[Er+3]>>>0)break;Se[Gf>>2]=Wf+1|0,Ae[Kf+Wf|0]=117;break r}while(0);Y(r,117);break r}if(3==(0|Uf)){var Zf=Me[cr],Qf=0==(0|Zf);do if(!Qf){var qf=r+8|0,$f=Me[qf>>2];if($f>>>0>=Me[Er+3]>>>0)break;Se[qf>>2]=$f+1|0,Ae[Zf+$f|0]=108;break r}while(0);Y(r,108);break r}if(4==(0|Uf)){var Jf=Me[cr],r_=0==(0|Jf);do if(!r_){var o=(r+8|0)>>2,a_=Me[o];if((a_+2|0)>>>0>Me[Er+3]>>>0)break;var e_=Jf+a_|0;oe=27765,Ae[e_]=255&oe,oe>>=8,Ae[e_+1]=255&oe;var i_=Se[o]+2|0;Se[o]=i_;break r}while(0);Q(r,0|He.__str142,2);break r}if(5==(0|Uf)){var v_=Me[cr],t_=0==(0|v_);do if(!t_){var n=(r+8|0)>>2,f_=Me[n];if((f_+2|0)>>>0>Me[Er+3]>>>0)break;var __=v_+f_|0;oe=27756,Ae[__]=255&oe,oe>>=8,Ae[__+1]=255&oe;var s_=Se[n]+2|0;Se[n]=s_;break r}while(0);Q(r,0|He.__str143,2);break r}if(6==(0|Uf)){var n_=Me[cr],o_=0==(0|n_);do if(!o_){var s=(r+8|0)>>2,l_=Me[s];if((l_+3|0)>>>0>Me[Er+3]>>>0)break;var b_=n_+l_|0;Ae[b_]=Ae[0|He.__str144],Ae[b_+1]=Ae[(0|He.__str144)+1],Ae[b_+2]=Ae[(0|He.__str144)+2];var k_=Se[s]+3|0;Se[s]=k_;break r}while(0);Q(r,0|He.__str144,3);break r}break r}if(7==(0|Uf)){var _=Se[pr+2]>>2;if(0!=(0|Se[_])){var zf=7;break}if(!(1==(0|Se[_+2])&49==(0|Ir))){var zf=Uf;break}var u_=Ae[Se[_+1]]<<24>>24;if(48==(0|u_)){var f=(r+8|0)>>2,c_=Me[f];if((c_+5|0)>>>0>Me[Er+3]>>>0){Q(r,0|He.__str145,5);break r}var h_=Or+c_|0;Ae[h_]=Ae[0|He.__str145],Ae[h_+1]=Ae[(0|He.__str145)+1],Ae[h_+2]=Ae[(0|He.__str145)+2],Ae[h_+3]=Ae[(0|He.__str145)+3],Ae[h_+4]=Ae[(0|He.__str145)+4];var d_=Se[f]+5|0;Se[f]=d_;break r}if(49==(0|u_)){var t=(r+8|0)>>2,w_=Me[t];if((w_+4|0)>>>0>Me[Er+3]>>>0){Q(r,0|He.__str146,4);break r}var p_=Or+w_|0;oe=1702195828,Ae[p_]=255&oe,oe>>=8,Ae[p_+1]=255&oe,oe>>=8,Ae[p_+2]=255&oe,oe>>=8,Ae[p_+3]=255&oe;var E_=Se[t]+4|0;Se[t]=E_;break r}var zf=Uf;break}var zf=Uf;break}var zf=0}while(0);var zf,v=(r+8|0)>>2,A_=Me[v],i=(r+12|0)>>2;A_>>>0<Me[i]>>>0?(Se[v]=A_+1|0,Ae[Or+A_|0]=40):Y(r,40);var g_=Se[Ff>>2];H(r,g_);var y_=Me[cr],m_=0==(0|y_);do{if(!m_){var S_=Me[v];if(S_>>>0>=Me[i]>>>0){gr=335;break}Se[v]=S_+1|0,Ae[y_+S_|0]=41,gr=336;break}gr=335}while(0);335==gr&&Y(r,41);var M_=50==(0|Se[Nr>>2]);e:do if(M_){var C_=Me[cr],R_=0==(0|C_);do if(!R_){var T_=Me[v];if(T_>>>0>=Me[i]>>>0)break;Se[v]=T_+1|0,Ae[C_+T_|0]=45;break e}while(0);Y(r,45)}while(0);if(8==(0|zf)){var O_=Me[cr],N_=0==(0|O_);do{if(!N_){var I_=Me[v];if(I_>>>0>=Me[i]>>>0){gr=345;break}Se[v]=I_+1|0,Ae[O_+I_|0]=91,gr=346;break}gr=345}while(0);345==gr&&Y(r,91);var P_=Se[pr+2];H(r,P_);var D_=Me[cr],L_=0==(0|D_);do if(!L_){var F_=Me[v];if(F_>>>0>=Me[i]>>>0)break;Se[v]=F_+1|0,Ae[D_+F_|0]=93;break r}while(0);Y(r,93);break r}var X_=Se[pr+2];H(r,X_);break r}Z(r);break r}}while(0);var e=(r+20|0)>>2,j_=Se[e],U_=0|Sr;Se[U_>>2]=j_,Se[e]=Sr,Se[Sr+4>>2]=a;var x_=Sr+8|0;Se[x_>>2]=0;var z_=Se[Er+4];Se[Sr+12>>2]=z_;var V_=Se[pr+1];H(r,V_),0==(0|Se[x_>>2])&&$(r,a);var B_=Se[U_>>2];Se[e]=B_}while(0);Oe=Ar}function K(r,a,e,i){var v=i>>2;Se[v]=r,Se[v+1]=r+e|0,Se[v+2]=a,Se[v+3]=r,Se[v+6]=e<<1,Se[v+5]=0,Se[v+9]=e,Se[v+8]=0,Se[v+10]=0,Se[v+11]=0,Se[v+12]=0}function Y(r,a){var e,i=r+4|0,v=Me[i>>2],t=0==(0|v);do if(!t){var e=(r+8|0)>>2,f=Me[e];if(f>>>0<Me[r+12>>2]>>>0)var _=v,s=f;else{tr(r,1);var n=Me[i>>2];if(0==(0|n))break;var _=n,s=Se[e]}var s,_;Ae[_+s|0]=255&a;var o=Se[e]+1|0;Se[e]=o}while(0)}function G(r,a,e,i){var v,t=i>>2,f=Oe;Oe+=4;var _=f,v=_>>2,s=0==(0|r);do if(s){if(0==(0|i)){var n=0;break}Se[t]=-3;var n=0}else{var o=0==(0|e);if(0!=(0|a)&o){if(0==(0|i)){var n=0;break}Se[t]=-3;var n=0}else{var l=W(r,_);if(0==(0|l)){if(0==(0|i)){var n=0;break}if(1==(0|Se[v])){Se[t]=-1;var n=0}else{Se[t]=-2;var n=0}}else{var b=0==(0|a);do if(b){if(o){var k=l;break}var u=Se[v];Se[e>>2]=u;var k=l}else{var c=Ca(l);if(c>>>0<Me[e>>2]>>>0){Ra(a,l);va(l);var k=a}else{va(a);var h=Se[v];Se[e>>2]=h;var k=l}}while(0);var k;if(0==(0|i)){var n=k;break}Se[t]=0;var n=k}}}while(0);var n;return Oe=f,n}function W(r,a){var e,i=Oe;Oe+=52;var v,t=i,e=t>>2;Se[a>>2]=0;var f=Ca(r),_=Ae[r]<<24>>24==95;do{if(_){if(Ae[r+1|0]<<24>>24==90){var s=0;v=13;break}v=3;break}v=3}while(0);do if(3==v){var n=Na(r,0|He.__str117,8);if(0!=(0|n)){var s=1;v=13;break}var o=Ae[r+8|0];if(o<<24>>24!=46&&o<<24>>24!=95&&o<<24>>24!=36){var s=1;v=13;break}var l=r+9|0,b=Ae[l];if(b<<24>>24!=68&&b<<24>>24!=73){\nvar s=1;v=13;break}if(Ae[r+10|0]<<24>>24!=95){var s=1;v=13;break}var k=f+29|0,u=Jr(k);if(0==(0|u)){Se[a>>2]=1;var c=0;v=19;break}Ae[l]<<24>>24==73?Pa(u,0|He.__str118,30,1):Pa(u,0|He.__str119,29,1);var h=r+11|0,c=(Ia(u,h),u);v=19;break}while(0);if(13==v){var s;K(r,17,f,t);var d=Se[e+6],w=Ta(),p=Oe;Oe+=12*d,Oe=Oe+3>>2<<2;var E=Oe;if(Oe+=4*Se[e+9],Oe=Oe+3>>2<<2,Se[e+4]=p,Se[e+7]=E,s)var A=N(t),g=A;else var y=T(t,1),g=y;var g,m=Ae[Se[e+3]]<<24>>24==0?g:0,S=Se[e+12]+f+10*Se[e+10]|0;if(0==(0|m))var M=0;else var C=S/8+S|0,R=B(17,m,C,a),M=R;var M;Oa(w);var c=M}var c;return Oe=i,c}function Z(r){var a=r+4|0,e=Se[a>>2];va(e),Se[a>>2]=0}function Q(r,a,e){var i,v=r+4|0,t=Me[v>>2],f=0==(0|t);do if(!f){var i=(r+8|0)>>2,_=Me[i];if((_+e|0)>>>0>Me[r+12>>2]>>>0){tr(r,e);var s=Me[v>>2];if(0==(0|s))break;var n=s,o=Se[i]}else var n=t,o=_;var o,n;Pa(n+o|0,a,e,1);var l=Se[i]+e|0;Se[i]=l}while(0)}function q(r,a,e){var i,v,t=a+e|0,f=(0|e)>0;r:do if(f)for(var _=t,s=r+4|0,i=(r+8|0)>>2,n=r+12|0,o=a;;){var o,l=(_-o|0)>3;a:do{if(l){if(Ae[o]<<24>>24!=95){v=21;break}if(Ae[o+1|0]<<24>>24!=95){v=21;break}if(Ae[o+2|0]<<24>>24!=85){v=21;break}for(var b=o+3|0,k=0;;){var k,b;if(b>>>0>=t>>>0){v=21;break a}var u=ge[b],c=u<<24>>24;if((u-48&255&255)<10)var h=c-48|0;else if((u-65&255&255)<6)var h=c-55|0;else{if((u-97&255&255)>=6)break;var h=c-87|0}var h,b=b+1|0,k=(k<<4)+h|0}if(!(u<<24>>24==95&k>>>0<256)){v=21;break}var d=Me[s>>2],w=0==(0|d);do if(!w){var p=Me[i];if(p>>>0>=Me[n>>2]>>>0)break;Se[i]=p+1|0,Ae[d+p|0]=255&k;var E=b;v=25;break a}while(0);Y(r,k);var E=b;v=25;break}v=21}while(0);a:do if(21==v){var A=Me[s>>2],g=0==(0|A);do if(!g){var y=Me[i];if(y>>>0>=Me[n>>2]>>>0)break;var m=Ae[o];Se[i]=y+1|0,Ae[A+y|0]=m;var E=o;break a}while(0);var S=Ae[o]<<24>>24;Y(r,S);var E=o}while(0);var E,M=E+1|0;if(M>>>0>=t>>>0)break r;var o=M}while(0)}function $(r,a){var e,i,v,t,f,_,s,n=r>>2,o=Se[a>>2];r:do if(22==(0|o)||25==(0|o)){var l=Me[n+1],b=0==(0|l);do if(!b){var _=(r+8|0)>>2,k=Me[_];if((k+9|0)>>>0>Me[n+3]>>>0)break;for(var u=l+k|0,c=0|He.__str147,h=u,d=c+9;c<d;c++,h++)Ae[h]=Ae[c];var w=Se[_]+9|0;Se[_]=w;break r}while(0);Q(r,0|He.__str147,9)}else if(23==(0|o)||26==(0|o)){var p=Me[n+1],E=0==(0|p);do if(!E){var f=(r+8|0)>>2,A=Me[f];if((A+9|0)>>>0>Me[n+3]>>>0)break;for(var g=p+A|0,c=0|He.__str148,h=g,d=c+9;c<d;c++,h++)Ae[h]=Ae[c];var y=Se[f]+9|0;Se[f]=y;break r}while(0);Q(r,0|He.__str148,9)}else if(24==(0|o)||27==(0|o)){var m=Me[n+1],S=0==(0|m);do if(!S){var t=(r+8|0)>>2,M=Me[t];if((M+6|0)>>>0>Me[n+3]>>>0)break;var C=m+M|0;Ae[C]=Ae[0|He.__str149],Ae[C+1]=Ae[(0|He.__str149)+1],Ae[C+2]=Ae[(0|He.__str149)+2],Ae[C+3]=Ae[(0|He.__str149)+3],Ae[C+4]=Ae[(0|He.__str149)+4],Ae[C+5]=Ae[(0|He.__str149)+5];var R=Se[t]+6|0;Se[t]=R;break r}while(0);Q(r,0|He.__str149,6)}else if(28==(0|o)){var T=Me[n+1],O=0==(0|T);do{if(!O){var N=r+8|0,I=Me[N>>2];if(I>>>0>=Me[n+3]>>>0){s=17;break}Se[N>>2]=I+1|0,Ae[T+I|0]=32,s=18;break}s=17}while(0);17==s&&Y(r,32);var P=Se[a+8>>2];H(r,P)}else if(29==(0|o)){if(0!=(4&Se[n]|0))break;var D=Me[n+1],L=0==(0|D);do if(!L){var F=r+8|0,X=Me[F>>2];if(X>>>0>=Me[n+3]>>>0)break;Se[F>>2]=X+1|0,Ae[D+X|0]=42;break r}while(0);Y(r,42)}else if(30==(0|o)){var j=Me[n+1],U=0==(0|j);do if(!U){var x=r+8|0,z=Me[x>>2];if(z>>>0>=Me[n+3]>>>0)break;Se[x>>2]=z+1|0,Ae[j+z|0]=38;break r}while(0);Y(r,38)}else if(31==(0|o)){var V=Me[n+1],B=0==(0|V);do if(!B){var v=(r+8|0)>>2,K=Me[v];if((K+8|0)>>>0>Me[n+3]>>>0)break;var G=V+K|0,W=0|G;oe=1886220131,Ae[W]=255&oe,oe>>=8,Ae[W+1]=255&oe,oe>>=8,Ae[W+2]=255&oe,oe>>=8,Ae[W+3]=255&oe;var Z=G+4|0;oe=544761196,Ae[Z]=255&oe,oe>>=8,Ae[Z+1]=255&oe,oe>>=8,Ae[Z+2]=255&oe,oe>>=8,Ae[Z+3]=255&oe;var q=Se[v]+8|0;Se[v]=q;break r}while(0);Q(r,0|He.__str150,8)}else if(32==(0|o)){var $=Me[n+1],J=0==(0|$);do if(!J){var i=(r+8|0)>>2,rr=Me[i];if((rr+10|0)>>>0>Me[n+3]>>>0)break;for(var ar=$+rr|0,c=0|He.__str151,h=ar,d=c+10;c<d;c++,h++)Ae[h]=Ae[c];var er=Se[i]+10|0;Se[i]=er;break r}while(0);Q(r,0|He.__str151,10)}else if(37==(0|o)){var ir=r+4|0,vr=Me[ir>>2],tr=0==(0|vr);do{if(!tr){var fr=r+8|0,_r=Me[fr>>2];if(0!=(0|_r)&&Ae[vr+(_r-1)|0]<<24>>24==40){s=42;break}if(_r>>>0>=Me[n+3]>>>0){s=41;break}Se[fr>>2]=_r+1|0,Ae[vr+_r|0]=32,s=42;break}s=41}while(0);41==s&&Y(r,32);var sr=Se[a+4>>2];H(r,sr);var nr=Me[ir>>2],or=0==(0|nr);do if(!or){var e=(r+8|0)>>2,lr=Me[e];if((lr+3|0)>>>0>Me[n+3]>>>0)break;var br=nr+lr|0;Ae[br]=Ae[0|He.__str135],Ae[br+1]=Ae[(0|He.__str135)+1],Ae[br+2]=Ae[(0|He.__str135)+2];var kr=Se[e]+3|0;Se[e]=kr;break r}while(0);Q(r,0|He.__str135,3)}else if(3==(0|o)){var ur=Se[a+4>>2];H(r,ur)}else H(r,a);while(0)}function J(r){var a=r+20|0,e=Se[a>>2];if((0|e)<(0|Se[r+24>>2])){var i=Se[r+16>>2]+12*e|0,v=e+1|0;Se[a>>2]=v;var t=i}else var t=0;var t;return t}function rr(r,a,e){var i,v,t,f,_=r>>2,s=e,t=s>>2,n=0;r:for(;;){var n,s,o=0==(0|s);do if(!o){if(0!=(0|Se[t+2]))break;var l=Se[Se[t+1]>>2];if(29==(0|l)||30==(0|l)){f=9;break r}if(22==(0|l)||23==(0|l)||24==(0|l)||28==(0|l)||31==(0|l)||32==(0|l)||37==(0|l)){var b=Se[_+1];f=12;break r}var s=Se[t],t=s>>2,n=1;continue r}while(0);if(0!=(0|Se[a+4>>2])&0==(0|n)){f=9;break}var k=0,u=r+4|0,v=u>>2;f=22;break}do if(9==f){var c=Se[_+1];if(0==(0|c)){f=17;break}var h=Se[_+2];if(0==(0|h)){var d=c;f=13;break}var w=Ae[c+(h-1)|0];if(w<<24>>24==40||w<<24>>24==42){f=18;break}var b=c;f=12;break}while(0);do if(12==f){var b;if(0==(0|b)){f=17;break}var d=b;f=13;break}while(0);do if(13==f){var d,p=r+8|0,E=Me[p>>2];if(0!=(0|E)&&Ae[d+(E-1)|0]<<24>>24==32){f=18;break}if(E>>>0>=Me[_+3]>>>0){f=17;break}Se[p>>2]=E+1|0,Ae[d+E|0]=32,f=18;break}while(0);do if(17==f){Y(r,32),f=18;break}while(0);r:do if(18==f){var A=r+4|0,g=Me[A>>2],y=0==(0|g);do if(!y){var m=r+8|0,S=Me[m>>2];if(S>>>0>=Me[_+3]>>>0)break;Se[m>>2]=S+1|0,Ae[g+S|0]=40;var k=1,u=A,v=u>>2;break r}while(0);Y(r,40);var k=1,u=A,v=u>>2}while(0);var u,k,i=(r+20|0)>>2,M=Se[i];Se[i]=0,vr(r,e,0);r:do if(k){var C=Me[v],R=0==(0|C);do if(!R){var T=r+8|0,O=Me[T>>2];if(O>>>0>=Me[_+3]>>>0)break;Se[T>>2]=O+1|0,Ae[C+O|0]=41;break r}while(0);Y(r,41)}while(0);var N=Me[v],I=0==(0|N);do{if(!I){var P=r+8|0,D=Me[P>>2];if(D>>>0>=Me[_+3]>>>0){f=30;break}Se[P>>2]=D+1|0,Ae[N+D|0]=40,f=31;break}f=30}while(0);30==f&&Y(r,40);var L=Se[a+8>>2];0!=(0|L)&&H(r,L);var F=Me[v],X=0==(0|F);do{if(!X){var j=r+8|0,U=Me[j>>2];if(U>>>0>=Me[_+3]>>>0){f=36;break}Se[j>>2]=U+1|0,Ae[F+U|0]=41,f=37;break}f=36}while(0);36==f&&Y(r,41),vr(r,e,1),Se[i]=M}function ar(r,a,e){var i,v,t,f=r>>2,_=0==(0|e);do{if(!_){var s=e,v=s>>2;r:for(;;){var s;if(0==(0|s)){var n=1;t=14;break}if(0==(0|Se[v+2])){var o=36==(0|Se[Se[v+1]>>2]),l=1&o^1;if(o){var n=l;t=14;break}var b=r+4|0,k=Me[b>>2],u=0==(0|k);do{if(!u){var i=(r+8|0)>>2,c=Me[i];if((c+2|0)>>>0>Me[f+3]>>>0){t=9;break}var h=k+c|0;oe=10272,Ae[h]=255&oe,oe>>=8,Ae[h+1]=255&oe;var d=Se[i]+2|0;Se[i]=d,vr(r,e,0),t=10;break}t=9}while(0);9==t&&(Q(r,0|He.__str140,2),vr(r,e,0));var w=Me[b>>2],p=0==(0|w);do if(!p){var E=r+8|0,A=Me[E>>2];if(A>>>0>=Me[f+3]>>>0)break;Se[E>>2]=A+1|0,Ae[w+A|0]=41;var g=l;t=15;break r}while(0);Y(r,41);var g=l;t=15;break}var s=Se[v],v=s>>2}if(14==t){var n;vr(r,e,0);var g=n}var g;if(0!=(0|g)){t=17;break}var y=r+4|0;t=21;break}t=17}while(0);r:do if(17==t){var m=r+4|0,S=Me[m>>2],M=0==(0|S);do if(!M){var C=r+8|0,R=Me[C>>2];if(R>>>0>=Me[f+3]>>>0)break;Se[C>>2]=R+1|0,Ae[S+R|0]=32;var y=m;break r}while(0);Y(r,32);var y=m}while(0);var y,T=Me[y>>2],O=0==(0|T);do{if(!O){var N=r+8|0,I=Me[N>>2];if(I>>>0>=Me[f+3]>>>0){t=24;break}Se[N>>2]=I+1|0,Ae[T+I|0]=91,t=25;break}t=24}while(0);24==t&&Y(r,91);var P=Se[a+4>>2];0!=(0|P)&&H(r,P);var D=Me[y>>2],L=0==(0|D);do{if(!L){var F=r+8|0,X=Me[F>>2];if(X>>>0>=Me[f+3]>>>0){t=30;break}Se[F>>2]=X+1|0,Ae[D+X|0]=93,t=31;break}t=30}while(0);30==t&&Y(r,93)}function er(r,a){var e,i,v,t,f,_,s=Oe;Oe+=8;var n,o=s,_=(a+4|0)>>2,l=Se[_];if(4==(0|Se[l>>2])){var f=(r+20|0)>>2,b=Se[f];Se[f]=0;var t=(r+16|0)>>2,k=Se[t],u=0|o;Se[u>>2]=k,Se[t]=o;var c=Se[_];Se[o+4>>2]=c;var h=Se[c+4>>2];H(r,h);var d=Se[u>>2];Se[t]=d;var v=(r+4|0)>>2,w=Me[v],p=0==(0|w);do{if(!p){var i=(r+8|0)>>2,E=Me[i],A=0==(0|E);do if(!A){if(Ae[w+(E-1)|0]<<24>>24!=60)break;E>>>0<Me[r+12>>2]>>>0?(Se[i]=E+1|0,Ae[w+E|0]=32):Y(r,32)}while(0);var g=Me[v];if(0==(0|g)){n=12;break}var y=Me[i];if(y>>>0>=Me[r+12>>2]>>>0){n=12;break}Se[i]=y+1|0,Ae[g+y|0]=60,n=13;break}n=12}while(0);12==n&&Y(r,60);var m=Se[Se[_]+8>>2];H(r,m);var S=Me[v],M=0==(0|S);do{if(!M){var e=(r+8|0)>>2,C=Me[e],R=0==(0|C);do if(!R){if(Ae[S+(C-1)|0]<<24>>24!=62)break;C>>>0<Me[r+12>>2]>>>0?(Se[e]=C+1|0,Ae[S+C|0]=32):Y(r,32)}while(0);var T=Me[v];if(0==(0|T)){n=22;break}var O=Me[e];if(O>>>0>=Me[r+12>>2]>>>0){n=22;break}Se[e]=O+1|0,Ae[T+O|0]=62,n=23;break}n=22}while(0);22==n&&Y(r,62),Se[f]=b}else H(r,l);Oe=s}function ir(r,a){var e,i=40==(0|Se[a>>2]);r:do if(i){var v=Me[r+4>>2],t=0==(0|v);do{if(!t){var e=(r+8|0)>>2,f=Me[e],_=a+4|0,s=Me[_>>2],n=Me[s+8>>2];if((n+f|0)>>>0>Me[r+12>>2]>>>0){var o=s,l=n;break}var b=v+f|0,k=Se[s+4>>2];Pa(b,k,n,1);var u=Se[e]+Se[Se[_>>2]+8>>2]|0;Se[e]=u;break r}var c=Me[a+4>>2],o=c,l=Se[c+8>>2]}while(0);var l,o,h=Se[o+4>>2];Q(r,h,l)}else H(r,a);while(0)}function vr(r,a,e){var i,v,t,f,_,f=(r+4|0)>>2,s=0==(0|e),t=(r+16|0)>>2;r:do if(s)for(var n=a;;){var n;if(0==(0|n)){_=29;break r}if(0==(0|Se[f])){_=29;break r}var o=n+8|0,l=0==(0|Se[o>>2]);do if(l){var b=n+4|0;if((Se[Se[b>>2]>>2]-25|0)>>>0<3)break;Se[o>>2]=1;var k=Me[t],u=Se[n+12>>2];Se[t]=u;var c=Me[b>>2],h=Se[c>>2];if(35==(0|h)){var d=n,w=k,p=c;_=14;break r}if(36==(0|h)){var E=n,A=k,g=c;_=15;break r}if(2==(0|h)){var y=k,m=b;_=16;break r}$(r,c),Se[t]=k}while(0);var n=Se[n>>2]}else for(var S=a;;){var S;if(0==(0|S)){_=29;break r}if(0==(0|Se[f])){_=29;break r}var M=S+8|0;if(0==(0|Se[M>>2])){Se[M>>2]=1;var C=Me[t],R=Se[S+12>>2];Se[t]=R;var T=S+4|0,O=Me[T>>2],N=Se[O>>2];if(35==(0|N)){var d=S,w=C,p=O;_=14;break r}if(36==(0|N)){var E=S,A=C,g=O;_=15;break r}if(2==(0|N)){var y=C,m=T;_=16;break r}$(r,O),Se[t]=C}var S=Se[S>>2]}while(0);if(14==_){var p,w,d,I=Se[d>>2];rr(r,p,I),Se[t]=w}else if(15==_){var g,A,E,P=Se[E>>2];ar(r,g,P),Se[t]=A}else if(16==_){var m,y,v=(r+20|0)>>2,D=Se[v];Se[v]=0;var L=Se[Se[m>>2]+4>>2];H(r,L),Se[v]=D;var F=0==(4&Se[r>>2]|0),X=Me[f],j=0!=(0|X);r:do if(F){do if(j){var i=(r+8|0)>>2,U=Me[i];if((U+2|0)>>>0>Me[r+12>>2]>>>0)break;var x=X+U|0;oe=14906,Ae[x]=255&oe,oe>>=8,Ae[x+1]=255&oe;var z=Se[i]+2|0;Se[i]=z;break r}while(0);Q(r,0|He.__str120,2)}else{do if(j){var V=r+8|0,B=Me[V>>2];if(B>>>0>=Me[r+12>>2]>>>0)break;Se[V>>2]=B+1|0,Ae[X+B|0]=46;break r}while(0);Y(r,46)}while(0);var K=Me[Se[m>>2]+8>>2],G=(Se[K>>2]-25|0)>>>0<3;r:do if(G)for(var W=K;;){var W,Z=Me[W+4>>2];if((Se[Z>>2]-25|0)>>>0>=3){var q=Z;break r}var W=Z}else var q=K;while(0);var q;H(r,q),Se[t]=y}}function tr(r,a){var e,e=(r+4|0)>>2,i=Se[e],v=0==(0|i);r:do if(!v){for(var t=Se[r+8>>2]+a|0,f=r+12|0,_=Se[f>>2],s=i;;){var s,_;if(t>>>0<=_>>>0)break r;var n=_<<1,o=fa(s,n);if(0==(0|o))break;Se[e]=o,Se[f>>2]=n;var _=n,s=o}var l=Se[e];va(l),Se[e]=0,Se[r+24>>2]=1}while(0)}function fr(r,a,e){var i,v=J(r),i=v>>2;return 0!=(0|v)&&(Se[i]=21,Se[i+1]=a,Se[i+2]=e),v}function _r(r){var a,a=(r+12|0)>>2,e=Se[a],i=Ae[e]<<24>>24;if(88==(0|i)){var v=e+1|0;Se[a]=v;var t=nr(r),f=Se[a],_=f+1|0;Se[a]=_;var s=Ae[f]<<24>>24==69?t:0,n=s}else if(76==(0|i))var o=or(r),n=o;else var l=N(r),n=l;var n;return n}function sr(r){var a,a=(r+12|0)>>2,e=Se[a],i=Ae[e];if(i<<24>>24==110){var v=e+1|0;Se[a]=v;var t=1,f=Ae[v],_=v}else var t=0,f=i,_=e;var _,f,t,s=(f-48&255&255)<10;r:do if(s)for(var n=f,o=0,l=_;;){var l,o,n,b=(n<<24>>24)-48+10*o|0,k=l+1|0;Se[a]=k;var u=ge[k];if((u-48&255&255)>=10){var c=b;break r}var n=u,o=b,l=k}else var c=0;while(0);var c,h=0==(0|t)?c:0|-c;return h}function nr(r){var a,e,a=(r+12|0)>>2,i=Se[a],v=Ae[i];do{if(v<<24>>24==76){var t=or(r),f=t;e=21;break}if(v<<24>>24==84){var _=x(r),f=_;e=21;break}if(v<<24>>24==115){if(Ae[i+1|0]<<24>>24!=114){e=8;break}var s=i+2|0;Se[a]=s;var n=N(r),o=br(r);if(Ae[Se[a]]<<24>>24==73){var l=z(r),b=D(r,4,o,l),k=D(r,1,n,b),f=k;e=21;break}var u=D(r,1,n,o),f=u;e=21;break}e=8}while(0);r:do if(8==e){var c=kr(r);if(0==(0|c)){var f=0;break}var h=0|c,d=Se[h>>2],w=40==(0|d);do{if(w){var p=c+4|0,E=r+48|0,A=Se[Se[p>>2]+8>>2]-2+Se[E>>2]|0;Se[E>>2]=A;var g=Se[h>>2];if(40!=(0|g)){var y=g;e=13;break}var m=Se[p>>2],S=Se[m>>2],M=Da(S,0|He.__str90);if(0!=(0|M)){var C=m;e=15;break}var R=N(r),T=D(r,43,c,R),f=T;break r}var y=d;e=13}while(0);do if(13==e){var y;if(40==(0|y)){var C=Se[c+4>>2];e=15;break}if(41==(0|y)){var O=c+4|0;e=17;break}if(42==(0|y)){e=18;break}var f=0;break r}while(0);do if(15==e){var C,O=C+12|0;e=17;break}while(0);do if(17==e){var O,I=Se[O>>2];if(1==(0|I))break;if(2==(0|I)){var P=nr(r),L=nr(r),F=D(r,45,P,L),X=D(r,44,c,F);return X}if(3==(0|I)){var j=nr(r),U=nr(r),V=nr(r),B=D(r,48,U,V),H=D(r,47,j,B),K=D(r,46,c,H);return K}var f=0;break r}while(0);var Y=nr(r),G=D(r,43,c,Y);return G}while(0);var f;return f}function or(r){var a,a=(r+12|0)>>2,e=Se[a],i=e+1|0;Se[a]=i;var v=Ae[e]<<24>>24==76;r:do if(v){if(Ae[i]<<24>>24==95)var t=T(r,0),f=t;else{var _=N(r);if(0==(0|_)){var s=0;break}var n=33==(0|Se[_>>2]);do if(n){var o=Se[_+4>>2];if(0==(0|Se[o+16>>2]))break;var l=r+48|0,b=Se[l>>2]-Se[o+4>>2]|0;Se[l>>2]=b}while(0);var k=Se[a];if(Ae[k]<<24>>24==110){var u=k+1|0;Se[a]=u;var c=50,h=u}else var c=49,h=k;for(var h,c,d=h;;){var d,w=Ae[d];if(w<<24>>24==69)break;if(w<<24>>24==0){var s=0;break r}var p=d+1|0;Se[a]=p;var d=p}var E=lr(r,h,d-h|0),A=D(r,c,_,E),f=A}var f,g=Se[a],y=g+1|0;Se[a]=y;var m=Ae[g]<<24>>24==69?f:0,s=m}else var s=0;while(0);var s;return s}function lr(r,a,e){var i=J(r),v=m(i,a,e),t=0==(0|v)?0:i;return t}function br(r){var a=r+12|0,e=Me[a>>2],i=ge[e],v=(i-48&255&255)<10;do if(v)var t=L(r),f=t;else if((i-97&255&255)<26){var _=kr(r);if(0==(0|_)){var f=0;break}if(40!=(0|Se[_>>2])){var f=_;break}var s=r+48|0,n=Se[Se[_+4>>2]+8>>2]+Se[s>>2]+7|0;Se[s>>2]=n;var f=_}else if(i<<24>>24==67||i<<24>>24==68)var o=hr(r),f=o;else{if(i<<24>>24!=76){var f=0;break}Se[a>>2]=e+1|0;var l=L(r);if(0==(0|l)){var f=0;break}var b=dr(r),k=0==(0|b)?0:l,f=k}while(0);var f;return f}function kr(r){var a,e,a=(r+12|0)>>2,i=Se[a],v=i+1|0;Se[a]=v;var t=ge[i],f=i+2|0;Se[a]=f;var _=ge[v];do{if(t<<24>>24==118){if((_-48&255&255)>=10){var s=49,n=0;e=6;break}var o=(_<<24>>24)-48|0,l=L(r),b=ur(r,o,l),k=b;e=14;break}if(t<<24>>24==99){if(_<<24>>24!=118){var s=49,n=0;e=6;break}var u=N(r),c=D(r,42,u,0),k=c;e=14;break}var s=49,n=0;e=6}while(0);r:do if(6==e){for(;;){var n,s,h=(s-n)/2+n|0,d=(h<<4)+ri|0,w=Se[d>>2],p=Ae[w],E=t<<24>>24==p<<24>>24;if(E&&_<<24>>24==Ae[w+1|0]<<24>>24)break;var A=t<<24>>24<p<<24>>24;do if(A)var g=h,y=n;else{if(E&&_<<24>>24<Ae[w+1|0]<<24>>24){var g=h,y=n;break}var g=s,y=h+1|0}while(0);var y,g;if((0|y)==(0|g)){var k=0;break r}var s=g,n=y}var m=cr(r,d),k=m}while(0);var k;return k}function ur(r,a,e){var i=J(r),v=S(i,a,e),t=0==(0|v)?0:i;return t}function cr(r,a){var e=J(r);return 0!=(0|e)&&(Se[e>>2]=40,Se[e+4>>2]=a),e}function hr(r){var a,e,i=Se[r+44>>2],e=i>>2,v=0==(0|i);do if(!v){var t=Se[e];if(0==(0|t)){var f=r+48|0,_=Se[f>>2]+Se[e+2]|0;Se[f>>2]=_}else{if(21!=(0|t))break;var s=r+48|0,n=Se[s>>2]+Se[e+2]|0;Se[s>>2]=n}}while(0);var a=(r+12|0)>>2,o=Se[a],l=o+1|0;Se[a]=l;var b=Ae[o]<<24>>24;do if(67==(0|b)){var k=o+2|0;Se[a]=k;var u=Ae[l]<<24>>24;if(49==(0|u))var c=1;else if(50==(0|u))var c=2;else{if(51!=(0|u)){var h=0;break}var c=3}var c,d=wr(r,c,i),h=d}else if(68==(0|b)){var w=o+2|0;Se[a]=w;var p=Ae[l]<<24>>24;if(48==(0|p))var E=1;else if(49==(0|p))var E=2;else{if(50!=(0|p)){var h=0;break}var E=3}var E,A=pr(r,E,i),h=A}else var h=0;while(0);var h;return h}function dr(r){var a=r+12|0,e=Se[a>>2];if(Ae[e]<<24>>24==95){var i=e+1|0;Se[a>>2]=i;var v=sr(r),t=v>>>31^1}else var t=1;var t;return t}function wr(r,a,e){var i=J(r),v=M(i,a,e),t=0==(0|v)?0:i;return t}function pr(r,a,e){var i=J(r),v=C(i,a,e),t=0==(0|v)?0:i;return t}function Er(r,a){var e=J(r);return 0!=(0|e)&&(Se[e>>2]=5,Se[e+4>>2]=a),e}function Ar(r){var a,a=(r+12|0)>>2,e=Se[a],i=Ae[e]<<24>>24;do if(78==(0|i))var v=gr(r),t=v;else if(90==(0|i))var f=yr(r),t=f;else if(76==(0|i))var _=br(r),t=_;else if(83==(0|i)){if(Ae[e+1|0]<<24>>24==116){var s=e+2|0;Se[a]=s;var n=lr(r,0|He.__str152,3),o=br(r),l=D(r,1,n,o),b=r+48|0,k=Se[b>>2]+3|0;Se[b>>2]=k;var u=0,c=l}else var h=V(r,0),u=1,c=h;var c,u;if(Ae[Se[a]]<<24>>24!=73){var t=c;break}if(0==(0|u)){var d=R(r,c);if(0==(0|d)){var t=0;break}}var w=z(r),p=D(r,4,c,w),t=p}else{var E=br(r);if(Ae[Se[a]]<<24>>24!=73){var t=E;break}var A=R(r,E);if(0==(0|A)){var t=0;break}var g=z(r),y=D(r,4,E,g),t=y}while(0);var t;return t}function gr(r){var a,e=Oe;Oe+=4;var i=e,a=(r+12|0)>>2,v=Se[a],t=v+1|0;Se[a]=t;var f=Ae[v]<<24>>24==78;do if(f){var _=I(r,i,1);if(0==(0|_)){var s=0;break}var n=mr(r);if(Se[_>>2]=n,0==(0|n)){var s=0;break}var o=Se[a],l=o+1|0;if(Se[a]=l,Ae[o]<<24>>24!=69){var s=0;break}var s=Se[i>>2]}else var s=0;while(0);var s;return Oe=e,s}function yr(r){var a,a=(r+12|0)>>2,e=Se[a],i=e+1|0;Se[a]=i;var v=Ae[e]<<24>>24==90;do if(v){var t=O(r,0),f=Se[a],_=f+1|0;if(Se[a]=_,Ae[f]<<24>>24!=69){var s=0;break}if(Ae[_]<<24>>24==115){var n=f+2|0;Se[a]=n;var o=dr(r);if(0==(0|o)){var s=0;break}var l=lr(r,0|He.__str168,14),b=D(r,2,t,l),s=b}else{var k=Ar(r),u=dr(r);if(0==(0|u)){var s=0;break}var c=D(r,2,t,k),s=c}}else var s=0;while(0);var s;return s}function mr(r){var a,e=r+12|0,i=0;r:for(;;){var i,v=ge[Se[e>>2]];if(v<<24>>24==0){var t=0;break}var f=(v-48&255&255)<10|(v-97&255&255)<26;do{if(!f){if(v<<24>>24==76||v<<24>>24==68||v<<24>>24==67){a=5;break}if(v<<24>>24==83){var _=V(r,1),s=_;a=10;break}if(v<<24>>24==73){if(0==(0|i)){var t=0;break r}var n=z(r),o=4,l=n;a=11;break}if(v<<24>>24==84){var b=x(r),s=b;a=10;break}if(v<<24>>24==69){var t=i;break r}var t=0;break r}a=5}while(0);do if(5==a){var k=br(r),s=k;a=10;break}while(0);do if(10==a){var s;if(0==(0|i)){var u=s;a=12;break}var o=1,l=s;a=11;break}while(0);if(11==a)var l,o,c=D(r,o,i,l),u=c;var u;if(v<<24>>24!=83)if(Ae[Se[e>>2]]<<24>>24!=69){var h=R(r,u);if(0==(0|h)){var t=0;break}var i=u}else var i=u;else var i=u}var t;return t}function Sr(r,a){var e,i,v=Oe;Oe+=4;var t=v,i=t>>2,e=(r+12|0)>>2,f=Se[e];if(Ae[f]<<24>>24==74){var _=f+1|0;Se[e]=_;var s=1}else var s=a;var s;Se[i]=0;var n=s,o=0,l=t;r:for(;;)for(var l,o,n,b=n,k=o;;){var k,b,u=Ae[Se[e]];if(u<<24>>24==0||u<<24>>24==69){var c=Se[i];if(0==(0|c)){var h=0;break r}var d=0==(0|Se[c+8>>2]);do if(d){var w=Se[c+4>>2];if(33!=(0|Se[w>>2])){var p=c;break}var E=Se[w+4>>2];if(9!=(0|Se[E+16>>2])){var p=c;break}var A=r+48|0,g=Se[A>>2]-Se[E+4>>2]|0;Se[A>>2]=g,Se[i]=0;var p=0}else var p=c;while(0);var p,y=D(r,35,k,p),h=y;break r}var m=N(r);if(0==(0|m)){var h=0;break r}if(0==(0|b)){var S=D(r,38,m,0);if(Se[l>>2]=S,0==(0|S)){var h=0;break r}var n=0,o=k,l=S+8|0;continue r}var b=0,k=m}var h;return Oe=v,h}function Mr(r){for(var a=r;;){var a;if(0==(0|a)){var e=0;break}var i=Se[a>>2];if(1!=(0|i)&&2!=(0|i)){if(6==(0|i)||7==(0|i)||42==(0|i)){var e=1;break}var e=0;break}var a=Se[a+8>>2]}var e;return e}function Cr(r){var a=r>>2;Se[a+3]=0,Se[a+2]=0,Se[a+1]=0,Se[a]=0,Se[a+4]=0}function Rr(r,a){var e,e=(r+12|0)>>2,i=Se[e],v=(Se[r+4>>2]-i|0)<(0|a);r:do if(v)var t=0;else{var f=i+a|0;Se[e]=f;var _=0==(4&Se[r+8>>2]|0);do if(!_){if(Ae[f]<<24>>24!=36)break;var s=a+(i+1)|0;Se[e]=s}while(0);var n=(0|a)>9;do if(n){var o=La(i,0|He.__str117,8);if(0!=(0|o))break;var l=Ae[i+8|0];if(l<<24>>24!=46&&l<<24>>24!=95&&l<<24>>24!=36)break;if(Ae[i+9|0]<<24>>24!=78)break;var b=r+48|0,k=22-a+Se[b>>2]|0;Se[b>>2]=k;var u=lr(r,0|He.__str169,21),t=u;break r}while(0);var c=lr(r,i,a),t=c}while(0);var t;return t}function Tr(r){var a,e,e=(r+48|0)>>2,i=Se[e],v=i+20|0;Se[e]=v;var a=(r+12|0)>>2,t=Se[a],f=t+1|0;Se[a]=f;var _=Ae[t];do if(_<<24>>24==84){var s=t+2|0;Se[a]=s;var n=Ae[f]<<24>>24;if(86==(0|n)){var o=i+15|0;Se[e]=o;var l=N(r),b=D(r,8,l,0),k=b}else if(84==(0|n)){var u=i+10|0;Se[e]=u;var c=N(r),h=D(r,9,c,0),k=h}else if(73==(0|n))var d=N(r),w=D(r,11,d,0),k=w;else if(83==(0|n))var p=N(r),E=D(r,12,p,0),k=E;else if(104==(0|n)){var A=Nr(r,104);if(0==(0|A)){var k=0;break}var g=O(r,0),y=D(r,14,g,0),k=y}else if(118==(0|n)){var m=Nr(r,118);if(0==(0|m)){var k=0;break}var S=O(r,0),M=D(r,15,S,0),k=M}else if(99==(0|n)){var C=Nr(r,0);if(0==(0|C)){var k=0;break}var R=Nr(r,0);if(0==(0|R)){var k=0;break}var T=O(r,0),I=D(r,16,T,0),k=I}else if(67==(0|n)){var P=N(r),L=sr(r);if((0|L)<0){var k=0;break}var F=Se[a],X=F+1|0;if(Se[a]=X,Ae[F]<<24>>24!=95){var k=0;break}var j=N(r),U=Se[e]+5|0;Se[e]=U;var x=D(r,10,j,P),k=x}else if(70==(0|n))var z=N(r),V=D(r,13,z,0),k=V;else{if(74!=(0|n)){var k=0;break}var B=N(r),H=D(r,17,B,0),k=H}}else if(_<<24>>24==71){var K=t+2|0;Se[a]=K;var Y=Ae[f]<<24>>24;if(86==(0|Y))var G=Ar(r),W=D(r,18,G,0),k=W;else if(82==(0|Y))var Z=Ar(r),Q=D(r,19,Z,0),k=Q;else{if(65!=(0|Y)){var k=0;break}var q=O(r,0),$=D(r,20,q,0),k=$}}else var k=0;while(0);var k;return k}function Or(r){for(var a,e=r,a=e>>2;;){var e;if(0==(0|e)){var i=0;break}var v=Se[a];if(4==(0|v)){var t=Se[a+1],f=Mr(t),i=0==(0|f)&1;break}if(25!=(0|v)&&26!=(0|v)&&27!=(0|v)){var i=0;break}var e=Se[a+1],a=e>>2}var i;return i}function Nr(r,a){var e;if(0==(0|a)){var i=r+12|0,v=Se[i>>2],t=v+1|0;Se[i>>2]=t;var f=Ae[v]<<24>>24}else var f=a;var f;do{if(104==(0|f)){var _=(sr(r),r+12|0);e=7;break}if(118==(0|f)){var s=(sr(r),r+12|0),n=Se[s>>2],o=n+1|0;if(Se[s>>2]=o,Ae[n]<<24>>24!=95){var l=0;e=8;break}var _=(sr(r),s);e=7;break}var l=0;e=8}while(0);if(7==e){var _,b=Se[_>>2],k=b+1|0;Se[_>>2]=k;var l=Ae[b]<<24>>24==95&1}var l;return l}function Ir(r){var a,e,i=r>>2,v=Oe;Oe+=56;var t,f=v,_=v+8,s=v+16,n=v+36,e=(0|r)>>2,o=Se[e],l=0==(8192&o|0);r:do{if(l){var a=(r+12|0)>>2,b=Se[a];if(Ae[b]<<24>>24!=63){var k=0;t=111;break}var u=b+1|0;Se[a]=u;var c=Ae[u];do if(c<<24>>24==63){if(Ae[b+2|0]<<24>>24==36){var h=b+3|0;if(Ae[h]<<24>>24!=63){var d=5;t=90;break}Se[a]=h;var w=6,p=h}else var w=0,p=u;var p,w,E=p+1|0;Se[a]=E;var A=Ae[E]<<24>>24;do if(48==(0|A)){var g=1;t=81}else{if(49==(0|A)){var g=2;t=81;break}if(50!=(0|A)){if(51==(0|A)){var y=0|He.__str2172,m=E;t=82;break}if(52==(0|A)){var y=0|He.__str3173,m=E;t=82;break}if(53==(0|A)){var y=0|He.__str4174,m=E;t=82;break}if(54==(0|A)){var y=0|He.__str5175,m=E;t=82;break}if(55==(0|A)){var y=0|He.__str6176,m=E;t=82;break}if(56==(0|A)){var y=0|He.__str7177,m=E;t=82;break}if(57==(0|A)){var y=0|He.__str8178,m=E;t=82;break}if(65==(0|A)){var y=0|He.__str9179,m=E;t=82;break}if(66==(0|A)){Se[a]=p+2|0;var S=0|He.__str10180,M=3;t=88;break}if(67==(0|A)){var y=0|He.__str11181,m=E;t=82;break}if(68==(0|A)){var y=0|He.__str12182,m=E;t=82;break}if(69==(0|A)){var y=0|He.__str13183,m=E;t=82;break}if(70==(0|A)){var y=0|He.__str14184,m=E;t=82;break}if(71==(0|A)){var y=0|He.__str15185,m=E;t=82;break}if(72==(0|A)){var y=0|He.__str16186,m=E;t=82;break}if(73==(0|A)){var y=0|He.__str17187,m=E;t=82;break}if(74==(0|A)){var y=0|He.__str18188,m=E;t=82;break}if(75==(0|A)){var y=0|He.__str19189,m=E;t=82;break}if(76==(0|A)){var y=0|He.__str20190,m=E;t=82;break}if(77==(0|A)){var y=0|He.__str21191,m=E;t=82;break}if(78==(0|A)){var y=0|He.__str22192,m=E;t=82;break}if(79==(0|A)){var y=0|He.__str23193,m=E;t=82;break}if(80==(0|A)){var y=0|He.__str24194,m=E;t=82;break}if(81==(0|A)){var y=0|He.__str25195,m=E;t=82;break}if(82==(0|A)){var y=0|He.__str26196,m=E;t=82;break}if(83==(0|A)){var y=0|He.__str27197,m=E;t=82;break}if(84==(0|A)){var y=0|He.__str28198,m=E;t=82;break}if(85==(0|A)){var y=0|He.__str29199,m=E;t=82;break}if(86==(0|A)){var y=0|He.__str30200,m=E;t=82;break}if(87==(0|A)){var y=0|He.__str31201,m=E;t=82;break}if(88==(0|A)){var y=0|He.__str32202,m=E;t=82;break}if(89==(0|A)){var y=0|He.__str33203,m=E;t=82;break}if(90==(0|A)){var y=0|He.__str34204,m=E;t=82;break}if(95==(0|A)){var C=p+2|0;Se[a]=C;var R=Ae[C]<<24>>24;if(48==(0|R)){var y=0|He.__str35205,m=C;t=82;break}if(49==(0|R)){var y=0|He.__str36206,m=C;t=82;break}if(50==(0|R)){var y=0|He.__str37207,m=C;t=82;break}if(51==(0|R)){var y=0|He.__str38208,m=C;t=82;break}if(52==(0|R)){var y=0|He.__str39209,m=C;t=82;break}if(53==(0|R)){var y=0|He.__str40210,m=C;t=82;break}if(54==(0|R)){var y=0|He.__str41211,m=C;t=82;break}if(55==(0|R)){var y=0|He.__str42212,m=C;t=82;break}if(56==(0|R)){var y=0|He.__str43213,m=C;t=82;break}if(57==(0|R)){var y=0|He.__str44214,m=C;t=82;break}if(65==(0|R)){var y=0|He.__str45215,m=C;t=82;break}if(66==(0|R)){var y=0|He.__str46216,m=C;t=82;break}if(67==(0|R)){Se[a]=p+3|0;var T=0|He.__str47217;t=84;break}if(68==(0|R)){var y=0|He.__str48218,m=C;t=82;break}if(69==(0|R)){var y=0|He.__str49219,m=C;t=82;break}if(70==(0|R)){var y=0|He.__str50220,m=C;t=82;break}if(71==(0|R)){var y=0|He.__str51221,m=C;t=82;break}if(72==(0|R)){var y=0|He.__str52222,m=C;t=82;break}if(73==(0|R)){var y=0|He.__str53223,m=C;t=82;break}if(74==(0|R)){var y=0|He.__str54224,m=C;t=82;break}if(75==(0|R)){var y=0|He.__str55225,m=C;t=82;break}if(76==(0|R)){var y=0|He.__str56226,m=C;t=82;break}if(77==(0|R)){var y=0|He.__str57227,m=C;t=82;break}if(78==(0|R)){var y=0|He.__str58228,m=C;t=82;break}if(79==(0|R)){var y=0|He.__str59229,m=C;t=82;break}if(82==(0|R)){var O=4|o;Se[e]=O;var N=p+3|0;Se[a]=N;var I=Ae[N]<<24>>24;if(48==(0|I)){Se[a]=p+4|0,Cr(s);var P=(Pr(r,_,s,0),Se[_>>2]),D=Se[_+4>>2],L=Dr(r,0|He.__str60230,(ne=Oe,Oe+=8,Se[ne>>2]=P,Se[ne+4>>2]=D,ne)),F=Se[a]-1|0;Se[a]=F;var y=L,m=F;t=82;break}if(49==(0|I)){Se[a]=p+4|0;var X=Lr(r),j=Lr(r),U=Lr(r),x=Lr(r),z=Se[a]-1|0;Se[a]=z;var V=Dr(r,0|He.__str61231,(ne=Oe,Oe+=16,Se[ne>>2]=X,Se[ne+4>>2]=j,Se[ne+8>>2]=U,Se[ne+12>>2]=x,ne)),y=V,m=Se[a];t=82;break}if(50==(0|I)){var y=0|He.__str62232,m=N;t=82;break}if(51==(0|I)){var y=0|He.__str63233,m=N;t=82;break}if(52==(0|I)){var y=0|He.__str64234,m=N;t=82;break}var y=0,m=N;t=82;break}if(83==(0|R)){var y=0|He.__str65235,m=C;t=82;break}if(84==(0|R)){var y=0|He.__str66236,m=C;t=82;break}if(85==(0|R)){var y=0|He.__str67237,m=C;t=82;break}if(86==(0|R)){var y=0|He.__str68238,m=C;t=82;break}if(88==(0|R)){var y=0|He.__str69239,m=C;t=82;break}if(89==(0|R)){var y=0|He.__str70240,m=C;t=82;break}var k=0;t=111;break r}var k=0;t=111;break r}var y=0|He.__str1171,m=E;t=82}while(0);do{if(81==t){var g;Se[a]=p+2|0;var B=g;t=83;break}if(82==t){var m,y;if(Se[a]=m+1|0,1==(0|w)||2==(0|w)){var B=w;t=83;break}if(4==(0|w)){var T=y;t=84;break}if(6!=(0|w)){var S=y,M=w;t=88;break}Cr(n);var H=Xr(r,n,0,60,62);if(0==(0|H))var K=y;else var Y=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=y,Se[ne+4>>2]=H,ne)),K=Y;var K;Se[i+6]=0;var S=K,M=w;t=88;break}}while(0);if(83==t){var B,G=r+40|0,W=Fr(r,0|He._symbol_demangle_dashed_null,-1,G);if(0==(0|W)){var k=0;t=111;break r}var d=B;t=90;break}if(84==t){var T;Se[i+4]=T;var Z=1,Q=T;t=109;break r}if(88==t){var M,S,q=r+40|0,$=Fr(r,S,-1,q);if(0==(0|$)){var k=0;t=111;break r}var d=M;t=90;break}}else{if(c<<24>>24==36){var J=b+2|0;Se[a]=J;var rr=jr(r);Se[i+4]=rr;var ar=0!=(0|rr)&1;t=107;break}var d=0;t=90}while(0);if(90==t){var d,er=Me[a],ir=Ae[er]<<24>>24;if(64==(0|ir))Se[a]=er+1|0;else if(36==(0|ir))t=93;else{var vr=zr(r);if(0==(0|vr)){var k=-1;t=111;break}}if(5==(0|d)){var tr=r+20|0,fr=Se[tr>>2]+1|0;Se[tr>>2]=fr}else if(1==(0|d)||2==(0|d)){if(Me[i+11]>>>0<2){var k=-1;t=111;break}var _r=r+56|0,sr=Me[_r>>2],nr=Se[sr+4>>2];if(1==(0|d))Se[sr>>2]=nr;else{var or=Dr(r,0|He.__str71241,(ne=Oe,Oe+=4,Se[ne>>2]=nr,ne)),lr=Se[_r>>2];Se[lr>>2]=or}var br=4|Se[e];Se[e]=br}else if(3==(0|d)){var kr=Se[e]&-5;Se[e]=kr}var ur=ge[Se[a]];if((ur-48&255&255)<10)var cr=Vr(r),ar=cr;else if((ur-65&255&255)<26)var hr=Br(r,3==(0|d)&1),ar=hr;else{if(ur<<24>>24!=36){var k=-1;t=111;break}var dr=Hr(r),ar=dr}}var ar;if(0==(0|ar)){var k=-1;t=111;break}var Z=ar,Q=Se[i+4];t=109;break}var wr=Pr(r,f,0,0);if(0==(0|wr)){var k=-1;t=111;break}var pr=Se[f>>2],Er=Se[f+4>>2],Ar=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=pr,Se[ne+4>>2]=Er,ne));Se[i+4]=Ar;var Z=1,Q=Ar;t=109;break}while(0);do if(109==t){var Q,Z;if(0!=(0|Q)){var k=Z;break}Xa(0|He.__str72242,1499,0|He.___func___symbol_demangle,0|He.__str73243);var k=Z}while(0);var k;return Oe=v,k}function Pr(r,a,e,i){var v,t,f,_=Oe;Oe+=24;var s=_,n=_+4,o=_+8,l=_+16,b=_+20;0==(0|a)&&Xa(0|He.__str72242,829,0|He.___func___demangle_datatype,0|He.__str121291);var f=(a+4|0)>>2;Se[f]=0;var t=(0|a)>>2;Se[t]=0;var v=(r+12|0)>>2,k=Me[v],u=k+1|0;Se[v]=u;var c=Ae[k],h=c<<24>>24;do if(95==(0|h)){Se[v]=k+2|0;var d=Ae[u],w=Zr(d);Se[t]=w}else if(67==(0|h)||68==(0|h)||69==(0|h)||70==(0|h)||71==(0|h)||72==(0|h)||73==(0|h)||74==(0|h)||75==(0|h)||77==(0|h)||78==(0|h)||79==(0|h)||88==(0|h)||90==(0|h)){var p=Qr(c);Se[t]=p}else if(84==(0|h)||85==(0|h)||86==(0|h)||89==(0|h)){var E=qr(r);if(0==(0|E))break;var A=0==(32768&Se[r>>2]|0);do if(A)if(84==(0|h))var g=0|He.__str122292;else if(85==(0|h))var g=0|He.__str123293;else if(86==(0|h))var g=0|He.__str124294;else{if(89!=(0|h)){var g=0;break}var g=0|He.__str125295}else var g=0;while(0);var g,y=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=g,Se[ne+4>>2]=E,ne));Se[t]=y}else if(63==(0|h))if(0==(0|i))$r(a,r,e,63,0);else{var m=Lr(r);if(0==(0|m))break;var S=Dr(r,0|He.__str126296,(ne=Oe,Oe+=4,Se[ne>>2]=m,ne));Se[t]=S}else if(65==(0|h)||66==(0|h))$r(a,r,e,c,i);else if(81==(0|h)||82==(0|h)||83==(0|h)){var M=0==(0|i)?80:c;$r(a,r,e,M,i)}else if(80==(0|h))if(((Ae[u]<<24>>24)-48|0)>>>0<10){var C=k+2|0;if(Se[v]=C,Ae[u]<<24>>24!=54)break;var R=r+44|0,T=Se[R>>2];Se[v]=k+3|0;var O=Ae[C],N=Se[r>>2]&-17,I=Ur(O,s,n,N);if(0==(0|I))break;var P=Pr(r,o,e,0);if(0==(0|P))break;var D=Xr(r,e,1,40,41);if(0==(0|D))break;Se[R>>2]=T;var L=Se[o>>2],F=Se[o+4>>2],X=Se[s>>2],j=Dr(r,0|He.__str127297,(ne=Oe,Oe+=12,Se[ne>>2]=L,Se[ne+4>>2]=F,Se[ne+8>>2]=X,ne));Se[t]=j;var U=Dr(r,0|He.__str128298,(ne=Oe,Oe+=4,Se[ne>>2]=D,ne));Se[f]=U}else $r(a,r,e,80,i);else if(87==(0|h)){if(Ae[u]<<24>>24!=52)break;Se[v]=k+2|0;var x=qr(r);if(0==(0|x))break;if(0==(32768&Se[r>>2]|0)){var z=Dr(r,0|He.__str129299,(ne=Oe,Oe+=4,Se[ne>>2]=x,ne));Se[t]=z}else Se[t]=x}else if(48==(0|h)||49==(0|h)||50==(0|h)||51==(0|h)||52==(0|h)||53==(0|h)||54==(0|h)||55==(0|h)||56==(0|h)||57==(0|h)){var V=h<<1,B=V-96|0,H=Yr(e,B);Se[t]=H;var K=V-95|0,Y=Yr(e,K);Se[f]=Y}else if(36==(0|h)){var G=k+2|0;Se[v]=G;var W=Ae[u]<<24>>24;if(48==(0|W)){var Z=Lr(r);Se[t]=Z}else if(68==(0|W)){var Q=Lr(r);if(0==(0|Q))break;var q=Dr(r,0|He.__str130300,(ne=Oe,Oe+=4,Se[ne>>2]=Q,ne));Se[t]=q}else if(70==(0|W)){var $=Lr(r);if(0==(0|$))break;var J=Lr(r);if(0==(0|J))break;var rr=Dr(r,0|He.__str131301,(ne=Oe,Oe+=8,Se[ne>>2]=$,Se[ne+4>>2]=J,ne));Se[t]=rr}else if(71==(0|W)){var ar=Lr(r);if(0==(0|ar))break;var er=Lr(r);if(0==(0|er))break;var ir=Lr(r);if(0==(0|ir))break;var vr=Dr(r,0|He.__str132302,(ne=Oe,Oe+=12,Se[ne>>2]=ar,Se[ne+4>>2]=er,Se[ne+8>>2]=ir,ne));Se[t]=vr}else if(81==(0|W)){var tr=Lr(r);if(0==(0|tr))break;var fr=Dr(r,0|He.__str133303,(ne=Oe,Oe+=4,Se[ne>>2]=tr,ne));Se[t]=fr}else{if(36!=(0|W))break;if(Ae[G]<<24>>24!=67)break;Se[v]=k+3|0;var _r=xr(r,l,b);if(0==(0|_r))break;var sr=Pr(r,a,e,i);if(0==(0|sr))break;var nr=Se[t],or=Se[l>>2],lr=Dr(r,0|He.__str83253,(ne=Oe,Oe+=8,Se[ne>>2]=nr,Se[ne+4>>2]=or,ne));Se[t]=lr}}while(0);var br=0!=(0|Se[t])&1;return Oe=_,br}function Dr(r,a){var e,i=Oe;Oe+=4;var v=i,e=v>>2,t=v;Se[t>>2]=arguments[Dr.length];var f=1,_=0;r:for(;;){var _,f,s=Ae[a+_|0];do{if(s<<24>>24==0)break r;if(s<<24>>24==37){var n=_+1|0,o=Ae[a+n|0]<<24>>24;if(115==(0|o)){var l=Se[e],b=l,k=l+4|0;Se[e]=k;var u=Se[b>>2];if(0==(0|u)){var c=f,h=n;break}var d=Ca(u),c=d+f|0,h=n;break}if(99==(0|o)){var w=Se[e]+4|0;Se[e]=w;var c=f+1|0,h=n;break}if(37==(0|o))var p=n;else var p=_;var p,c=f+1|0,h=p}else var c=f+1|0,h=_}while(0);var h,c,f=c,_=h+1|0}var E=Wr(r,f);if(0==(0|E))var A=0;else{Se[t>>2]=arguments[Dr.length];var g=E,y=0;r:for(;;){var y,g,m=Ae[a+y|0];do{if(m<<24>>24==0)break r;if(m<<24>>24==37){var S=y+1|0,M=Ae[a+S|0]<<24>>24;if(115==(0|M)){var C=Se[e],R=C,T=C+4|0;Se[e]=T;var O=Se[R>>2];if(0==(0|O)){var N=g,I=S;break}var P=Ca(O);Pa(g,O,P,1);var N=g+P|0,I=S;break}if(99==(0|M)){var D=Se[e],L=D,F=D+4|0;Se[e]=F,Ae[g]=255&Se[L>>2];var N=g+1|0,I=S;break}if(37==(0|M))var X=S;else var X=y;var X;Ae[g]=37;var N=g+1|0,I=X}else{Ae[g]=m;var N=g+1|0,I=y}}while(0);var I,N,g=N,y=I+1|0}Ae[g]=0;var A=E}var A;return Oe=i,A}function Lr(r){var a,a=(r+12|0)>>2,e=Se[a],i=Ae[e];if(i<<24>>24==63){var v=e+1|0;Se[a]=v;var t=1,f=v,_=Ae[v]}else var t=0,f=e,_=i;var _,f,t,s=(_-48&255&255)<9;do if(s){var n=Wr(r,3),o=0!=(0|t);o&&(Ae[n]=45);var l=Ae[Se[a]]+1&255;Ae[n+t|0]=l;var b=o?2:1;\nAe[n+b|0]=0;var k=Se[a]+1|0;Se[a]=k;var u=n}else if(_<<24>>24==57){var c=Wr(r,4),h=0!=(0|t);h&&(Ae[c]=45),Ae[c+t|0]=49;var d=h?2:1;Ae[c+d|0]=48;var w=h?3:2;Ae[c+w|0]=0;var p=Se[a]+1|0;Se[a]=p;var u=c}else{if((_-65&255&255)>=16){var u=0;break}for(var E=0,A=f;;){var A,E,g=A+1|0;Se[a]=g;var y=(Ae[A]<<24>>24)+((E<<4)-65)|0,m=ge[g];if((m-65&255&255)>=16)break;var E=y,A=g}if(m<<24>>24!=64){var u=0;break}var S=Wr(r,17),M=0!=(0|t)?0|He.__str119289:0|ii,C=(za(S,0|He.__str118288,(ne=Oe,Oe+=8,Se[ne>>2]=M,Se[ne+4>>2]=y,ne)),Se[a]+1|0);Se[a]=C;var u=S}while(0);var u;return u}function Fr(r,a,e,i){var v,t,f,_;0==(0|a)&&Xa(0|He.__str72242,212,0|He.___func___str_array_push,0|He.__str115285),0==(0|i)&&Xa(0|He.__str72242,213,0|He.___func___str_array_push,0|He.__str116286);var f=(i+12|0)>>2,s=Me[f],n=0==(0|s);do{if(n){Se[f]=32;var o=Wr(r,128);if(0==(0|o)){var l=0;_=17;break}Se[i+16>>2]=o,_=11;break}if(Me[i+8>>2]>>>0<s>>>0){_=11;break}var b=s<<3,k=Wr(r,b);if(0==(0|k)){var l=0;_=17;break}var u=k,c=i+16|0,h=Se[c>>2],d=Se[f]<<2;Pa(k,h,d,1);var w=Se[f]<<1;Se[f]=w,Se[c>>2]=u,_=11;break}while(0);do if(11==_){if((0|e)==-1)var p=Ca(a),E=p;else var E=e;var E,A=ja(a),g=E+1|0,y=Wr(r,g),t=(i+4|0)>>2,v=(i+16|0)>>2,m=(Se[t]<<2)+Se[v]|0;Se[m>>2]=y;var S=Se[Se[v]+(Se[t]<<2)>>2];if(0==(0|S)){Xa(0|He.__str72242,233,0|He.___func___str_array_push,0|He.__str117287);var M=Se[Se[v]+(Se[t]<<2)>>2]}else var M=S;var M;Pa(M,A,E,1),va(A),Ae[Se[Se[v]+(Se[t]<<2)>>2]+g|0]=0;var C=Se[t]+1|0;Se[t]=C;var R=i+8|0;if(C>>>0<Me[R>>2]>>>0){var l=1;break}Se[R>>2]=C;var l=1}while(0);var l;return l}function Xr(r,a,e,i,v){var t,f,_=Oe;Oe+=28;var s,n=_,o=_+8;Cr(o);var f=(r+12|0)>>2,l=0==(0|e),t=(0|n)>>2,b=n+4|0;r:do if(l)for(;;){var k=Se[f],u=Ae[k];if(u<<24>>24==0){s=12;break r}if(u<<24>>24==64){var c=k;s=7;break r}var h=Pr(r,n,a,1);if(0==(0|h)){var d=0;s=25;break r}var w=Se[t],p=Se[b>>2],E=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=w,Se[ne+4>>2]=p,ne)),A=Fr(r,E,-1,o);if(0==(0|A)){var d=0;s=25;break r}var g=Se[t],y=Da(g,0|He.__str110280);if(0==(0|y)){s=12;break r}}else for(;;){var m=Se[f],S=Ae[m];if(S<<24>>24==0){s=12;break r}if(S<<24>>24==64){var c=m;s=7;break r}var M=Pr(r,n,a,1);if(0==(0|M)){var d=0;s=25;break r}var C=Se[t],R=Da(C,0|He.__str84254);if(0==(0|R)){s=13;break r}var T=Se[b>>2],O=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=C,Se[ne+4>>2]=T,ne)),N=Fr(r,O,-1,o);if(0==(0|N)){var d=0;s=25;break r}var I=Se[t],P=Da(I,0|He.__str110280);if(0==(0|P)){s=12;break r}}while(0);do if(7==s){var c;Se[f]=c+1|0,s=12;break}while(0);do if(12==s){if(l){s=14;break}s=13;break}while(0);do if(13==s){var D=Se[f],L=D+1|0;if(Se[f]=L,Ae[D]<<24>>24==90){s=14;break}var d=0;s=25;break}while(0);r:do if(14==s){var F=o+4|0,X=Me[F>>2];do{if(0!=(0|X)){if(1==(0|X)){var j=o+16|0,U=Se[Se[j>>2]>>2],x=Da(U,0|He.__str84254);if(0==(0|x)){s=17;break}var z=j;s=20;break}var V=o+16|0;if(X>>>0<=1){var z=V;s=20;break}for(var B=0,H=1;;){var H,B,K=Se[Se[V>>2]+(H<<2)>>2],Y=Dr(r,0|He.__str112282,(ne=Oe,Oe+=8,Se[ne>>2]=B,Se[ne+4>>2]=K,ne)),G=H+1|0;if(G>>>0>=Me[F>>2]>>>0)break;var B=Y,H=G}if(0==(0|Y)){var z=V;s=20;break}var W=Y,Z=Y;s=21;break}s=17}while(0);if(17==s){var Q=i<<24>>24,q=v<<24>>24,$=Dr(r,0|He.__str111281,(ne=Oe,Oe+=8,Se[ne>>2]=Q,Se[ne+4>>2]=q,ne)),d=$;break}if(20==s)var z,W=Se[Se[z>>2]>>2],Z=0;var Z,W,J=v<<24>>24,rr=v<<24>>24==62;do if(rr){var ar=Ca(W);if(Ae[W+(ar-1)|0]<<24>>24!=62)break;var er=i<<24>>24,ir=Se[Se[o+16>>2]>>2],vr=Dr(r,0|He.__str113283,(ne=Oe,Oe+=16,Se[ne>>2]=er,Se[ne+4>>2]=ir,Se[ne+8>>2]=Z,Se[ne+12>>2]=J,ne)),d=vr;break r}while(0);var tr=i<<24>>24,fr=Se[Se[o+16>>2]>>2],_r=Dr(r,0|He.__str114284,(ne=Oe,Oe+=16,Se[ne>>2]=tr,Se[ne+4>>2]=fr,Se[ne+8>>2]=Z,Se[ne+12>>2]=J,ne)),d=_r}while(0);var d;return Oe=_,d}function jr(r){var a,e=Oe;Oe+=20;var i=e,v=r+24|0,t=Se[v>>2],a=(r+20|0)>>2,f=Se[a],_=r+44|0,s=Se[_>>2];Se[a]=t;var n=Kr(r);if(0==(0|n))var o=0;else{Cr(i);var l=Xr(r,i,0,60,62);if(0==(0|l))var b=n;else var k=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=n,Se[ne+4>>2]=l,ne)),b=k;var b;Se[v>>2]=t,Se[a]=f,Se[_>>2]=s;var o=b}var o;return Oe=e,o}function Ur(r,a,e,i){var v,t=a>>2;Se[e>>2]=0,Se[t]=0;var f=0==(18&i|0);do{if(f){var _=r<<24>>24,s=1==((_-65)%2|0);if(0==(1&i|0)){if(s?Se[e>>2]=0|He.__str95265:v=14,65==(0|_)||66==(0|_)){Se[t]=0|He.__str96266,v=21;break}if(67==(0|_)||68==(0|_)){Se[t]=0|He.__str97267,v=21;break}if(69==(0|_)||70==(0|_)){Se[t]=0|He.__str98268,v=21;break}if(71==(0|_)||72==(0|_)){Se[t]=0|He.__str99269,v=21;break}if(73==(0|_)||74==(0|_)){Se[t]=0|He.__str100270,v=21;break}if(75==(0|_)||76==(0|_)){v=21;break}if(77==(0|_)){Se[t]=0|He.__str101271,v=21;break}var n=0;v=22;break}if(s?Se[e>>2]=0|He.__str88258:v=5,65==(0|_)||66==(0|_)){Se[t]=0|He.__str89259,v=21;break}if(67==(0|_)||68==(0|_)){Se[t]=0|He.__str90260,v=21;break}if(69==(0|_)||70==(0|_)){Se[t]=0|He.__str91261,v=21;break}if(71==(0|_)||72==(0|_)){Se[t]=0|He.__str92262,v=21;break}if(73==(0|_)||74==(0|_)){Se[t]=0|He.__str93263,v=21;break}if(75==(0|_)||76==(0|_)){v=21;break}if(77==(0|_)){Se[t]=0|He.__str94264,v=21;break}var n=0;v=22;break}v=21}while(0);if(21==v)var n=1;var n;return n}function xr(r,a,e){var i;Se[e>>2]=0;var i=(r+12|0)>>2,v=Se[i];if(Ae[v]<<24>>24==69){Se[e>>2]=0|He.__str102272;var t=Se[i]+1|0;Se[i]=t;var f=t}else var f=v;var f;Se[i]=f+1|0;var _=Ae[f]<<24>>24;if(65==(0|_)){Se[a>>2]=0;var s=1}else if(66==(0|_)){Se[a>>2]=0|He.__str103273;var s=1}else if(67==(0|_)){Se[a>>2]=0|He.__str104274;var s=1}else if(68==(0|_)){Se[a>>2]=0|He.__str105275;var s=1}else var s=0;var s;return s}function zr(r){var a,e,a=(r+12|0)>>2,i=r+40|0,v=r+20|0,t=0|i,f=r+44|0,_=r+48|0,s=r+52|0,n=r+56|0,o=r+20|0,l=r+24|0,b=r+16|0,k=0;r:for(;;){var k,u=Se[a],c=Ae[u];if(c<<24>>24==64){var h=u+1|0;Se[a]=h;var d=1;break}var w=c<<24>>24;do{if(0==(0|w)){var d=0;break r}if(48==(0|w)||49==(0|w)||50==(0|w)||51==(0|w)||52==(0|w)||53==(0|w)||54==(0|w)||55==(0|w)||56==(0|w)||57==(0|w)){var p=u+1|0;Se[a]=p;var E=(Ae[u]<<24>>24)-48|0,A=Yr(v,E),g=A;e=14;break}if(63==(0|w)){var y=u+1|0;Se[a]=y;var m=Ae[y]<<24>>24;if(36==(0|m)){var S=u+2|0;Se[a]=S;var M=jr(r);if(0==(0|M)){var d=0;break r}var C=Fr(r,M,-1,v);if(0==(0|C)){var d=0;break r}var R=M;e=15;break}if(63==(0|m)){var T=Se[t>>2],O=Se[f>>2],N=Se[_>>2],I=Se[s>>2],P=Se[n>>2],D=Se[o>>2],L=Se[l>>2];Cr(i);var F=Ir(r);if(0==(0|F))var X=k;else var j=Se[b>>2],U=Dr(r,0|He.__str109279,(ne=Oe,Oe+=4,Se[ne>>2]=j,ne)),X=U;var X;Se[o>>2]=D,Se[l>>2]=L,Se[t>>2]=T,Se[f>>2]=O,Se[_>>2]=N,Se[s>>2]=I,Se[n>>2]=P;var g=X;e=14;break}var x=Lr(r);if(0==(0|x)){var d=0;break r}var z=Dr(r,0|He.__str109279,(ne=Oe,Oe+=4,Se[ne>>2]=x,ne)),g=z;e=14;break}var V=Kr(r),g=V;e=14;break}while(0);if(14==e){var g;if(0==(0|g)){var d=0;break}var R=g}var R,B=Fr(r,R,-1,i);if(0==(0|B)){var d=0;break}var k=R}var d;return d}function Vr(r){var a,e,i,v=Oe;Oe+=36;var t,f=v,i=f>>2,_=v+4,s=v+8,e=s>>2,n=v+16;Se[i]=0;var o=0|r,l=Se[o>>2],b=0==(128&l|0),k=r+12|0;do if(b){var u=Ae[Se[k>>2]]<<24>>24;if(48==(0|u))var c=0|He.__str76246,h=k,a=h>>2;else if(49==(0|u))var c=0|He.__str77247,h=k,a=h>>2;else{if(50!=(0|u)){var c=0,h=k,a=h>>2;break}var c=0|He.__str78248,h=k,a=h>>2}}else var c=0,h=k,a=h>>2;while(0);var h,c,d=0==(512&l|0);do if(d){if((Ae[Se[a]]-48&255&255)>=3){var w=0;break}var w=0|He.__str79249}else var w=0;while(0);var w,p=Gr(r,0),E=Se[a],A=E+1|0;Se[a]=A;var g=Ae[E]<<24>>24;do{if(48==(0|g)||49==(0|g)||50==(0|g)||51==(0|g)||52==(0|g)||53==(0|g)){var y=r+44|0,m=Se[y>>2];Cr(n);var S=Pr(r,s,n,0);if(0==(0|S)){var M=0;t=28;break}var C=xr(r,f,_);if(0==(0|C)){var M=0;t=28;break}var R=Se[i],T=0==(0|R),O=Se[_>>2];do if(T)Se[i]=O;else{if(0==(0|O))break;var N=Dr(r,0|He.__str83253,(ne=Oe,Oe+=8,Se[ne>>2]=R,Se[ne+4>>2]=O,ne));Se[i]=N}while(0);Se[y>>2]=m,t=22;break}if(54==(0|g)||55==(0|g)){var I=s+4|0;Se[I>>2]=0,Se[e]=0;var P=xr(r,f,_);if(0==(0|P)){var M=0;t=28;break}if(Ae[Se[a]]<<24>>24==64){t=22;break}var D=qr(r);if(0==(0|D)){var M=0;t=28;break}var L=Dr(r,0|He.__str107277,(ne=Oe,Oe+=4,Se[ne>>2]=D,ne));Se[I>>2]=L,t=22;break}if(56==(0|g)||57==(0|g)){Se[e+1]=0,Se[e]=0,Se[i]=0,t=22;break}var M=0;t=28}while(0);if(22==t){var F=0==(4096&Se[o>>2]|0);do{if(F){var X=Se[e],j=Se[i];if(0==(0|j)){var U=X;t=26;break}var x=0!=(0|X)?0|He.__str87257:0,z=0|He.__str87257,V=j,B=x,H=X;t=27;break}Se[i]=0,Se[e+1]=0,Se[e]=0;var U=0;t=26;break}while(0);if(26==t)var U,K=0!=(0|U)?0|He.__str87257:0,z=K,V=0,B=0,H=U;var H,B,V,z,Y=Se[e+1],G=Dr(r,0|He.__str108278,(ne=Oe,Oe+=32,Se[ne>>2]=c,Se[ne+4>>2]=w,Se[ne+8>>2]=H,Se[ne+12>>2]=B,Se[ne+16>>2]=V,Se[ne+20>>2]=z,Se[ne+24>>2]=p,Se[ne+28>>2]=Y,ne));Se[r+16>>2]=G;var M=1}var M;return Oe=v,M}function Br(r,a){var e,i,v,t,f=Oe;Oe+=44;var _,s=f,t=s>>2,n=f+8,o=f+12,v=o>>2,l=f+16,b=f+20,k=f+40;Se[v]=0;var i=(r+12|0)>>2,u=Se[i],c=u+1|0;Se[i]=c;var h=ge[u],d=h<<24>>24,w=(h-65&255&255)>25;r:do if(w)var p=0;else{var e=(0|r)>>2,E=Me[e],A=0==(128&E|0),g=d-65|0;do if(A){var y=g/8|0;if(0==(0|y))var m=0|He.__str76246,S=g;else if(1==(0|y))var m=0|He.__str77247,S=g;else{if(2!=(0|y)){var m=0,S=g;break}var m=0|He.__str78248,S=g}}else var m=0,S=g;while(0);var S,m,M=0==(512&E|0)&h<<24>>24<89,C=(0|S)%8;do if(M)if(2==(0|C)||3==(0|C))var R=m,T=0|He.__str79249;else if(4==(0|C)||5==(0|C))var R=m,T=0|He.__str80250;else{if(6!=(0|C)&&7!=(0|C)){var R=m,T=0;break}var O=Dr(r,0|He.__str81251,(ne=Oe,Oe+=4,Se[ne>>2]=m,ne)),R=O,T=0|He.__str80250}else var R=m,T=0;while(0);var T,R,N=Gr(r,0),I=6==(0|C);do{if(!I){if(7==((d-56)%8|0)){_=14;break}var P=N;_=15;break}_=14}while(0);if(14==_)var D=Lr(r),L=Dr(r,0|He.__str82252,(ne=Oe,Oe+=8,Se[ne>>2]=N,Se[ne+4>>2]=D,ne)),P=L;var P,F=h<<24>>24>88;do if(F)var X=0;else{if((C-2|0)>>>0<2){var X=0;break}var j=xr(r,o,k);if(0==(0|j)){var p=0;break r}var U=Me[v],x=Se[k>>2];if(0==(0|U)&0==(0|x)){var X=0;break}var z=Dr(r,0|He.__str83253,(ne=Oe,Oe+=8,Se[ne>>2]=U,Se[ne+4>>2]=x,ne));Se[v]=z;var X=z}while(0);var X,V=Se[i],B=V+1|0;Se[i]=B;var H=Ae[V],K=Se[e],Y=Ur(H,n,l,K);if(0==(0|Y)){var p=0;break}Cr(b);var G=Se[i];if(Ae[G]<<24>>24==64){Se[t]=0|He.__str84254,Se[t+1]=0;var W=G+1|0;Se[i]=W}else{var Z=Pr(r,s,b,0);if(0==(0|Z)){var p=0;break}}if(0!=(4&Se[e]|0)&&(Se[t+1]=0,Se[t]=0),0==(0|a))var Q=P;else{var q=0|s,$=Se[q>>2],J=s+4|0,rr=Se[J>>2],ar=Dr(r,0|He.__str85255,(ne=Oe,Oe+=12,Se[ne>>2]=P,Se[ne+4>>2]=$,Se[ne+8>>2]=rr,ne));Se[J>>2]=0,Se[q>>2]=0;var Q=ar}var Q,er=r+44|0,ir=Se[er>>2],vr=Xr(r,b,1,40,41);if(0==(0|vr)){var p=0;break}if(0==(4096&Se[e]|0))var tr=vr,fr=X;else{Se[v]=0;var tr=0,fr=0}var fr,tr;Se[er>>2]=ir;var _r=Se[t],sr=Se[t+1];if(0==(0|_r))var nr=0;else var or=0!=(0|sr)?0:0|He.__str87257,nr=or;var nr,lr=Se[n>>2],br=0!=(0|lr)?0|He.__str87257:0,kr=Se[l>>2],ur=Dr(r,0|He.__str86256,(ne=Oe,Oe+=44,Se[ne>>2]=R,Se[ne+4>>2]=T,Se[ne+8>>2]=_r,Se[ne+12>>2]=nr,Se[ne+16>>2]=lr,Se[ne+20>>2]=br,Se[ne+24>>2]=kr,Se[ne+28>>2]=Q,Se[ne+32>>2]=tr,Se[ne+36>>2]=fr,Se[ne+40>>2]=sr,ne));Se[r+16>>2]=ur;var p=1}while(0);var p;return Oe=f,p}function Hr(r){var a,a=(r+12|0)>>2,e=Se[a];if(Ae[e]<<24>>24==36)var i=e;else{Xa(0|He.__str72242,1252,0|He.___func___handle_template,0|He.__str74244);var i=Se[a]}var i;Se[a]=i+1|0;var v=Kr(r),t=0==(0|v);do if(t)var f=0;else{var _=Xr(r,0,0,60,62);if(0==(0|_)){var f=0;break}var s=Dr(r,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=v,Se[ne+4>>2]=_,ne));Se[r+16>>2]=s;var f=1}while(0);var f;return f}function Kr(r){for(var a,a=(r+12|0)>>2,e=Me[a],i=e,v=Ae[e];;){var v,i;if(!((v-65&255&255)<26|(v-97&255&255)<26|(v-48&255&255)<10)&&v<<24>>24!=95&&v<<24>>24!=36){var t=0;break}var f=i+1|0;Se[a]=f;var _=ge[f];if(_<<24>>24==64){Se[a]=i+2|0;var s=f-e|0,n=r+20|0,o=Fr(r,e,s,n);if(0==(0|o)){var t=0;break}var l=Se[r+24>>2]-1-Se[n>>2]|0,b=Yr(n,l),t=b;break}var i=f,v=_}var t;return t}function Yr(r,a){0==(0|r)&&Xa(0|He.__str72242,263,0|He.___func___str_array_get_ref,0|He.__str75245);var e=Se[r>>2]+a|0;if(e>>>0<Me[r+8>>2]>>>0)var i=Se[Se[r+16>>2]+(e<<2)>>2];else var i=0;var i;return i}function Gr(r,a){var e,e=(r+44|0)>>2,i=Me[e];if(i>>>0>a>>>0){for(var v=r+56|0,t=a,f=0,_=Se[v>>2],s=i;;){var s,_,f,t,n=Me[_+(t<<2)>>2];if(0==(0|n)){Xa(0|He.__str72242,680,0|He.___func___get_class_string,0|He.__str106276);var o=Se[v>>2],l=o,b=Se[o+(t<<2)>>2],k=Se[e]}else var l=_,b=n,k=s;var k,b,l,u=Ca(b),c=u+(f+2)|0,h=t+1|0;if(h>>>0>=k>>>0)break;var t=h,f=c,_=l,s=k}var d=c-1|0}else var d=-1;var d,w=Wr(r,d);if(0==(0|w))var p=0;else{var E=Se[e]-1|0,A=(0|E)<(0|a);r:do if(A)var g=0;else for(var y=r+56|0,m=0,S=E;;){var S,m,M=Se[Se[y>>2]+(S<<2)>>2],C=Ca(M),R=w+m|0;Pa(R,M,C,1);var T=C+m|0;if((0|S)>(0|a)){var O=T+1|0;Ae[w+T|0]=58;var N=T+2|0;Ae[w+O|0]=58;var I=N}else var I=T;var I,P=S-1|0;if((0|P)<(0|a)){var g=I;break r}var m=I,S=P}while(0);var g;Ae[w+g|0]=0;var p=w}var p;return p}function Wr(r,a){var e,i=a>>>0>1020;do if(i){var v=Se[r+4>>2],t=a+4|0,f=pe[v](t);if(0==(0|f)){var _=0;break}var s=r+60|0,n=Se[s>>2],o=f;Se[o>>2]=n,Se[s>>2]=f,Se[r+64>>2]=0;var _=f+4|0}else{var e=(r+64|0)>>2,l=Me[e];if(l>>>0<a>>>0){var b=Se[r+4>>2],k=pe[b](1024);if(0==(0|k)){var _=0;break}var u=r+60|0,c=Se[u>>2],h=k;Se[h>>2]=c,Se[u>>2]=k,Se[e]=1020;var d=1020,w=k}else var d=l,w=Se[r+60>>2];var w,d;Se[e]=d-a|0;var _=w+(1024-d)|0}while(0);var _;return _}function Zr(r){var a=r<<24>>24;if(68==(0|a))var e=0|He.__str157327;else if(69==(0|a))var e=0|He.__str158328;else if(70==(0|a))var e=0|He.__str159329;else if(71==(0|a))var e=0|He.__str160330;else if(72==(0|a))var e=0|He.__str161331;else if(73==(0|a))var e=0|He.__str162332;else if(74==(0|a))var e=0|He.__str163333;else if(75==(0|a))var e=0|He.__str164334;else if(76==(0|a))var e=0|He.__str165335;else if(77==(0|a))var e=0|He.__str166336;else if(78==(0|a))var e=0|He.__str167337;else if(87==(0|a))var e=0|He.__str168338;else var e=0;var e;return e}function Qr(r){var a=r<<24>>24;if(67==(0|a))var e=0|He.__str145315;else if(68==(0|a))var e=0|He.__str146316;else if(69==(0|a))var e=0|He.__str147317;else if(70==(0|a))var e=0|He.__str148318;else if(71==(0|a))var e=0|He.__str149319;else if(72==(0|a))var e=0|He.__str150320;else if(73==(0|a))var e=0|He.__str151321;else if(74==(0|a))var e=0|He.__str152322;else if(75==(0|a))var e=0|He.__str153323;else if(77==(0|a))var e=0|He.__str154324;else if(78==(0|a))var e=0|He.__str155325;else if(79==(0|a))var e=0|He.__str156326;else if(88==(0|a))var e=0|He.__str84254;else if(90==(0|a))var e=0|He.__str110280;else var e=0;var e;return e}function qr(r){var a=r+44|0,e=Se[a>>2],i=zr(r);if(0==(0|i))var v=0;else var t=Gr(r,e),v=t;var v;return Se[a>>2]=e,v}function $r(r,a,e,i,v){var t,f,_,s=Oe;Oe+=16;var n,o=s,_=o>>2,l=s+4,b=s+8,f=b>>2;Se[l>>2]=0|ii;var t=(a+12|0)>>2,k=Se[t];if(Ae[k]<<24>>24==69){Se[l>>2]=0|He.__str134304;var u=k+1|0;Se[t]=u;var c=0|He.__str134304}else var c=0|ii;var c,h=i<<24>>24;do{if(65==(0|h)){var d=Dr(a,0|He.__str135305,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=d;n=10;break}if(66==(0|h)){var p=Dr(a,0|He.__str136306,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=p;n=10;break}if(80==(0|h)){var E=Dr(a,0|He.__str137307,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=E;n=10;break}if(81==(0|h)){var A=Dr(a,0|He.__str138308,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=A;n=10;break}if(82==(0|h)){var g=Dr(a,0|He.__str139309,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=g;n=10;break}if(83==(0|h)){var y=Dr(a,0|He.__str140310,(ne=Oe,Oe+=4,Se[ne>>2]=c,ne)),w=y;n=10;break}if(63==(0|h)){var w=0|ii;n=10}else n=31}while(0);r:do if(10==n){var w,m=xr(a,o,l);if(0==(0|m))break;var S=a+44|0,M=Se[S>>2],C=Se[t],R=Ae[C]<<24>>24==89;a:do if(R){var T=C+1|0;Se[t]=T;var O=Lr(a);if(0==(0|O))break r;var N=Ha(O),I=Ae[w]<<24>>24==32,P=Se[_],D=0==(0|P);do{if(I){if(!D){n=17;break}var L=w+1|0;n=18;break}if(D){var L=w;n=18;break}n=17;break}while(0);if(17==n){var F=Dr(a,0|He.__str141311,(ne=Oe,Oe+=8,Se[ne>>2]=P,Se[ne+4>>2]=w,ne));Se[_]=0;var X=F}else if(18==n)var L,j=Dr(a,0|He.__str142312,(ne=Oe,Oe+=4,Se[ne>>2]=L,ne)),X=j;var X;if(0==(0|N)){var U=X;break}for(var x=X,z=N;;){var z,x,V=z-1|0,B=Lr(a),H=Dr(a,0|He.__str143313,(ne=Oe,Oe+=8,Se[ne>>2]=x,Se[ne+4>>2]=B,ne));if(0==(0|V)){var U=H;break a}var x=H,z=V}}else var U=w;while(0);var U,K=Pr(a,b,e,0);if(0==(0|K))break;var Y=Se[_];if(0==(0|Y)){var G=0==(0|v);do if(G){if(Ae[U]<<24>>24==0){var W=U;break}var Z=U+1|0;if(Ae[Z]<<24>>24!=42){var W=U;break}var Q=Se[f],q=Ca(Q);if(Ae[Q+(q-1)|0]<<24>>24!=42){var W=U;break}var W=Z}else var W=U;while(0);var W,$=Se[f],J=Dr(a,0|He.__str170,(ne=Oe,Oe+=8,Se[ne>>2]=$,Se[ne+4>>2]=W,ne));Se[r>>2]=J}else{var rr=Se[f],ar=Dr(a,0|He.__str144314,(ne=Oe,Oe+=12,Se[ne>>2]=rr,Se[ne+4>>2]=Y,Se[ne+8>>2]=U,ne));Se[r>>2]=ar}var er=Se[f+1];Se[r+4>>2]=er,Se[S>>2]=M}while(0);Oe=s}function Jr(r){var a,e=r>>>0<245;do{if(e){if(r>>>0<11)var i=16;else var i=r+11&-8;var i,v=i>>>3,t=Me[vi>>2],f=t>>>(v>>>0);if(0!=(3&f|0)){var _=(1&f^1)+v|0,s=_<<1,n=(s<<2)+vi+40|0,o=(s+2<<2)+vi+40|0,l=Me[o>>2],b=l+8|0,k=Me[b>>2];if((0|n)==(0|k))Se[vi>>2]=t&(1<<_^-1);else{if(k>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[o>>2]=k,Se[k+12>>2]=n}var u=_<<3;Se[l+4>>2]=3|u;var c=l+(4|u)|0,h=1|Se[c>>2];Se[c>>2]=h;var d=b;a=38;break}if(i>>>0<=Me[vi+8>>2]>>>0){var w=i;a=30;break}if(0!=(0|f)){var p=2<<v,E=f<<v&(p|-p),A=(E&-E)-1|0,g=A>>>12&16,y=A>>>(g>>>0),m=y>>>5&8,S=y>>>(m>>>0),M=S>>>2&4,C=S>>>(M>>>0),R=C>>>1&2,T=C>>>(R>>>0),O=T>>>1&1,N=(m|g|M|R|O)+(T>>>(O>>>0))|0,I=N<<1,P=(I<<2)+vi+40|0,D=(I+2<<2)+vi+40|0,L=Me[D>>2],F=L+8|0,X=Me[F>>2];if((0|P)==(0|X))Se[vi>>2]=t&(1<<N^-1);else{if(X>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[D>>2]=X,Se[X+12>>2]=P}var j=N<<3,U=j-i|0;Se[L+4>>2]=3|i;var x=L,z=x+i|0;Se[x+(4|i)>>2]=1|U,Se[x+j>>2]=U;var V=Me[vi+8>>2];if(0!=(0|V)){var B=Se[vi+20>>2],H=V>>>2&1073741822,K=(H<<2)+vi+40|0,Y=Me[vi>>2],G=1<<(V>>>3),W=0==(Y&G|0);do{if(!W){var Z=(H+2<<2)+vi+40|0,Q=Me[Z>>2];if(Q>>>0>=Me[vi+16>>2]>>>0){var q=Q,$=Z;break}throw Ka(),\"Reached an unreachable!\"}Se[vi>>2]=Y|G;var q=K,$=(H+2<<2)+vi+40|0}while(0);var $,q;Se[$>>2]=B,Se[q+12>>2]=B;var J=B+8|0;Se[J>>2]=q;var rr=B+12|0;Se[rr>>2]=K}Se[vi+8>>2]=U,Se[vi+20>>2]=z;var d=F;a=38;break}if(0==(0|Se[vi+4>>2])){var w=i;a=30;break}var ar=ra(i);if(0==(0|ar)){var w=i;a=30;break}var d=ar;a=38;break}if(r>>>0>4294967231){var w=-1;a=30;break}var er=r+11&-8;if(0==(0|Se[vi+4>>2])){var w=er;a=30;break}var ir=ea(er);if(0==(0|ir)){var w=er;a=30;break}var d=ir;a=38;break}while(0);if(30==a){var w,vr=Me[vi+8>>2];if(w>>>0>vr>>>0){var tr=Me[vi+12>>2];if(w>>>0<tr>>>0){var fr=tr-w|0;Se[vi+12>>2]=fr;var _r=Me[vi+24>>2],sr=_r;Se[vi+24>>2]=sr+w|0,Se[w+(sr+4)>>2]=1|fr,Se[_r+4>>2]=3|w;var d=_r+8|0}else var nr=aa(w),d=nr}else{var or=vr-w|0,lr=Me[vi+20>>2];if(or>>>0>15){var br=lr;Se[vi+20>>2]=br+w|0,Se[vi+8>>2]=or,Se[w+(br+4)>>2]=1|or,Se[br+vr>>2]=or,Se[lr+4>>2]=3|w}else{Se[vi+8>>2]=0,Se[vi+20>>2]=0,Se[lr+4>>2]=3|vr;var kr=vr+(lr+4)|0,ur=1|Se[kr>>2];Se[kr>>2]=ur}var d=lr+8|0}}var d;return d}function ra(r){var a,e,i,v=Se[vi+4>>2],t=(v&-v)-1|0,f=t>>>12&16,_=t>>>(f>>>0),s=_>>>5&8,n=_>>>(s>>>0),o=n>>>2&4,l=n>>>(o>>>0),b=l>>>1&2,k=l>>>(b>>>0),u=k>>>1&1,c=Me[vi+((s|f|o|b|u)+(k>>>(u>>>0))<<2)+304>>2],h=c,e=h>>2,d=(Se[c+4>>2]&-8)-r|0;r:for(;;)for(var d,h,w=h;;){var w,p=Se[w+16>>2];if(0==(0|p)){var E=Se[w+20>>2];if(0==(0|E))break r;var A=E}else var A=p;var A,g=(Se[A+4>>2]&-8)-r|0;if(g>>>0<d>>>0){var h=A,e=h>>2,d=g;continue r}var w=A}var y=h,m=Me[vi+16>>2],S=y>>>0<m>>>0;do if(!S){var M=y+r|0,C=M;if(y>>>0>=M>>>0)break;var R=Me[e+6],T=Me[e+3],O=(0|T)==(0|h);do if(O){var N=h+20|0,I=Se[N>>2];if(0==(0|I)){var P=h+16|0,D=Se[P>>2];if(0==(0|D)){var L=0,a=L>>2;break}var F=P,X=D}else{var F=N,X=I;i=14}for(;;){var X,F,j=X+20|0,U=Se[j>>2];if(0==(0|U)){var x=X+16|0,z=Me[x>>2];if(0==(0|z))break;var F=x,X=z}else var F=j,X=U}if(F>>>0<m>>>0)throw Ka(),\"Reached an unreachable!\";Se[F>>2]=0;var L=X,a=L>>2}else{var V=Me[e+2];if(V>>>0<m>>>0)throw Ka(),\"Reached an unreachable!\";Se[V+12>>2]=T,Se[T+8>>2]=V;var L=T,a=L>>2}while(0);var L,B=0==(0|R);r:do if(!B){var H=h+28|0,K=(Se[H>>2]<<2)+vi+304|0,Y=(0|h)==(0|Se[K>>2]);do{if(Y){if(Se[K>>2]=L,0!=(0|L))break;var G=Se[vi+4>>2]&(1<<Se[H>>2]^-1);Se[vi+4>>2]=G;break r}if(R>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";var W=R+16|0;if((0|Se[W>>2])==(0|h)?Se[W>>2]=L:Se[R+20>>2]=L,0==(0|L))break r}while(0);if(L>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+6]=R;var Z=Me[e+4];if(0!=(0|Z)){if(Z>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+4]=Z,Se[Z+24>>2]=L}var Q=Me[e+5];if(0==(0|Q))break;if(Q>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+5]=Q,Se[Q+24>>2]=L}while(0);if(d>>>0<16){var q=d+r|0;Se[e+1]=3|q;var $=q+(y+4)|0,J=1|Se[$>>2];Se[$>>2]=J}else{Se[e+1]=3|r,Se[r+(y+4)>>2]=1|d,Se[y+d+r>>2]=d;var rr=Me[vi+8>>2];if(0!=(0|rr)){var ar=Me[vi+20>>2],er=rr>>>2&1073741822,ir=(er<<2)+vi+40|0,vr=Me[vi>>2],tr=1<<(rr>>>3),fr=0==(vr&tr|0);do{if(!fr){var _r=(er+2<<2)+vi+40|0,sr=Me[_r>>2];if(sr>>>0>=Me[vi+16>>2]>>>0){var nr=sr,or=_r;break}throw Ka(),\"Reached an unreachable!\"}Se[vi>>2]=vr|tr;var nr=ir,or=(er+2<<2)+vi+40|0}while(0);var or,nr;Se[or>>2]=ar,Se[nr+12>>2]=ar,Se[ar+8>>2]=nr,Se[ar+12>>2]=ir}Se[vi+8>>2]=d,Se[vi+20>>2]=C}return h+8|0}while(0);throw Ka(),\"Reached an unreachable!\"}function aa(r){var a,e;0==(0|Se[ti>>2])&&ba();var i=0==(4&Se[vi+440>>2]|0);do{if(i){var v=Se[vi+24>>2],t=0==(0|v);do{if(!t){var f=v,_=ua(f);if(0==(0|_)){e=6;break}var s=Se[ti+8>>2],n=r+47-Se[vi+12>>2]+s&-s;if(n>>>0>=2147483647){e=14;break}var o=re(n);if((0|o)==(Se[_>>2]+Se[_+4>>2]|0)){var l=o,b=n,k=o;e=13;break}var u=o,c=n;e=15;break}e=6}while(0);do if(6==e){var h=re(0);if((0|h)==-1){e=14;break}var d=Se[ti+8>>2],w=d+(r+47)&-d,p=h,E=Se[ti+4>>2],A=E-1|0;if(0==(A&p|0))var g=w;else var g=w-p+(A+p&-E)|0;var g;if(g>>>0>=2147483647){e=14;break}var y=re(g);if((0|y)==(0|h)){var l=h,b=g,k=y;e=13;break}var u=y,c=g;e=15;break}while(0);if(13==e){var k,b,l;if((0|l)!=-1){var m=b,S=l;e=26;break}var u=k,c=b}else if(14==e){var M=4|Se[vi+440>>2];Se[vi+440>>2]=M,e=23;break}var c,u,C=0|-c,R=(0|u)!=-1&c>>>0<2147483647;do{if(R){if(c>>>0>=(r+48|0)>>>0){var T=c;e=21;break}var O=Se[ti+8>>2],N=r+47-c+O&-O;if(N>>>0>=2147483647){var T=c;e=21;break}var I=re(N);if((0|I)==-1){re(C);e=22;break}var T=N+c|0;e=21;break}var T=c;e=21}while(0);if(21==e){var T;if((0|u)!=-1){var m=T,S=u;e=26;break}}var P=4|Se[vi+440>>2];Se[vi+440>>2]=P,e=23;break}e=23}while(0);do if(23==e){var D=Se[ti+8>>2],L=D+(r+47)&-D;if(L>>>0>=2147483647){e=49;break}var F=re(L),X=re(0);if(!((0|X)!=-1&(0|F)!=-1&F>>>0<X>>>0)){e=49;break}var j=X-F|0;if(j>>>0<=(r+40|0)>>>0|(0|F)==-1){e=49;break}var m=j,S=F;e=26;break}while(0);r:do if(26==e){var S,m,U=Se[vi+432>>2]+m|0;Se[vi+432>>2]=U,U>>>0>Me[vi+436>>2]>>>0&&(Se[vi+436>>2]=U);var x=Me[vi+24>>2],z=0==(0|x);a:do if(z){var V=Me[vi+16>>2];0==(0|V)|S>>>0<V>>>0&&(Se[vi+16>>2]=S),Se[vi+444>>2]=S,Se[vi+448>>2]=m,Se[vi+456>>2]=0;var B=Se[ti>>2];Se[vi+36>>2]=B,Se[vi+32>>2]=-1,ha(),ca(S,m-40|0)}else{for(var H=vi+444|0,a=H>>2;;){var H;if(0==(0|H))break;var K=Me[a],Y=H+4|0,G=Me[Y>>2],W=K+G|0;if((0|S)==(0|W)){if(0!=(8&Se[a+3]|0))break;var Z=x;if(!(Z>>>0>=K>>>0&Z>>>0<W>>>0))break;Se[Y>>2]=G+m|0;var Q=Se[vi+24>>2],q=Se[vi+12>>2]+m|0;ca(Q,q);break a}var H=Se[a+2],a=H>>2}S>>>0<Me[vi+16>>2]>>>0&&(Se[vi+16>>2]=S);for(var $=S+m|0,J=vi+444|0;;){var J;if(0==(0|J))break;var rr=0|J,ar=Me[rr>>2];if((0|ar)==(0|$)){if(0!=(8&Se[J+12>>2]|0))break;Se[rr>>2]=S;var er=J+4|0,ir=Se[er>>2]+m|0;Se[er>>2]=ir;var vr=da(S,ar,r),tr=vr;e=50;break r}var J=Se[J+8>>2]}Ma(S,m)}while(0);var fr=Me[vi+12>>2];if(fr>>>0<=r>>>0){e=49;break}var _r=fr-r|0;Se[vi+12>>2]=_r;var sr=Me[vi+24>>2],nr=sr;Se[vi+24>>2]=nr+r|0,Se[r+(nr+4)>>2]=1|_r,Se[sr+4>>2]=3|r;var tr=sr+8|0;e=50;break}while(0);if(49==e){var or=Je();Se[or>>2]=12;var tr=0}var tr;return tr}function ea(r){var a,e,i,v,t,f,_=r>>2,s=0|-r,n=r>>>8,o=0==(0|n);do if(o)var l=0;else{if(r>>>0>16777215){var l=31;break}var b=(n+1048320|0)>>>16&8,k=n<<b,u=(k+520192|0)>>>16&4,c=k<<u,h=(c+245760|0)>>>16&2,d=14-(u|b|h)+(c<<h>>>15)|0,l=r>>>((d+7|0)>>>0)&1|d<<1}while(0);var l,w=Me[vi+(l<<2)+304>>2],p=0==(0|w);r:do if(p)var E=0,A=s,g=0;else{if(31==(0|l))var y=0;else var y=25-(l>>>1)|0;for(var y,m=0,S=s,M=w,t=M>>2,C=r<<y,R=0;;){var R,C,M,S,m,T=Se[t+1]&-8,O=T-r|0;if(O>>>0<S>>>0){if((0|T)==(0|r)){var E=M,A=O,g=M;break r}var N=M,I=O}else var N=m,I=S;var I,N,P=Me[t+5],D=Me[((C>>>31<<2)+16>>2)+t],L=0==(0|P)|(0|P)==(0|D)?R:P;if(0==(0|D)){var E=N,A=I,g=L;break r}var m=N,S=I,M=D,t=M>>2,C=C<<1,R=L}}while(0);var g,A,E,F=0==(0|g)&0==(0|E);do if(F){var X=2<<l,j=Se[vi+4>>2]&(X|-X);if(0==(0|j)){var U=g;break}var x=(j&-j)-1|0,z=x>>>12&16,V=x>>>(z>>>0),B=V>>>5&8,H=V>>>(B>>>0),K=H>>>2&4,Y=H>>>(K>>>0),G=Y>>>1&2,W=Y>>>(G>>>0),Z=W>>>1&1,U=Se[vi+((B|z|K|G|Z)+(W>>>(Z>>>0))<<2)+304>>2]}else var U=g;while(0);var U,Q=0==(0|U);r:do if(Q)var q=A,$=E,v=$>>2;else for(var J=U,i=J>>2,rr=A,ar=E;;){var ar,rr,J,er=(Se[i+1]&-8)-r|0,ir=er>>>0<rr>>>0,vr=ir?er:rr,tr=ir?J:ar,fr=Me[i+4];if(0==(0|fr)){var _r=Me[i+5];if(0==(0|_r)){var q=vr,$=tr,v=$>>2;break r}var J=_r,i=J>>2,rr=vr,ar=tr}else var J=fr,i=J>>2,rr=vr,ar=tr}while(0);var $,q,sr=0==(0|$);r:do{if(!sr){if(q>>>0>=(Se[vi+8>>2]-r|0)>>>0){var nr=0;break}var or=$,e=or>>2,lr=Me[vi+16>>2],br=or>>>0<lr>>>0;do if(!br){var kr=or+r|0,ur=kr;if(or>>>0>=kr>>>0)break;var cr=Me[v+6],hr=Me[v+3],dr=(0|hr)==(0|$);do if(dr){var wr=$+20|0,pr=Se[wr>>2];if(0==(0|pr)){var Er=$+16|0,Ar=Se[Er>>2];if(0==(0|Ar)){var gr=0,a=gr>>2;break}var yr=Er,mr=Ar}else{var yr=wr,mr=pr;f=28}for(;;){var mr,yr,Sr=mr+20|0,Mr=Se[Sr>>2];if(0==(0|Mr)){var Cr=mr+16|0,Rr=Me[Cr>>2];if(0==(0|Rr))break;var yr=Cr,mr=Rr}else var yr=Sr,mr=Mr}if(yr>>>0<lr>>>0)throw Ka(),\"Reached an unreachable!\";Se[yr>>2]=0;var gr=mr,a=gr>>2}else{var Tr=Me[v+2];if(Tr>>>0<lr>>>0)throw Ka(),\"Reached an unreachable!\";Se[Tr+12>>2]=hr,Se[hr+8>>2]=Tr;var gr=hr,a=gr>>2}while(0);var gr,Or=0==(0|cr);a:do if(!Or){var Nr=$+28|0,Ir=(Se[Nr>>2]<<2)+vi+304|0,Pr=(0|$)==(0|Se[Ir>>2]);do{if(Pr){if(Se[Ir>>2]=gr,0!=(0|gr))break;var Dr=Se[vi+4>>2]&(1<<Se[Nr>>2]^-1);Se[vi+4>>2]=Dr;break a}if(cr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";var Lr=cr+16|0;if((0|Se[Lr>>2])==(0|$)?Se[Lr>>2]=gr:Se[cr+20>>2]=gr,0==(0|gr))break a}while(0);if(gr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+6]=cr;var Fr=Me[v+4];if(0!=(0|Fr)){if(Fr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+4]=Fr,Se[Fr+24>>2]=gr}var Xr=Me[v+5];if(0==(0|Xr))break;if(Xr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+5]=Xr,Se[Xr+24>>2]=gr}while(0);var jr=q>>>0<16;a:do if(jr){var Ur=q+r|0;Se[v+1]=3|Ur;var xr=Ur+(or+4)|0,zr=1|Se[xr>>2];Se[xr>>2]=zr}else if(Se[v+1]=3|r,Se[_+(e+1)]=1|q,Se[(q>>2)+e+_]=q,q>>>0<256){var Vr=q>>>2&1073741822,Br=(Vr<<2)+vi+40|0,Hr=Me[vi>>2],Kr=1<<(q>>>3),Yr=0==(Hr&Kr|0);do{if(!Yr){var Gr=(Vr+2<<2)+vi+40|0,Wr=Me[Gr>>2];if(Wr>>>0>=Me[vi+16>>2]>>>0){var Zr=Wr,Qr=Gr;break}throw Ka(),\"Reached an unreachable!\"}Se[vi>>2]=Hr|Kr;var Zr=Br,Qr=(Vr+2<<2)+vi+40|0}while(0);var Qr,Zr;Se[Qr>>2]=ur,Se[Zr+12>>2]=ur,Se[_+(e+2)]=Zr,Se[_+(e+3)]=Br}else{var qr=kr,$r=q>>>8,Jr=0==(0|$r);do if(Jr)var ra=0;else{if(q>>>0>16777215){var ra=31;break}var aa=($r+1048320|0)>>>16&8,ea=$r<<aa,ia=(ea+520192|0)>>>16&4,va=ea<<ia,ta=(va+245760|0)>>>16&2,fa=14-(ia|aa|ta)+(va<<ta>>>15)|0,ra=q>>>((fa+7|0)>>>0)&1|fa<<1}while(0);var ra,_a=(ra<<2)+vi+304|0;Se[_+(e+7)]=ra;var sa=r+(or+16)|0;Se[_+(e+5)]=0,Se[sa>>2]=0;var na=Se[vi+4>>2],oa=1<<ra;if(0==(na&oa|0)){var la=na|oa;Se[vi+4>>2]=la,Se[_a>>2]=qr,Se[_+(e+6)]=_a,Se[_+(e+3)]=qr,Se[_+(e+2)]=qr}else{if(31==(0|ra))var ba=0;else var ba=25-(ra>>>1)|0;for(var ba,ka=q<<ba,ua=Se[_a>>2];;){var ua,ka;if((Se[ua+4>>2]&-8|0)==(0|q)){var ca=ua+8|0,ha=Me[ca>>2],da=Me[vi+16>>2],wa=ua>>>0<da>>>0;do if(!wa){if(ha>>>0<da>>>0)break;Se[ha+12>>2]=qr,Se[ca>>2]=qr,Se[_+(e+2)]=ha,Se[_+(e+3)]=ua,Se[_+(e+6)]=0;break a}while(0);throw Ka(),\"Reached an unreachable!\"}var pa=(ka>>>31<<2)+ua+16|0,Ea=Me[pa>>2];if(0==(0|Ea)){if(pa>>>0>=Me[vi+16>>2]>>>0){Se[pa>>2]=qr,Se[_+(e+6)]=ua,Se[_+(e+3)]=qr,Se[_+(e+2)]=qr;break a}throw Ka(),\"Reached an unreachable!\"}var ka=ka<<1,ua=Ea}}}while(0);var nr=$+8|0;break r}while(0);throw Ka(),\"Reached an unreachable!\"}var nr=0}while(0);var nr;return nr}function ia(r){var a;0==(0|Se[ti>>2])&&ba();var e=r>>>0<4294967232;r:do if(e){var i=Me[vi+24>>2];if(0==(0|i)){var v=0;break}var t=Me[vi+12>>2],f=t>>>0>(r+40|0)>>>0;do if(f){var _=Me[ti+8>>2],s=-40-r-1+t+_|0,n=Math.floor((s>>>0)/(_>>>0)),o=(n-1)*_|0,l=i,b=ua(l);if(0!=(8&Se[b+12>>2]|0))break;var k=re(0),a=(b+4|0)>>2;if((0|k)!=(Se[b>>2]+Se[a]|0))break;var u=o>>>0>2147483646?-2147483648-_|0:o,c=0|-u,h=re(c),d=re(0);if(!((0|h)!=-1&d>>>0<k>>>0))break;var w=k-d|0;if((0|k)==(0|d))break;var p=Se[a]-w|0;Se[a]=p;var E=Se[vi+432>>2]-w|0;Se[vi+432>>2]=E;var A=Se[vi+24>>2],g=Se[vi+12>>2]-w|0;ca(A,g);var v=(0|k)!=(0|d);break r}while(0);if(Me[vi+12>>2]>>>0<=Me[vi+28>>2]>>>0){var v=0;break}Se[vi+28>>2]=-1;var v=0}else var v=0;while(0);var v;return 1&v}function va(r){var a,e,i,v,t,f,_,s=r>>2,n=0==(0|r);r:do if(!n){var o=r-8|0,l=o,b=Me[vi+16>>2],k=o>>>0<b>>>0;a:do if(!k){var u=Me[r-4>>2],c=3&u;if(1==(0|c))break;var h=u&-8,f=h>>2,d=r+(h-8)|0,w=d,p=0==(1&u|0);e:do if(p){var E=Me[o>>2];if(0==(0|c))break r;var A=-8-E|0,t=A>>2,g=r+A|0,y=g,m=E+h|0;if(g>>>0<b>>>0)break a;if((0|y)==(0|Se[vi+20>>2])){var v=(r+(h-4)|0)>>2;if(3!=(3&Se[v]|0)){var S=y,i=S>>2,M=m;break}Se[vi+8>>2]=m;var C=Se[v]&-2;Se[v]=C,Se[t+(s+1)]=1|m,Se[d>>2]=m;break r}if(E>>>0<256){var R=Me[t+(s+2)],T=Me[t+(s+3)];if((0|R)!=(0|T)){var O=((E>>>2&1073741822)<<2)+vi+40|0,N=(0|R)!=(0|O)&R>>>0<b>>>0;do if(!N){if(!((0|T)==(0|O)|T>>>0>=b>>>0))break;Se[R+12>>2]=T,Se[T+8>>2]=R;var S=y,i=S>>2,M=m;break e}while(0);throw Ka(),\"Reached an unreachable!\"}var I=Se[vi>>2]&(1<<(E>>>3)^-1);Se[vi>>2]=I;var S=y,i=S>>2,M=m}else{var P=g,D=Me[t+(s+6)],L=Me[t+(s+3)],F=(0|L)==(0|P);do if(F){var X=A+(r+20)|0,j=Se[X>>2];if(0==(0|j)){var U=A+(r+16)|0,x=Se[U>>2];if(0==(0|x)){var z=0,e=z>>2;break}var V=U,B=x}else{var V=X,B=j;_=21}for(;;){var B,V,H=B+20|0,K=Se[H>>2];if(0==(0|K)){var Y=B+16|0,G=Me[Y>>2];if(0==(0|G))break;var V=Y,B=G}else var V=H,B=K}if(V>>>0<b>>>0)throw Ka(),\"Reached an unreachable!\";Se[V>>2]=0;var z=B,e=z>>2}else{var W=Me[t+(s+2)];if(W>>>0<b>>>0)throw Ka(),\"Reached an unreachable!\";Se[W+12>>2]=L,Se[L+8>>2]=W;var z=L,e=z>>2}while(0);var z;if(0==(0|D)){var S=y,i=S>>2,M=m;break}var Z=A+(r+28)|0,Q=(Se[Z>>2]<<2)+vi+304|0,q=(0|P)==(0|Se[Q>>2]);do{if(q){if(Se[Q>>2]=z,0!=(0|z))break;var $=Se[vi+4>>2]&(1<<Se[Z>>2]^-1);Se[vi+4>>2]=$;var S=y,i=S>>2,M=m;break e}if(D>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";var J=D+16|0;if((0|Se[J>>2])==(0|P)?Se[J>>2]=z:Se[D+20>>2]=z,0==(0|z)){var S=y,i=S>>2,M=m;break e}}while(0);if(z>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[e+6]=D;var rr=Me[t+(s+4)];if(0!=(0|rr)){if(rr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[e+4]=rr,Se[rr+24>>2]=z}var ar=Me[t+(s+5)];if(0==(0|ar)){var S=y,i=S>>2,M=m;break}if(ar>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[e+5]=ar,Se[ar+24>>2]=z;var S=y,i=S>>2,M=m}}else var S=l,i=S>>2,M=h;while(0);var M,S,er=S;if(er>>>0>=d>>>0)break;var ir=r+(h-4)|0,vr=Me[ir>>2];if(0==(1&vr|0))break;var tr=0==(2&vr|0);do{if(tr){if((0|w)==(0|Se[vi+24>>2])){var fr=Se[vi+12>>2]+M|0;Se[vi+12>>2]=fr,Se[vi+24>>2]=S;var _r=1|fr;if(Se[i+1]=_r,(0|S)==(0|Se[vi+20>>2])&&(Se[vi+20>>2]=0,Se[vi+8>>2]=0),fr>>>0<=Me[vi+28>>2]>>>0)break r;ia(0);break r}if((0|w)==(0|Se[vi+20>>2])){var sr=Se[vi+8>>2]+M|0;Se[vi+8>>2]=sr,Se[vi+20>>2]=S;var nr=1|sr;Se[i+1]=nr;var or=er+sr|0;Se[or>>2]=sr;break r}var lr=(vr&-8)+M|0,br=vr>>>3,kr=vr>>>0<256;e:do if(kr){var ur=Me[s+f],cr=Me[((4|h)>>2)+s];if((0|ur)!=(0|cr)){var hr=((vr>>>2&1073741822)<<2)+vi+40|0,dr=(0|ur)==(0|hr);do{if(!dr){if(ur>>>0<Me[vi+16>>2]>>>0){_=66;break}_=63;break}_=63}while(0);do if(63==_){if((0|cr)!=(0|hr)&&cr>>>0<Me[vi+16>>2]>>>0)break;Se[ur+12>>2]=cr,Se[cr+8>>2]=ur;break e}while(0);throw Ka(),\"Reached an unreachable!\"}var wr=Se[vi>>2]&(1<<br^-1);Se[vi>>2]=wr}else{var pr=d,Er=Me[f+(s+4)],Ar=Me[((4|h)>>2)+s],gr=(0|Ar)==(0|pr);do if(gr){var yr=h+(r+12)|0,mr=Se[yr>>2];if(0==(0|mr)){var Sr=h+(r+8)|0,Mr=Se[Sr>>2];if(0==(0|Mr)){var Cr=0,a=Cr>>2;break}var Rr=Sr,Tr=Mr}else{var Rr=yr,Tr=mr;_=73}for(;;){var Tr,Rr,Or=Tr+20|0,Nr=Se[Or>>2];if(0==(0|Nr)){var Ir=Tr+16|0,Pr=Me[Ir>>2];if(0==(0|Pr))break;var Rr=Ir,Tr=Pr}else var Rr=Or,Tr=Nr}if(Rr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[Rr>>2]=0;var Cr=Tr,a=Cr>>2}else{var Dr=Me[s+f];if(Dr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[Dr+12>>2]=Ar,\nSe[Ar+8>>2]=Dr;var Cr=Ar,a=Cr>>2}while(0);var Cr;if(0==(0|Er))break;var Lr=h+(r+20)|0,Fr=(Se[Lr>>2]<<2)+vi+304|0,Xr=(0|pr)==(0|Se[Fr>>2]);do{if(Xr){if(Se[Fr>>2]=Cr,0!=(0|Cr))break;var jr=Se[vi+4>>2]&(1<<Se[Lr>>2]^-1);Se[vi+4>>2]=jr;break e}if(Er>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";var Ur=Er+16|0;if((0|Se[Ur>>2])==(0|pr)?Se[Ur>>2]=Cr:Se[Er+20>>2]=Cr,0==(0|Cr))break e}while(0);if(Cr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+6]=Er;var xr=Me[f+(s+2)];if(0!=(0|xr)){if(xr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+4]=xr,Se[xr+24>>2]=Cr}var zr=Me[f+(s+3)];if(0==(0|zr))break;if(zr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[a+5]=zr,Se[zr+24>>2]=Cr}while(0);if(Se[i+1]=1|lr,Se[er+lr>>2]=lr,(0|S)!=(0|Se[vi+20>>2])){var Vr=lr;break}Se[vi+8>>2]=lr;break r}Se[ir>>2]=vr&-2,Se[i+1]=1|M,Se[er+M>>2]=M;var Vr=M}while(0);var Vr;if(Vr>>>0<256){var Br=Vr>>>2&1073741822,Hr=(Br<<2)+vi+40|0,Kr=Me[vi>>2],Yr=1<<(Vr>>>3),Gr=0==(Kr&Yr|0);do{if(!Gr){var Wr=(Br+2<<2)+vi+40|0,Zr=Me[Wr>>2];if(Zr>>>0>=Me[vi+16>>2]>>>0){var Qr=Zr,qr=Wr;break}throw Ka(),\"Reached an unreachable!\"}Se[vi>>2]=Kr|Yr;var Qr=Hr,qr=(Br+2<<2)+vi+40|0}while(0);var qr,Qr;Se[qr>>2]=S,Se[Qr+12>>2]=S,Se[i+2]=Qr,Se[i+3]=Hr;break r}var $r=S,Jr=Vr>>>8,ra=0==(0|Jr);do if(ra)var aa=0;else{if(Vr>>>0>16777215){var aa=31;break}var ea=(Jr+1048320|0)>>>16&8,va=Jr<<ea,fa=(va+520192|0)>>>16&4,_a=va<<fa,sa=(_a+245760|0)>>>16&2,na=14-(fa|ea|sa)+(_a<<sa>>>15)|0,aa=Vr>>>((na+7|0)>>>0)&1|na<<1}while(0);var aa,oa=(aa<<2)+vi+304|0;Se[i+7]=aa,Se[i+5]=0,Se[i+4]=0;var la=Se[vi+4>>2],ba=1<<aa,ka=0==(la&ba|0);e:do if(ka){var ua=la|ba;Se[vi+4>>2]=ua,Se[oa>>2]=$r,Se[i+6]=oa,Se[i+3]=S,Se[i+2]=S}else{if(31==(0|aa))var ca=0;else var ca=25-(aa>>>1)|0;for(var ca,ha=Vr<<ca,da=Se[oa>>2];;){var da,ha;if((Se[da+4>>2]&-8|0)==(0|Vr)){var wa=da+8|0,pa=Me[wa>>2],Ea=Me[vi+16>>2],Aa=da>>>0<Ea>>>0;do if(!Aa){if(pa>>>0<Ea>>>0)break;Se[pa+12>>2]=$r,Se[wa>>2]=$r,Se[i+2]=pa,Se[i+3]=da,Se[i+6]=0;break e}while(0);throw Ka(),\"Reached an unreachable!\"}var ga=(ha>>>31<<2)+da+16|0,ya=Me[ga>>2];if(0==(0|ya)){if(ga>>>0>=Me[vi+16>>2]>>>0){Se[ga>>2]=$r,Se[i+6]=da,Se[i+3]=S,Se[i+2]=S;break e}throw Ka(),\"Reached an unreachable!\"}var ha=ha<<1,da=ya}}while(0);var ma=Se[vi+32>>2]-1|0;if(Se[vi+32>>2]=ma,0!=(0|ma))break r;ta();break r}while(0);throw Ka(),\"Reached an unreachable!\"}while(0)}function ta(){var r=Se[vi+452>>2],a=0==(0|r);r:do if(!a)for(var e=r;;){var e,i=Se[e+8>>2];if(0==(0|i))break r;var e=i}while(0);Se[vi+32>>2]=-1}function fa(r,a){if(0==(0|r))var e=Jr(a),i=e;else var v=la(r,a),i=v;var i;return i}function _a(r,a){var e,i=r>>>0<9;do if(i)var v=Jr(a),t=v;else{var f=r>>>0<16?16:r,_=0==(f-1&f|0);r:do if(_)var s=f;else{if(f>>>0<=16){var s=16;break}for(var n=16;;){var n,o=n<<1;if(o>>>0>=f>>>0){var s=o;break r}var n=o}}while(0);var s;if((-64-s|0)>>>0>a>>>0){if(a>>>0<11)var l=16;else var l=a+11&-8;var l,b=Jr(l+(s+12)|0);if(0==(0|b)){var t=0;break}var k=b-8|0;if(0==((b>>>0)%(s>>>0)|0))var u=k,c=0;else{var h=b+(s-1)&-s,d=h-8|0,w=k;if((d-w|0)>>>0>15)var p=d;else var p=h+(s-8)|0;var p,E=p-w|0,e=(b-4|0)>>2,A=Se[e],g=(A&-8)-E|0;if(0==(3&A|0)){var y=Se[k>>2]+E|0;Se[p>>2]=y,Se[p+4>>2]=g;var u=p,c=0}else{var m=p+4|0,S=g|1&Se[m>>2]|2;Se[m>>2]=S;var M=g+(p+4)|0,C=1|Se[M>>2];Se[M>>2]=C;var R=E|1&Se[e]|2;Se[e]=R;var T=b+(E-4)|0,O=1|Se[T>>2];Se[T>>2]=O;var u=p,c=b}}var c,u,N=u+4|0,I=Me[N>>2],P=0==(3&I|0);do if(P)var D=0;else{var L=I&-8;if(L>>>0<=(l+16|0)>>>0){var D=0;break}var F=L-l|0;Se[N>>2]=l|1&I|2,Se[u+(4|l)>>2]=3|F;var X=u+(4|L)|0,j=1|Se[X>>2];Se[X>>2]=j;var D=l+(u+8)|0}while(0);var D;0!=(0|c)&&va(c),0!=(0|D)&&va(D);var t=u+8|0}else{var U=Je();Se[U>>2]=12;var t=0}}while(0);var t;return t}function sa(r,a,e,i){var v,t;0==(0|Se[ti>>2])&&ba();var f=0==(0|i),_=0==(0|r);do{if(f){if(_){var s=Jr(0),n=s;t=30;break}var o=r<<2;if(o>>>0<11){var l=0,b=16;t=9;break}var l=0,b=o+11&-8;t=9;break}if(_){var n=i;t=30;break}var l=i,b=0;t=9;break}while(0);do if(9==t){var b,l,k=0==(1&e|0);r:do if(k){if(_){var u=0,c=0;break}for(var h=0,d=0;;){var d,h,w=Me[a+(d<<2)>>2];if(w>>>0<11)var p=16;else var p=w+11&-8;var p,E=p+h|0,A=d+1|0;if((0|A)==(0|r)){var u=0,c=E;break r}var h=E,d=A}}else{var g=Me[a>>2];if(g>>>0<11)var y=16;else var y=g+11&-8;var y,u=y,c=y*r|0}while(0);var c,u,m=Jr(b-4+c|0);if(0==(0|m)){var n=0;break}var S=m-8|0,M=Se[m-4>>2]&-8;if(0!=(2&e|0)){var C=-4-b+M|0;Fa(m,0,C,1)}if(0==(0|l)){var R=m+c|0,T=M-c|3;Se[m+(c-4)>>2]=T;var O=R,v=O>>2,N=c}else var O=l,v=O>>2,N=M;var N,O;Se[v]=m;var I=r-1|0,P=0==(0|I);r:do if(P)var D=S,L=N;else if(0==(0|u))for(var F=S,X=N,j=0;;){var j,X,F,U=Me[a+(j<<2)>>2];if(U>>>0<11)var x=16;else var x=U+11&-8;var x,z=X-x|0;Se[F+4>>2]=3|x;var V=F+x|0,B=j+1|0;if(Se[(B<<2>>2)+v]=x+(F+8)|0,(0|B)==(0|I)){var D=V,L=z;break r}var F=V,X=z,j=B}else for(var H=3|u,K=u+8|0,Y=S,G=N,W=0;;){var W,G,Y,Z=G-u|0;Se[Y+4>>2]=H;var Q=Y+u|0,q=W+1|0;if(Se[(q<<2>>2)+v]=Y+K|0,(0|q)==(0|I)){var D=Q,L=Z;break r}var Y=Q,G=Z,W=q}while(0);var L,D;Se[D+4>>2]=3|L;var n=O}while(0);var n;return n}function na(r){var a=r>>2;0==(0|Se[ti>>2])&&ba();var e=Me[vi+24>>2];if(0==(0|e))var i=0,v=0,t=0,f=0,_=0,s=0,n=0;else{for(var o=Me[vi+12>>2],l=o+40|0,b=vi+444|0,k=l,u=l,c=1;;){var c,u,k,b,h=Me[b>>2],d=h+8|0;if(0==(7&d|0))var w=0;else var w=7&-d;for(var w,p=b+4|0,E=h+w|0,A=c,g=u,y=k;;){var y,g,A,E;if(E>>>0<h>>>0)break;if(E>>>0>=(h+Se[p>>2]|0)>>>0|(0|E)==(0|e))break;var m=Se[E+4>>2];if(7==(0|m))break;var S=m&-8,M=S+y|0;if(1==(3&m|0))var C=A+1|0,R=S+g|0;else var C=A,R=g;var R,C,E=E+S|0,A=C,g=R,y=M}var T=Me[b+8>>2];if(0==(0|T))break;var b=T,k=y,u=g,c=A}var O=Se[vi+432>>2],i=y,v=A,t=o,f=g,_=O-y|0,s=Se[vi+436>>2],n=O-g|0}var n,s,_,f,t,v,i;Se[a]=i,Se[a+1]=v,Se[a+2]=0,Se[a+3]=0,Se[a+4]=_,Se[a+5]=s,Se[a+6]=0,Se[a+7]=n,Se[a+8]=f,Se[a+9]=t}function oa(){0==(0|Se[ti>>2])&&ba();var r=Me[vi+24>>2],a=0==(0|r);r:do if(a)var e=0,i=0,v=0;else for(var t=Se[vi+436>>2],f=Me[vi+432>>2],_=vi+444|0,s=f-40-Se[vi+12>>2]|0;;){var s,_,n=Me[_>>2],o=n+8|0;if(0==(7&o|0))var l=0;else var l=7&-o;for(var l,b=_+4|0,k=n+l|0,u=s;;){var u,k;if(k>>>0<n>>>0)break;if(k>>>0>=(n+Se[b>>2]|0)>>>0|(0|k)==(0|r))break;var c=Se[k+4>>2];if(7==(0|c))break;var h=c&-8,d=1==(3&c|0)?h:0,w=u-d|0,k=k+h|0,u=w}var p=Me[_+8>>2];if(0==(0|p)){var e=t,i=f,v=u;break r}var _=p,s=u}while(0);var v,i,e,E=Se[Se[qe>>2]+12>>2],A=(Qa(E,0|He.__str339,(ne=Oe,Oe+=4,Se[ne>>2]=e,ne)),Se[Se[qe>>2]+12>>2]),g=(Qa(A,0|He.__str1340,(ne=Oe,Oe+=4,Se[ne>>2]=i,ne)),Se[Se[qe>>2]+12>>2]);Qa(g,0|He.__str2341,(ne=Oe,Oe+=4,Se[ne>>2]=v,ne))}function la(r,a){var e,i,v,t=a>>>0>4294967231;r:do{if(!t){var f=r-8|0,_=f,i=(r-4|0)>>2,s=Me[i],n=s&-8,o=n-8|0,l=r+o|0,b=f>>>0<Me[vi+16>>2]>>>0;do if(!b){var k=3&s;if(!(1!=(0|k)&(0|o)>-8))break;var e=(r+(n-4)|0)>>2;if(0==(1&Se[e]|0))break;if(a>>>0<11)var u=16;else var u=a+11&-8;var u,c=0==(0|k);do{if(c){var h=ka(_,u),d=0,w=h;v=17;break}if(n>>>0<u>>>0){if((0|l)!=(0|Se[vi+24>>2])){v=21;break}var p=Se[vi+12>>2]+n|0;if(p>>>0<=u>>>0){v=21;break}var E=p-u|0,A=r+(u-8)|0;Se[i]=u|1&s|2;var g=1|E;Se[r+(u-4)>>2]=g,Se[vi+24>>2]=A,Se[vi+12>>2]=E;var d=0,w=_;v=17;break}var y=n-u|0;if(y>>>0<=15){var d=0,w=_;v=17;break}Se[i]=u|1&s|2,Se[r+(u-4)>>2]=3|y;var m=1|Se[e];Se[e]=m;var d=r+u|0,w=_;v=17;break}while(0);do if(17==v){var w,d;if(0==(0|w))break;0!=(0|d)&&va(d);var S=w+8|0;break r}while(0);var M=Jr(a);if(0==(0|M)){var S=0;break r}var C=0==(3&Se[i]|0)?8:4,R=n-C|0,T=R>>>0<a>>>0?R:a;Pa(M,r,T,1),va(r);var S=M;break r}while(0);throw Ka(),\"Reached an unreachable!\"}var O=Je();Se[O>>2]=12;var S=0}while(0);var S;return S}function ba(){if(0==(0|Se[ti>>2])){var r=qa(8);if(0!=(r-1&r|0))throw Ka(),\"Reached an unreachable!\";Se[ti+8>>2]=r,Se[ti+4>>2]=r,Se[ti+12>>2]=-1,Se[ti+16>>2]=2097152,Se[ti+20>>2]=0,Se[vi+440>>2]=0;var a=$a(0);Se[ti>>2]=a&-16^1431655768}}function ka(r,a){var e=Se[r+4>>2]&-8,i=a>>>0<256;do if(i)var v=0;else{if(e>>>0>=(a+4|0)>>>0&&(e-a|0)>>>0<=Se[ti+8>>2]<<1>>>0){var v=r;break}var v=0}while(0);var v;return v}function ua(r){for(var a,e=vi+444|0,a=e>>2;;){var e,i=Me[a];if(i>>>0<=r>>>0&&(i+Se[a+1]|0)>>>0>r>>>0){var v=e;break}var t=Me[a+2];if(0==(0|t)){var v=0;break}var e=t,a=e>>2}var v;return v}function ca(r,a){var e=r,i=r+8|0;if(0==(7&i|0))var v=0;else var v=7&-i;var v,t=a-v|0;Se[vi+24>>2]=e+v|0,Se[vi+12>>2]=t,Se[v+(e+4)>>2]=1|t,Se[a+(e+4)>>2]=40;var f=Se[ti+16>>2];Se[vi+28>>2]=f}function ha(){for(var r=0;;){var r,a=r<<1,e=(a<<2)+vi+40|0;Se[vi+(a+3<<2)+40>>2]=e,Se[vi+(a+2<<2)+40>>2]=e;var i=r+1|0;if(32==(0|i))break;var r=i}}function da(r,a,e){var i,v,t,f,_=a>>2,s=r>>2,n=r+8|0;if(0==(7&n|0))var o=0;else var o=7&-n;var o,l=a+8|0;if(0==(7&l|0))var b=0,t=b>>2;else var b=7&-l,t=b>>2;var b,k=a+b|0,u=k,c=o+e|0,v=c>>2,h=r+c|0,d=h,w=k-(r+o)-e|0;Se[(o+4>>2)+s]=3|e;var p=(0|u)==(0|Se[vi+24>>2]);r:do if(p){var E=Se[vi+12>>2]+w|0;Se[vi+12>>2]=E,Se[vi+24>>2]=d;var A=1|E;Se[v+(s+1)]=A}else if((0|u)==(0|Se[vi+20>>2])){var g=Se[vi+8>>2]+w|0;Se[vi+8>>2]=g,Se[vi+20>>2]=d;var y=1|g;Se[v+(s+1)]=y;var m=r+g+c|0;Se[m>>2]=g}else{var S=Me[t+(_+1)];if(1==(3&S|0)){var M=S&-8,C=S>>>3,R=S>>>0<256;a:do if(R){var T=Me[((8|b)>>2)+_],O=Me[t+(_+3)];if((0|T)!=(0|O)){var N=((S>>>2&1073741822)<<2)+vi+40|0,I=(0|T)==(0|N);do{if(!I){if(T>>>0<Me[vi+16>>2]>>>0){f=18;break}f=15;break}f=15}while(0);do if(15==f){if((0|O)!=(0|N)&&O>>>0<Me[vi+16>>2]>>>0)break;Se[T+12>>2]=O,Se[O+8>>2]=T;break a}while(0);throw Ka(),\"Reached an unreachable!\"}var P=Se[vi>>2]&(1<<C^-1);Se[vi>>2]=P}else{var D=k,L=Me[((24|b)>>2)+_],F=Me[t+(_+3)],X=(0|F)==(0|D);do if(X){var j=16|b,U=j+(a+4)|0,x=Se[U>>2];if(0==(0|x)){var z=a+j|0,V=Se[z>>2];if(0==(0|V)){var B=0,i=B>>2;break}var H=z,K=V}else{var H=U,K=x;f=25}for(;;){var K,H,Y=K+20|0,G=Se[Y>>2];if(0==(0|G)){var W=K+16|0,Z=Me[W>>2];if(0==(0|Z))break;var H=W,K=Z}else var H=Y,K=G}if(H>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[H>>2]=0;var B=K,i=B>>2}else{var Q=Me[((8|b)>>2)+_];if(Q>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[Q+12>>2]=F,Se[F+8>>2]=Q;var B=F,i=B>>2}while(0);var B;if(0==(0|L))break;var q=b+(a+28)|0,$=(Se[q>>2]<<2)+vi+304|0,J=(0|D)==(0|Se[$>>2]);do{if(J){if(Se[$>>2]=B,0!=(0|B))break;var rr=Se[vi+4>>2]&(1<<Se[q>>2]^-1);Se[vi+4>>2]=rr;break a}if(L>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";var ar=L+16|0;if((0|Se[ar>>2])==(0|D)?Se[ar>>2]=B:Se[L+20>>2]=B,0==(0|B))break a}while(0);if(B>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[i+6]=L;var er=16|b,ir=Me[(er>>2)+_];if(0!=(0|ir)){if(ir>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[i+4]=ir,Se[ir+24>>2]=B}var vr=Me[(er+4>>2)+_];if(0==(0|vr))break;if(vr>>>0<Me[vi+16>>2]>>>0)throw Ka(),\"Reached an unreachable!\";Se[i+5]=vr,Se[vr+24>>2]=B}while(0);var tr=a+(M|b)|0,fr=M+w|0}else var tr=u,fr=w;var fr,tr,_r=tr+4|0,sr=Se[_r>>2]&-2;if(Se[_r>>2]=sr,Se[v+(s+1)]=1|fr,Se[(fr>>2)+s+v]=fr,fr>>>0<256){var nr=fr>>>2&1073741822,or=(nr<<2)+vi+40|0,lr=Me[vi>>2],br=1<<(fr>>>3),kr=0==(lr&br|0);do{if(!kr){var ur=(nr+2<<2)+vi+40|0,cr=Me[ur>>2];if(cr>>>0>=Me[vi+16>>2]>>>0){var hr=cr,dr=ur;break}throw Ka(),\"Reached an unreachable!\"}Se[vi>>2]=lr|br;var hr=or,dr=(nr+2<<2)+vi+40|0}while(0);var dr,hr;Se[dr>>2]=d,Se[hr+12>>2]=d,Se[v+(s+2)]=hr,Se[v+(s+3)]=or}else{var wr=h,pr=fr>>>8,Er=0==(0|pr);do if(Er)var Ar=0;else{if(fr>>>0>16777215){var Ar=31;break}var gr=(pr+1048320|0)>>>16&8,yr=pr<<gr,mr=(yr+520192|0)>>>16&4,Sr=yr<<mr,Mr=(Sr+245760|0)>>>16&2,Cr=14-(mr|gr|Mr)+(Sr<<Mr>>>15)|0,Ar=fr>>>((Cr+7|0)>>>0)&1|Cr<<1}while(0);var Ar,Rr=(Ar<<2)+vi+304|0;Se[v+(s+7)]=Ar;var Tr=c+(r+16)|0;Se[v+(s+5)]=0,Se[Tr>>2]=0;var Or=Se[vi+4>>2],Nr=1<<Ar;if(0==(Or&Nr|0)){var Ir=Or|Nr;Se[vi+4>>2]=Ir,Se[Rr>>2]=wr,Se[v+(s+6)]=Rr,Se[v+(s+3)]=wr,Se[v+(s+2)]=wr}else{if(31==(0|Ar))var Pr=0;else var Pr=25-(Ar>>>1)|0;for(var Pr,Dr=fr<<Pr,Lr=Se[Rr>>2];;){var Lr,Dr;if((Se[Lr+4>>2]&-8|0)==(0|fr)){var Fr=Lr+8|0,Xr=Me[Fr>>2],jr=Me[vi+16>>2],Ur=Lr>>>0<jr>>>0;do if(!Ur){if(Xr>>>0<jr>>>0)break;Se[Xr+12>>2]=wr,Se[Fr>>2]=wr,Se[v+(s+2)]=Xr,Se[v+(s+3)]=Lr,Se[v+(s+6)]=0;break r}while(0);throw Ka(),\"Reached an unreachable!\"}var xr=(Dr>>>31<<2)+Lr+16|0,zr=Me[xr>>2];if(0==(0|zr)){if(xr>>>0>=Me[vi+16>>2]>>>0){Se[xr>>2]=wr,Se[v+(s+6)]=Lr,Se[v+(s+3)]=wr,Se[v+(s+2)]=wr;break r}throw Ka(),\"Reached an unreachable!\"}var Dr=Dr<<1,Lr=zr}}}}while(0);return r+(8|o)|0}function wa(r){return 0|He.__str3342}function pa(r){return 0|He.__str14343}function Ea(r){Se[r>>2]=si+8|0}function Aa(r){0!=(0|r)&&va(r)}function ga(r){ya(r);var a=r;Aa(a)}function ya(r){var a=0|r;Ye(a)}function ma(r){var a=0|r;Ea(a),Se[r>>2]=ni+8|0}function Sa(r){var a=0|r;ya(a);var e=r;Aa(e)}function Ma(r,a){var e,i,v=Me[vi+24>>2],i=v>>2,t=v,f=ua(t),_=Se[f>>2],s=Se[f+4>>2],n=_+s|0,o=_+(s-39)|0;if(0==(7&o|0))var l=0;else var l=7&-o;var l,b=_+(s-47)+l|0,k=b>>>0<(v+16|0)>>>0?t:b,u=k+8|0,e=u>>2,c=u,h=r,d=a-40|0;ca(h,d);var w=k+4|0;Se[w>>2]=27,Se[e]=Se[vi+444>>2],Se[e+1]=Se[vi+448>>2],Se[e+2]=Se[vi+452>>2],Se[e+3]=Se[vi+456>>2],Se[vi+444>>2]=r,Se[vi+448>>2]=a,Se[vi+456>>2]=0,Se[vi+452>>2]=c;var p=k+28|0;Se[p>>2]=7;var E=(k+32|0)>>>0<n>>>0;r:do if(E)for(var A=p;;){var A,g=A+4|0;if(Se[g>>2]=7,(A+8|0)>>>0>=n>>>0)break r;var A=g}while(0);var y=(0|k)==(0|t);r:do if(!y){var m=k-v|0,S=t+m|0,M=m+(t+4)|0,C=Se[M>>2]&-2;Se[M>>2]=C;var R=1|m;Se[i+1]=R;var T=S;if(Se[T>>2]=m,m>>>0<256){var O=m>>>2&1073741822,N=(O<<2)+vi+40|0,I=Me[vi>>2],P=1<<(m>>>3),D=0==(I&P|0);do{if(!D){var L=(O+2<<2)+vi+40|0,F=Me[L>>2];if(F>>>0>=Me[vi+16>>2]>>>0){var X=F,j=L;break}throw Ka(),\"Reached an unreachable!\"}var U=I|P;Se[vi>>2]=U;var X=N,j=(O+2<<2)+vi+40|0}while(0);var j,X;Se[j>>2]=v,Se[X+12>>2]=v,Se[i+2]=X,Se[i+3]=N}else{var x=v,z=m>>>8,V=0==(0|z);do if(V)var B=0;else{if(m>>>0>16777215){var B=31;break}var H=(z+1048320|0)>>>16&8,K=z<<H,Y=(K+520192|0)>>>16&4,G=K<<Y,W=(G+245760|0)>>>16&2,Z=14-(Y|H|W)+(G<<W>>>15)|0,B=m>>>((Z+7|0)>>>0)&1|Z<<1}while(0);var B,Q=(B<<2)+vi+304|0;Se[i+7]=B,Se[i+5]=0,Se[i+4]=0;var q=Se[vi+4>>2],$=1<<B;if(0==(q&$|0)){var J=q|$;Se[vi+4>>2]=J,Se[Q>>2]=x,Se[i+6]=Q,Se[i+3]=v,Se[i+2]=v}else{if(31==(0|B))var rr=0;else var rr=25-(B>>>1)|0;for(var rr,ar=m<<rr,er=Se[Q>>2];;){var er,ar;if((Se[er+4>>2]&-8|0)==(0|m)){var ir=er+8|0,vr=Me[ir>>2],tr=Me[vi+16>>2],fr=er>>>0<tr>>>0;do if(!fr){if(vr>>>0<tr>>>0)break;Se[vr+12>>2]=x,Se[ir>>2]=x,Se[i+2]=vr,Se[i+3]=er,Se[i+6]=0;break r}while(0);throw Ka(),\"Reached an unreachable!\"}var _r=(ar>>>31<<2)+er+16|0,sr=Me[_r>>2];if(0==(0|sr)){if(_r>>>0>=Me[vi+16>>2]>>>0){Se[_r>>2]=x,Se[i+6]=er,Se[i+3]=v,Se[i+2]=v;break r}throw Ka(),\"Reached an unreachable!\"}var ar=ar<<1,er=sr}}}}while(0)}function Ca(r){return d(r)}function Ra(r,a){var e=0;do Ae[r+e]=Ae[a+e],e++;while(0!=Ae[a+e-1]);return r}function Ta(){var r=Ta;return r.LLVM_SAVEDSTACKS||(r.LLVM_SAVEDSTACKS=[]),r.LLVM_SAVEDSTACKS.push(le.stackSave()),r.LLVM_SAVEDSTACKS.length-1}function Oa(r){var a=Ta,e=a.LLVM_SAVEDSTACKS[r];a.LLVM_SAVEDSTACKS.splice(r,1),le.stackRestore(e)}function Na(r,a,e){for(var i=0;i<e;){var v=Ae[r+i],t=Ae[a+i];if(v==t&&0==v)return 0;if(0==v)return-1;if(0==t)return 1;if(v!=t)return v>t?1:-1;i++}return 0}function Ia(r,a){var e=Ca(r),i=0;do Ae[r+e+i]=Ae[a+i],i++;while(0!=Ae[a+i-1]);return r}function Pa(r,a,e,i){if(e>=20&&a%2==r%2)if(a%4==r%4){for(var v=a+e;a%4;)Ae[r++]=Ae[a++];for(var t=a>>2,f=r>>2,_=v>>2;t<_;)Se[f++]=Se[t++];for(a=t<<2,r=f<<2;a<v;)Ae[r++]=Ae[a++]}else{var v=a+e;a%2&&(Ae[r++]=Ae[a++]);for(var s=a>>1,n=r>>1,o=v>>1;s<o;)ye[n++]=ye[s++];a=s<<1,r=n<<1,a<v&&(Ae[r++]=Ae[a++])}else for(;e--;)Ae[r++]=Ae[a++]}function Da(r,a){return Na(r,a,Le)}function La(r,a,e){for(var i=0;i<e;i++){var v=Ae[r+i],t=Ae[a+i];if(v!=t)return v>t?1:-1}return 0}function Fa(r,a,e,i){if(e>=20){for(var v=r+e;r%4;)Ae[r++]=a;a<0&&(a+=256);for(var t=r>>2,f=v>>2,_=a|a<<8|a<<16|a<<24;t<f;)Se[t++]=_;for(r=t<<2;r<v;)Ae[r++]=a}else for(;e--;)Ae[r++]=a}function Xa(r,a,e,i){throw\"Assertion failed: \"+s(i)+\", at: \"+[s(r),a,s(e)]}function ja(r){var a=d(r),e=Jr(a+1);return Pa(e,r,a,1),Ae[e+a]=0,e}function Ua(r,a){function e(r){var e;return\"double\"===r?(xe[0]=Se[a+_>>2],xe[1]=Se[a+_+4>>2],e=ze[0]):\"i64\"==r?e=[Se[a+_>>2],Se[a+_+4>>2]]:(r=\"i32\",e=Se[a+_>>2]),_+=le.getNativeFieldSize(r),e}for(var i,v,t,f=r,_=0,s=[];;){var n=f;if(i=Ae[f],0===i)break;if(v=Ae[f+1],i==\"%\".charCodeAt(0)){var o=!1,l=!1,b=!1,k=!1;r:for(;;){switch(v){case\"+\".charCodeAt(0):o=!0;break;case\"-\".charCodeAt(0):l=!0;break;case\"#\".charCodeAt(0):b=!0;break;case\"0\".charCodeAt(0):if(k)break r;k=!0;break;default:break r}f++,v=Ae[f+1]}var u=0;if(v==\"*\".charCodeAt(0))u=e(\"i32\"),f++,v=Ae[f+1];else for(;v>=\"0\".charCodeAt(0)&&v<=\"9\".charCodeAt(0);)u=10*u+(v-\"0\".charCodeAt(0)),f++,v=Ae[f+1];var c=!1;if(v==\".\".charCodeAt(0)){var h=0;if(c=!0,f++,v=Ae[f+1],v==\"*\".charCodeAt(0))h=e(\"i32\"),f++;else for(;;){var d=Ae[f+1];if(d<\"0\".charCodeAt(0)||d>\"9\".charCodeAt(0))break;h=10*h+(d-\"0\".charCodeAt(0)),f++}v=Ae[f+1]}else var h=6;var E;switch(String.fromCharCode(v)){case\"h\":var A=Ae[f+2];A==\"h\".charCodeAt(0)?(f++,E=1):E=2;break;case\"l\":var A=Ae[f+2];A==\"l\".charCodeAt(0)?(f++,E=8):E=4;break;case\"L\":case\"q\":case\"j\":E=8;break;case\"z\":case\"t\":case\"I\":E=4;break;default:E=null}if(E&&f++,v=Ae[f+1],[\"d\",\"i\",\"u\",\"o\",\"x\",\"X\",\"p\"].indexOf(String.fromCharCode(v))!=-1){var m=v==\"d\".charCodeAt(0)||v==\"i\".charCodeAt(0);E=E||4;var t=e(\"i\"+8*E);if(8==E&&(t=le.makeBigInt(t[0],t[1],v==\"u\".charCodeAt(0))),E<=4){var S=Math.pow(256,E)-1;t=(m?y:g)(t&S,8*E)}var M,C=Math.abs(t),R=\"\";if(v==\"d\".charCodeAt(0)||v==\"i\".charCodeAt(0))M=y(t,8*E,1).toString(10);else if(v==\"u\".charCodeAt(0))M=g(t,8*E,1).toString(10),t=Math.abs(t);else if(v==\"o\".charCodeAt(0))M=(b?\"0\":\"\")+C.toString(8);else if(v==\"x\".charCodeAt(0)||v==\"X\".charCodeAt(0)){if(R=b?\"0x\":\"\",t<0){t=-t,M=(C-1).toString(16);for(var T=[],O=0;O<M.length;O++)T.push((15-parseInt(M[O],16)).toString(16));for(M=T.join(\"\");M.length<2*E;)M=\"f\"+M}else M=C.toString(16);v==\"X\".charCodeAt(0)&&(R=R.toUpperCase(),M=M.toUpperCase())}else v==\"p\".charCodeAt(0)&&(0===C?M=\"(nil)\":(R=\"0x\",M=C.toString(16)));if(c)for(;M.length<h;)M=\"0\"+M;for(o&&(R=t<0?\"-\"+R:\"+\"+R);R.length+M.length<u;)l?M+=\" \":k?M=\"0\"+M:R=\" \"+R;M=R+M,M.split(\"\").forEach(function(r){s.push(r.charCodeAt(0))})}else if([\"f\",\"F\",\"e\",\"E\",\"g\",\"G\"].indexOf(String.fromCharCode(v))!=-1){var M,t=e(\"double\");if(isNaN(t))M=\"nan\",k=!1;else if(isFinite(t)){var N=!1,I=Math.min(h,20);if(v==\"g\".charCodeAt(0)||v==\"G\".charCodeAt(0)){N=!0,h=h||1;var P=parseInt(t.toExponential(I).split(\"e\")[1],10);h>P&&P>=-4?(v=(v==\"g\".charCodeAt(0)?\"f\":\"F\").charCodeAt(0),h-=P+1):(v=(v==\"g\".charCodeAt(0)?\"e\":\"E\").charCodeAt(0),h--),I=Math.min(h,20)}v==\"e\".charCodeAt(0)||v==\"E\".charCodeAt(0)?(M=t.toExponential(I),/[eE][-+]\\\\d$/.test(M)&&(M=M.slice(0,-1)+\"0\"+M.slice(-1))):v!=\"f\".charCodeAt(0)&&v!=\"F\".charCodeAt(0)||(M=t.toFixed(I));var D=M.split(\"e\");if(N&&!b)for(;D[0].length>1&&D[0].indexOf(\".\")!=-1&&(\"0\"==D[0].slice(-1)||\".\"==D[0].slice(-1));)D[0]=D[0].slice(0,-1);else for(b&&M.indexOf(\".\")==-1&&(D[0]+=\".\");h>I++;)D[0]+=\"0\";M=D[0]+(D.length>1?\"e\"+D[1]:\"\"),v==\"E\".charCodeAt(0)&&(M=M.toUpperCase()),o&&t>=0&&(M=\"+\"+M)}else M=(t<0?\"-\":\"\")+\"inf\",k=!1;for(;M.length<u;)l?M+=\" \":M=!k||\"-\"!=M[0]&&\"+\"!=M[0]?(k?\"0\":\" \")+M:M[0]+\"0\"+M.slice(1);v<\"a\".charCodeAt(0)&&(M=M.toUpperCase()),M.split(\"\").forEach(function(r){s.push(r.charCodeAt(0))})}else if(v==\"s\".charCodeAt(0)){var L,F=e(\"i8*\");if(F?(L=w(F),c&&L.length>h&&(L=L.slice(0,h))):L=p(\"(null)\",!0),!l)for(;L.length<u--;)s.push(\" \".charCodeAt(0));if(s=s.concat(L),l)for(;L.length<u--;)s.push(\" \".charCodeAt(0))}else if(v==\"c\".charCodeAt(0)){for(l&&s.push(e(\"i8\"));--u>0;)s.push(\" \".charCodeAt(0));l||s.push(e(\"i8\"))}else if(v==\"n\".charCodeAt(0)){var X=e(\"i32*\");Se[X>>2]=s.length}else if(v==\"%\".charCodeAt(0))s.push(i);else for(var O=n;O<f+2;O++)s.push(Ae[O]);f+=2}else s.push(i),f+=1}return s}function xa(r,a,e,i){for(var v=Ua(e,i),t=void 0===a?v.length:Math.min(v.length,a-1),f=0;f<t;f++)Ae[r+f]=v[f];return Ae[r+f]=0,v.length}function za(r,a,e){return xa(r,void 0,a,e)}function Va(r){return r in{32:0,9:0,10:0,11:0,12:0,13:0}}function Ba(r){return r>=\"0\".charCodeAt(0)&&r<=\"9\".charCodeAt(0)}function Ha(r){for(var a;(a=Ae[r])&&Va(a);)r++;if(!a||!Ba(a))return 0;for(var e=r;(a=Ae[e])&&Ba(a);)e++;return Math.floor(Number(s(r).substr(0,e-r)))}function Ka(r){throw ke=!0,\"ABORT: \"+r+\", at \"+(new Error).stack}function Ya(r){return Ya.ret||(Ya.ret=_([0],\"i32\",we)),Se[Ya.ret>>2]=r,r}function Ga(r,a,e,i){var v=$e.streams[r];if(!v||v.object.isDevice)return Ya(Ge.EBADF),-1;if(v.isWrite){if(v.object.isFolder)return Ya(Ge.EISDIR),-1;if(e<0||i<0)return Ya(Ge.EINVAL),-1;for(var t=v.object.contents;t.length<i;)t.push(0);for(var f=0;f<e;f++)t[i+f]=ge[a+f];return v.object.timestamp=Date.now(),f}return Ya(Ge.EACCES),-1}function Wa(r,a,e){var i=$e.streams[r];if(i){if(i.isWrite){if(e<0)return Ya(Ge.EINVAL),-1;if(i.object.isDevice){if(i.object.output){for(var v=0;v<e;v++)try{i.object.output(Ae[a+v])}catch(r){return Ya(Ge.EIO),-1}return i.object.timestamp=Date.now(),v}return Ya(Ge.ENXIO),-1}var t=Ga(r,a,e,i.position);return t!=-1&&(i.position+=t),t}return Ya(Ge.EACCES),-1}return Ya(Ge.EBADF),-1}function Za(r,a,e,i){var v=e*a;if(0==v)return 0;var t=Wa(i,r,v);return t==-1?($e.streams[i]&&($e.streams[i].error=!0),-1):Math.floor(t/a)}function Qa(r,a,e){var i=Ua(a,e),v=le.stackSave(),t=Za(_(i,\"i8\",de),1,i.length,r);return le.stackRestore(v),t}function qa(r){switch(r){case 8:return Pe;case 54:case 56:case 21:case 61:case 63:case 22:case 67:case 23:case 24:case 25:case 26:case 27:case 69:case 28:case 101:case 70:case 71:case 29:case 30:case 199:case 75:case 76:case 32:case 43:case 44:case 80:case 46:case 47:case 45:case 48:case 49:case 42:case 82:case 33:case 7:case 108:case 109:case 107:case 112:case 119:case 121:return 200809;case 13:case 104:case 94:case 95:case 34:case 35:case 77:case 81:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 91:case 94:case 95:case 110:case 111:case 113:case 114:case 115:case 116:case 117:case 118:case 120:case 40:case 16:case 79:case 19:return-1;case 92:case 93:case 5:case 72:case 6:case 74:case 92:case 93:case 96:case 97:case 98:case 99:case 102:case 103:case 105:return 1;case 38:case 66:case 50:case 51:case 4:return 1024;case 15:case 64:case 41:return 32;case 55:case 37:case 17:return 2147483647;case 18:case 1:return 47839;case 59:case 57:return 99;case 68:case 58:return 2048;case 0:return 2097152;case 3:return 65536;case 14:return 32768;case 73:return 32767;case 39:return 16384;case 60:return 1e3;case 106:return 700;case 52:return 256;case 62:return 255;case 2:return 100;case 65:return 64;case 36:return 20;case 100:return 16;case 20:return 6;case 53:return 4}return Ya(Ge.EINVAL),-1}function $a(r){var a=Math.floor(Date.now()/1e3);return r&&(Se[r>>2]=a),a}function Ja(){return Ya.ret}function re(r){var a=re;a.called||(Ie=o(Ie),a.called=!0);var e=Ie;return 0!=r&&le.staticAlloc(r),e}function ae(){return Se[ae.buf>>2]}function ee(r){r=r||Module.arguments,k();var a=null;return Module._main&&(a=Module.callMain(r),Module.noExitRuntime||u()),a}var ie=[],ve=false,te=\"object\"==typeof window,fe=\"function\"==typeof importScripts,_e=!te&&!ve&&!fe;if(ve){print=function(r){process.stdout.write(r+\"\\\\n\")},printErr=function(r){process.stderr.write(r+\"\\\\n\")};var se=require(\"fs\");read=function(r){var a=se.readFileSync(r).toString();return a||\"/\"==r[0]||(r=__dirname.split(\"/\").slice(0,-1).join(\"/\")+\"/src/\"+r,a=se.readFileSync(r).toString()),a},load=function(a){r(read(a))},ie=process.argv.slice(2)}else if(_e)this.read||(this.read=function(r){snarf(r)}),\"undefined\"!=typeof scriptArgs?ie=scriptArgs:\"undefined\"!=typeof arguments&&(ie=arguments);else if(te)this.print=printErr=function(r){console.log(r)},this.read=function(r){var a=new XMLHttpRequest;return a.open(\"GET\",r,!1),a.send(null),a.responseText},this.arguments&&(ie=arguments);else{if(!fe)throw\"Unknown runtime environment. Where are we?\";this.load=importScripts}\"undefined\"==typeof load&&\"undefined\"!=typeof read&&(this.load=function(a){r(read(a))}),\"undefined\"==typeof printErr&&(this.printErr=function(){}),\"undefined\"==typeof print&&(this.print=printErr);try{this.Module=Module}catch(r){this.Module=Module={}}Module.arguments||(Module.arguments=ie),Module.print&&(print=Module.print);var ne,oe,le={stackSave:function(){return Oe},stackRestore:function(r){Oe=r},forceAlign:function(r,a){if(a=a||4,1==a)return r;if(isNumber(r)&&isNumber(a))return Math.ceil(r/a)*a;if(isNumber(a)&&isPowerOfTwo(a)){var e=log2(a);return\"((((\"+r+\")+\"+(a-1)+\")>>\"+e+\")<<\"+e+\")\"}return\"Math.ceil((\"+r+\")/\"+a+\")*\"+a},isNumberType:function(r){return r in le.INT_TYPES||r in le.FLOAT_TYPES},isPointerType:function(r){return\"*\"==r[r.length-1]},isStructType:function(r){return!isPointerType(r)&&(!!/^\\\\[\\\\d+\\\\ x\\\\ (.*)\\\\]/.test(r)||(!!/<?{ [^}]* }>?/.test(r)||\"%\"==r[0]))},INT_TYPES:{i1:0,i8:0,i16:0,i32:0,i64:0},FLOAT_TYPES:{float:0,double:0},bitshift64:function(r,e,i,v){var t=Math.pow(2,v)-1;if(v<32)switch(i){case\"shl\":return[r<<v,e<<v|(r&t<<32-v)>>>32-v];case\"ashr\":return[(r>>>v|(e&t)<<32-v)>>0>>>0,e>>v>>>0];case\"lshr\":return[(r>>>v|(e&t)<<32-v)>>>0,e>>>v]}else if(32==v)switch(i){case\"shl\":return[0,r];case\"ashr\":return[e,(0|e)<0?t:0];case\"lshr\":return[e,0]}else switch(i){case\"shl\":return[0,r<<v-32];case\"ashr\":return[e>>v-32>>>0,(0|e)<0?t:0];case\"lshr\":return[e>>>v-32,0]}a(\"unknown bitshift64 op: \"+[value,i,v])},or64:function(r,a){var e=0|r|(0|a),i=4294967296*(Math.round(r/4294967296)|Math.round(a/4294967296));return e+i},and64:function(r,a){var e=(0|r)&(0|a),i=4294967296*(Math.round(r/4294967296)&Math.round(a/4294967296));return e+i},xor64:function(r,a){var e=(0|r)^(0|a),i=4294967296*(Math.round(r/4294967296)^Math.round(a/4294967296));return e+i},getNativeTypeSize:function(r,a){if(1==le.QUANTUM_SIZE)return 1;var i={\"%i1\":1,\"%i8\":1,\"%i16\":2,\"%i32\":4,\"%i64\":8,\"%float\":4,\"%double\":8}[\"%\"+r];if(!i)if(\"*\"==r[r.length-1])i=le.QUANTUM_SIZE;else if(\"i\"==r[0]){var v=parseInt(r.substr(1));e(v%8==0),i=v/8}return i},getNativeFieldSize:function(r){return Math.max(le.getNativeTypeSize(r),le.QUANTUM_SIZE)},dedup:function(r,a){var e={};return a?r.filter(function(r){return!e[r[a]]&&(e[r[a]]=!0,!0)}):r.filter(function(r){return!e[r]&&(e[r]=!0,!0)})},set:function(){for(var r=\"object\"==typeof arguments[0]?arguments[0]:arguments,a={},e=0;e<r.length;e++)a[r[e]]=0;return a},calculateStructAlignment:function(r){r.flatSize=0,r.alignSize=0;var a=[],e=-1;return r.flatIndexes=r.fields.map(function(i){var v,t;if(le.isNumberType(i)||le.isPointerType(i))v=le.getNativeTypeSize(i),t=v;else{if(!le.isStructType(i))throw\"Unclear type in struct: \"+i+\", in \"+r.name_+\" :: \"+dump(Types.types[r.name_]);v=Types.types[i].flatSize,t=Types.types[i].alignSize}t=r.packed?1:Math.min(t,le.QUANTUM_SIZE),r.alignSize=Math.max(r.alignSize,t);var f=le.alignMemory(r.flatSize,t);return r.flatSize=f+v,e>=0&&a.push(f-e),e=f,f}),r.flatSize=le.alignMemory(r.flatSize,r.alignSize),0==a.length?r.flatFactor=r.flatSize:1==le.dedup(a).length&&(r.flatFactor=a[0]),r.needsFlattening=1!=r.flatFactor,r.flatIndexes},generateStructInfo:function(r,a,i){var v,t;if(a){if(i=i||0,v=(\"undefined\"==typeof Types?le.typeInfo:Types.types)[a],!v)return null;e(v.fields.length===r.length,\"Number of named fields must match the type for \"+a),t=v.flatIndexes}else{var v={fields:r.map(function(r){return r[0]})};t=le.calculateStructAlignment(v)}var f={__size__:v.flatSize};return a?r.forEach(function(r,a){if(\"string\"==typeof r)f[r]=t[a]+i;else{var e;for(var _ in r)e=_;f[e]=le.generateStructInfo(r[e],v.fields[a],t[a])}}):r.forEach(function(r,a){f[r[1]]=t[a]}),f},stackAlloc:function(r){var a=Oe;return Oe+=r,Oe=Oe+3>>2<<2,a},staticAlloc:function(r){var a=Ie;return Ie+=r,Ie=Ie+3>>2<<2,Ie>=Le&&l(),a},alignMemory:function(r,a){var e=r=Math.ceil(r/(a?a:4))*(a?a:4);return e},makeBigInt:function(r,a,e){var i=e?(r>>>0)+4294967296*(a>>>0):(r>>>0)+4294967296*(0|a);return i},QUANTUM_SIZE:4,__dummy__:0},be={MAX_ALLOWED:0,corrections:0,sigs:{},note:function(r,e,i){e||(this.corrections++,this.corrections>=this.MAX_ALLOWED&&a(\"\\\\n\\\\nToo many corrections!\"))},print:function(){}},ke=!1,ue=0,ce=this;Module.ccall=i,Module.setValue=t,Module.getValue=f;var he=0,de=1,we=2;Module.ALLOC_NORMAL=he,Module.ALLOC_STACK=de,Module.ALLOC_STATIC=we,Module.allocate=_,Module.Pointer_stringify=s,Module.Array_stringify=n;var pe,Ee,Ae,ge,ye,me,Se,Me,Ce,Re,Te,Oe,Ne,Ie,Pe=4096,De=Module.TOTAL_STACK||5242880,Le=Module.TOTAL_MEMORY||10485760;Module.FAST_MEMORY||2097152;e(!!(Int32Array&&Float64Array&&new Int32Array(1).subarray&&new Int32Array(1).set),\"Cannot fallback to non-typed array case: Code is too specialized\");var Fe=new ArrayBuffer(Le);Ae=new Int8Array(Fe),ye=new Int16Array(Fe),Se=new Int32Array(Fe),ge=new Uint8Array(Fe),me=new Uint16Array(Fe),Me=new Uint32Array(Fe),Ce=new Float32Array(Fe),Re=new Float64Array(Fe),Se[0]=255,e(255===ge[0]&&0===ge[3],\"Typed arrays 2 must be run on a little-endian system\");var Xe=p(\"(null)\");Ie=Xe.length;for(var je=0;je<Xe.length;je++)Ae[je]=Xe[je];Module.HEAP=Ee,Module.HEAP8=Ae,Module.HEAP16=ye,Module.HEAP32=Se,Module.HEAPU8=ge,Module.HEAPU16=me,Module.HEAPU32=Me,Module.HEAPF32=Ce,Module.HEAPF64=Re,Te=Oe=le.alignMemory(Ie),Ne=Te+De;var Ue=le.alignMemory(Ne,8),xe=(Ae.subarray(Ue),Se.subarray(Ue>>2)),ze=(Ce.subarray(Ue>>2),Re.subarray(Ue>>3));Ne=Ue+8,Ie=o(Ne);var Ve=[],Be=[];Module.Array_copy=c,Module.TypedArray_copy=h,Module.String_len=d,Module.String_copy=w,Module.intArrayFromString=p,Module.intArrayToString=E,Module.writeStringToMemory=A;var He=[],Ke=0;O.X=1,N.X=1,V.X=1,H.X=1,G.X=1,W.X=1,q.X=1,$.X=1,rr.X=1,ar.X=1,er.X=1,vr.X=1,nr.X=1,or.X=1,kr.X=1,hr.X=1,Ar.X=1,Sr.X=1,Tr.X=1,Ir.X=1,Pr.X=1,Dr.X=1,Lr.X=1,Fr.X=1,Xr.X=1,zr.X=1,Vr.X=1,Br.X=1,Gr.X=1,$r.X=1,Module._malloc=Jr,Jr.X=1,ra.X=1,aa.X=1,ea.X=1,ia.X=1,Module._free=va,va.X=1,_a.X=1,sa.X=1,na.X=1,oa.X=1,la.X=1,da.X=1,Ma.X=1;var Ye,Ge={E2BIG:7,EACCES:13,EADDRINUSE:98,EADDRNOTAVAIL:99,EAFNOSUPPORT:97,EAGAIN:11,EALREADY:114,EBADF:9,EBADMSG:74,EBUSY:16,ECANCELED:125,ECHILD:10,ECONNABORTED:103,ECONNREFUSED:111,ECONNRESET:104,EDEADLK:35,EDESTADDRREQ:89,EDOM:33,EDQUOT:122,EEXIST:17,EFAULT:14,EFBIG:27,EHOSTUNREACH:113,EIDRM:43,EILSEQ:84,EINPROGRESS:115,EINTR:4,EINVAL:22,EIO:5,EISCONN:106,EISDIR:21,ELOOP:40,EMFILE:24,EMLINK:31,EMSGSIZE:90,EMULTIHOP:72,ENAMETOOLONG:36,ENETDOWN:100,ENETRESET:102,ENETUNREACH:101,ENFILE:23,ENOBUFS:105,ENODATA:61,ENODEV:19,ENOENT:2,ENOEXEC:8,ENOLCK:37,ENOLINK:67,ENOMEM:12,ENOMSG:42,ENOPROTOOPT:92,ENOSPC:28,ENOSR:63,ENOSTR:60,ENOSYS:38,ENOTCONN:107,ENOTDIR:20,ENOTEMPTY:39,ENOTRECOVERABLE:131,ENOTSOCK:88,ENOTSUP:95,ENOTTY:25,ENXIO:6,EOVERFLOW:75,EOWNERDEAD:130,EPERM:1,EPIPE:32,EPROTO:71,EPROTONOSUPPORT:93,EPROTOTYPE:91,ERANGE:34,EROFS:30,ESPIPE:29,ESRCH:3,ESTALE:116,ETIME:62,ETIMEDOUT:110,ETXTBSY:26,EWOULDBLOCK:11,EXDEV:18},We=0,Ze=0,Qe=0,qe=0,$e={currentPath:\"/\",nextInode:2,streams:[null],ignorePermissions:!0,absolutePath:function(r,a){if(\"string\"!=typeof r)return null;void 0===a&&(a=$e.currentPath),r&&\"/\"==r[0]&&(a=\"\");for(var e=a+\"/\"+r,i=e.split(\"/\").reverse(),v=[\"\"];i.length;){var t=i.pop();\"\"==t||\".\"==t||(\"..\"==t?v.length>1&&v.pop():v.push(t))}return 1==v.length?\"/\":v.join(\"/\")},analyzePath:function(r,a,e){var i={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};if(r=$e.absolutePath(r),\"/\"==r)i.isRoot=!0,i.exists=i.parentExists=!0,i.name=\"/\",i.path=i.parentPath=\"/\",i.object=i.parentObject=$e.root;else if(null!==r){e=e||0,r=r.slice(1).split(\"/\");for(var v=$e.root,t=[\"\"];r.length;){1==r.length&&v.isFolder&&(i.parentExists=!0,i.parentPath=1==t.length?\"/\":t.join(\"/\"),i.parentObject=v,i.name=r[0]);var f=r.shift();if(!v.isFolder){i.error=Ge.ENOTDIR;break}if(!v.read){i.error=Ge.EACCES;break}if(!v.contents.hasOwnProperty(f)){i.error=Ge.ENOENT;break}if(v=v.contents[f],v.link&&(!a||0!=r.length)){if(e>40){i.error=Ge.ELOOP;break}var _=$e.absolutePath(v.link,t.join(\"/\"));return $e.analyzePath([_].concat(r).join(\"/\"),a,e+1)}t.push(f),0==r.length&&(i.exists=!0,i.path=t.join(\"/\"),i.object=v)}return i}return i},findObject:function(r,a){$e.ensureRoot();var e=$e.analyzePath(r,a);return e.exists?e.object:(Ya(e.error),null)},createObject:function(r,a,e,i,v){if(r||(r=\"/\"),\"string\"==typeof r&&(r=$e.findObject(r)),!r)throw Ya(Ge.EACCES),new Error(\"Parent path must exist.\");if(!r.isFolder)throw Ya(Ge.ENOTDIR),\nnew Error(\"Parent must be a folder.\");if(!r.write&&!$e.ignorePermissions)throw Ya(Ge.EACCES),new Error(\"Parent folder must be writeable.\");if(!a||\".\"==a||\"..\"==a)throw Ya(Ge.ENOENT),new Error(\"Name must not be empty.\");if(r.contents.hasOwnProperty(a))throw Ya(Ge.EEXIST),new Error(\"Can't overwrite object.\");r.contents[a]={read:void 0===i||i,write:void 0!==v&&v,timestamp:Date.now(),inodeNumber:$e.nextInode++};for(var t in e)e.hasOwnProperty(t)&&(r.contents[a][t]=e[t]);return r.contents[a]},createFolder:function(r,a,e,i){var v={isFolder:!0,isDevice:!1,contents:{}};return $e.createObject(r,a,v,e,i)},createPath:function(r,a,e,i){var v=$e.findObject(r);if(null===v)throw new Error(\"Invalid parent.\");for(a=a.split(\"/\").reverse();a.length;){var t=a.pop();t&&(v.contents.hasOwnProperty(t)||$e.createFolder(v,t,e,i),v=v.contents[t])}return v},createFile:function(r,a,e,i,v){return e.isFolder=!1,$e.createObject(r,a,e,i,v)},createDataFile:function(r,a,e,i,v){if(\"string\"==typeof e){for(var t=new Array(e.length),f=0,_=e.length;f<_;++f)t[f]=e.charCodeAt(f);e=t}var s={isDevice:!1,contents:e};return $e.createFile(r,a,s,i,v)},createLazyFile:function(r,a,e,i,v){var t={isDevice:!1,url:e};return $e.createFile(r,a,t,i,v)},createLink:function(r,a,e,i,v){var t={isDevice:!1,link:e};return $e.createFile(r,a,t,i,v)},createDevice:function(r,a,e,i){if(!e&&!i)throw new Error(\"A device must have at least one callback defined.\");var v={isDevice:!0,input:e,output:i};return $e.createFile(r,a,v,Boolean(e),Boolean(i))},forceLoadFile:function(r){if(r.isDevice||r.isFolder||r.link||r.contents)return!0;var a=!0;if(\"undefined\"!=typeof XMLHttpRequest)e(\"Cannot do synchronous binary XHRs in modern browsers. Use --embed-file or --preload-file in emcc\");else{if(\"undefined\"==typeof read)throw new Error(\"Cannot load without read() or XMLHttpRequest.\");try{r.contents=p(read(r.url),!0)}catch(r){a=!1}}return a||Ya(Ge.EIO),a},ensureRoot:function(){$e.root||($e.root={read:!0,write:!0,isFolder:!0,isDevice:!1,timestamp:Date.now(),inodeNumber:1,contents:{}})},init:function(r,a,i){function v(r){null===r||r===\"\\\\n\".charCodeAt(0)?(a.printer(a.buffer.join(\"\")),a.buffer=[]):a.buffer.push(String.fromCharCode(r))}e(!$e.init.initialized,\"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)\"),$e.init.initialized=!0,$e.ensureRoot(),r=r||Module.stdin,a=a||Module.stdout,i=i||Module.stderr;var t=!0,f=!0,s=!0;r||(t=!1,r=function(){if(!r.cache||!r.cache.length){var a;\"undefined\"!=typeof window&&\"function\"==typeof window.prompt?a=window.prompt(\"Input: \"):\"function\"==typeof readline&&(a=readline()),a||(a=\"\"),r.cache=p(a+\"\\\\n\",!0)}return r.cache.shift()}),a||(f=!1,a=v),a.printer||(a.printer=print),a.buffer||(a.buffer=[]),i||(s=!1,i=v),i.printer||(i.printer=print),i.buffer||(i.buffer=[]),$e.createFolder(\"/\",\"tmp\",!0,!0);var n=$e.createFolder(\"/\",\"dev\",!0,!0),o=$e.createDevice(n,\"stdin\",r),l=$e.createDevice(n,\"stdout\",null,a),b=$e.createDevice(n,\"stderr\",null,i);$e.createDevice(n,\"tty\",r,a),$e.streams[1]={path:\"/dev/stdin\",object:o,position:0,isRead:!0,isWrite:!1,isAppend:!1,isTerminal:!t,error:!1,eof:!1,ungotten:[]},$e.streams[2]={path:\"/dev/stdout\",object:l,position:0,isRead:!1,isWrite:!0,isAppend:!1,isTerminal:!f,error:!1,eof:!1,ungotten:[]},$e.streams[3]={path:\"/dev/stderr\",object:b,position:0,isRead:!1,isWrite:!0,isAppend:!1,isTerminal:!s,error:!1,eof:!1,ungotten:[]},We=_([1],\"void*\",we),Ze=_([2],\"void*\",we),Qe=_([3],\"void*\",we),$e.createPath(\"/\",\"dev/shm/tmp\",!0,!0),$e.streams[We]=$e.streams[1],$e.streams[Ze]=$e.streams[2],$e.streams[Qe]=$e.streams[3],qe=_([_([0,0,0,0,We,0,0,0,Ze,0,0,0,Qe,0,0,0],\"void*\",we)],\"void*\",we)},quit:function(){$e.init.initialized&&($e.streams[2]&&$e.streams[2].object.output.buffer.length>0&&$e.streams[2].object.output(\"\\\\n\".charCodeAt(0)),$e.streams[3]&&$e.streams[3].object.output.buffer.length>0&&$e.streams[3].object.output(\"\\\\n\".charCodeAt(0)))}},Je=Ja;Ve.unshift({func:function(){$e.ignorePermissions=!1,$e.init.initialized||$e.init()}}),Be.push({func:function(){$e.quit()}}),Ya(0),ae.buf=_(12,\"void*\",we),Module.callMain=function(r){function a(){for(var r=0;r<3;r++)i.push(0)}var e=r.length+1,i=[_(p(\"/bin/this.program\"),\"i8\",we)];a();for(var v=0;v<e-1;v+=1)i.push(_(p(r[v]),\"i8\",we)),a();return i.push(0),i=_(i,\"i32\",we),_main(e,i,0)};var ri,ai,ei,ii,vi,ti,qe,fi,_i,si,ni,oi,li,bi,ki,ui,ci,hi,di,wi;if(He.__str=_([97,78,0],\"i8\",we),He.__str1=_([38,61,0],\"i8\",we),He.__str2=_([97,83,0],\"i8\",we),He.__str3=_([61,0],\"i8\",we),He.__str4=_([97,97,0],\"i8\",we),He.__str5=_([38,38,0],\"i8\",we),He.__str6=_([97,100,0],\"i8\",we),He.__str7=_([38,0],\"i8\",we),He.__str8=_([97,110,0],\"i8\",we),He.__str9=_([99,108,0],\"i8\",we),He.__str10=_([40,41,0],\"i8\",we),He.__str11=_([99,109,0],\"i8\",we),He.__str12=_([44,0],\"i8\",we),He.__str13=_([99,111,0],\"i8\",we),He.__str14=_([126,0],\"i8\",we),He.__str15=_([100,86,0],\"i8\",we),He.__str16=_([47,61,0],\"i8\",we),He.__str17=_([100,97,0],\"i8\",we),He.__str18=_([100,101,108,101,116,101,91,93,0],\"i8\",we),He.__str19=_([100,101,0],\"i8\",we),He.__str20=_([42,0],\"i8\",we),He.__str21=_([100,108,0],\"i8\",we),He.__str22=_([100,101,108,101,116,101,0],\"i8\",we),He.__str23=_([100,118,0],\"i8\",we),He.__str24=_([47,0],\"i8\",we),He.__str25=_([101,79,0],\"i8\",we),He.__str26=_([94,61,0],\"i8\",we),He.__str27=_([101,111,0],\"i8\",we),He.__str28=_([94,0],\"i8\",we),He.__str29=_([101,113,0],\"i8\",we),He.__str30=_([61,61,0],\"i8\",we),He.__str31=_([103,101,0],\"i8\",we),He.__str32=_([62,61,0],\"i8\",we),He.__str33=_([103,116,0],\"i8\",we),He.__str34=_([62,0],\"i8\",we),He.__str35=_([105,120,0],\"i8\",we),He.__str36=_([91,93,0],\"i8\",we),He.__str37=_([108,83,0],\"i8\",we),He.__str38=_([60,60,61,0],\"i8\",we),He.__str39=_([108,101,0],\"i8\",we),He.__str40=_([60,61,0],\"i8\",we),He.__str41=_([108,115,0],\"i8\",we),He.__str42=_([60,60,0],\"i8\",we),He.__str43=_([108,116,0],\"i8\",we),He.__str44=_([60,0],\"i8\",we),He.__str45=_([109,73,0],\"i8\",we),He.__str46=_([45,61,0],\"i8\",we),He.__str47=_([109,76,0],\"i8\",we),He.__str48=_([42,61,0],\"i8\",we),He.__str49=_([109,105,0],\"i8\",we),He.__str51=_([109,108,0],\"i8\",we),He.__str52=_([109,109,0],\"i8\",we),He.__str53=_([45,45,0],\"i8\",we),He.__str54=_([110,97,0],\"i8\",we),He.__str55=_([110,101,119,91,93,0],\"i8\",we),He.__str56=_([110,101,0],\"i8\",we),He.__str57=_([33,61,0],\"i8\",we),He.__str58=_([110,103,0],\"i8\",we),He.__str59=_([110,116,0],\"i8\",we),He.__str60=_([33,0],\"i8\",we),He.__str61=_([110,119,0],\"i8\",we),He.__str62=_([110,101,119,0],\"i8\",we),He.__str63=_([111,82,0],\"i8\",we),He.__str64=_([124,61,0],\"i8\",we),He.__str65=_([111,111,0],\"i8\",we),He.__str66=_([124,124,0],\"i8\",we),He.__str67=_([111,114,0],\"i8\",we),He.__str68=_([124,0],\"i8\",we),He.__str69=_([112,76,0],\"i8\",we),He.__str70=_([43,61,0],\"i8\",we),He.__str71=_([112,108,0],\"i8\",we),He.__str72=_([43,0],\"i8\",we),He.__str73=_([112,109,0],\"i8\",we),He.__str74=_([45,62,42,0],\"i8\",we),He.__str75=_([112,112,0],\"i8\",we),He.__str76=_([43,43,0],\"i8\",we),He.__str77=_([112,115,0],\"i8\",we),He.__str78=_([112,116,0],\"i8\",we),He.__str79=_([45,62,0],\"i8\",we),He.__str80=_([113,117,0],\"i8\",we),He.__str81=_([63,0],\"i8\",we),He.__str82=_([114,77,0],\"i8\",we),He.__str83=_([37,61,0],\"i8\",we),He.__str84=_([114,83,0],\"i8\",we),He.__str85=_([62,62,61,0],\"i8\",we),He.__str86=_([114,109,0],\"i8\",we),He.__str87=_([37,0],\"i8\",we),He.__str88=_([114,115,0],\"i8\",we),He.__str89=_([62,62,0],\"i8\",we),He.__str90=_([115,116,0],\"i8\",we),He.__str91=_([115,105,122,101,111,102,32,0],\"i8\",we),He.__str92=_([115,122,0],\"i8\",we),ri=_([0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,8,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,6,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,3,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0],we),He.__str95=_([98,111,111,108,101,97,110,0],\"i8\",we),He.__str97=_([98,121,116,101,0],\"i8\",we),He.__str101=_([95,95,102,108,111,97,116,49,50,56,0],\"i8\",we),He.__str105=_([117,110,115,105,103,110,101,100,0],\"i8\",we),He.__str114=_([108,111,110,103,32,108,111,110,103,0],\"i8\",we),He.__str115=_([117,110,115,105,103,110,101,100,32,108,111,110,103,32,108,111,110,103,0],\"i8\",we),ai=_([0,0,0,0,11,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,7,0,0,0,7,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,6,0,0,0,0,0,0,0,6,0,0,0,8,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,11,0,0,0,8,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,5,0,0,0,8,0,0,0,0,0,0,0,10,0,0,0,0,0,0,0,10,0,0,0,8,0,0,0,0,0,0,0,13,0,0,0,0,0,0,0,13,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,3,0,0,0,1,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,8,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,0,0,0,0,13,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,9,0,0,0,0,0,0,0,7,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,9,0,0,0,0,0,0,0,4,0,0,0,5,0,0,0,0,0,0,0,18,0,0,0,0,0,0,0,18,0,0,0,6,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0],[\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0],we),He.__str117=_([95,71,76,79,66,65,76,95,0],\"i8\",we),He.__str118=_([103,108,111,98,97,108,32,99,111,110,115,116,114,117,99,116,111,114,115,32,107,101,121,101,100,32,116,111,32,0],\"i8\",we),He.__str119=_([103,108,111,98,97,108,32,100,101,115,116,114,117,99,116,111,114,115,32,107,101,121,101,100,32,116,111,32,0],\"i8\",we),He.__str120=_([58,58,0],\"i8\",we),He.__str121=_([118,116,97,98,108,101,32,102,111,114,32,0],\"i8\",we),He.__str122=_([86,84,84,32,102,111,114,32,0],\"i8\",we),He.__str123=_([99,111,110,115,116,114,117,99,116,105,111,110,32,118,116,97,98,108,101,32,102,111,114,32,0],\"i8\",we),He.__str124=_([45,105,110,45,0],\"i8\",we),He.__str125=_([116,121,112,101,105,110,102,111,32,102,111,114,32,0],\"i8\",we),He.__str126=_([116,121,112,101,105,110,102,111,32,110,97,109,101,32,102,111,114,32,0],\"i8\",we),He.__str127=_([116,121,112,101,105,110,102,111,32,102,110,32,102,111,114,32,0],\"i8\",we),He.__str128=_([110,111,110,45,118,105,114,116,117,97,108,32,116,104,117,110,107,32,116,111,32,0],\"i8\",we),He.__str129=_([118,105,114,116,117,97,108,32,116,104,117,110,107,32,116,111,32,0],\"i8\",we),He.__str130=_([99,111,118,97,114,105,97,110,116,32,114,101,116,117,114,110,32,116,104,117,110,107,32,116,111,32,0],\"i8\",we),He.__str131=_([106,97,118,97,32,67,108,97,115,115,32,102,111,114,32,0],\"i8\",we),He.__str132=_([103,117,97,114,100,32,118,97,114,105,97,98,108,101,32,102,111,114,32,0],\"i8\",we),He.__str133=_([114,101,102,101,114,101,110,99,101,32,116,101,109,112,111,114,97,114,121,32,102,111,114,32,0],\"i8\",we),He.__str134=_([104,105,100,100,101,110,32,97,108,105,97,115,32,102,111,114,32,0],\"i8\",we),He.__str135=_([58,58,42,0],\"i8\",we),He.__str136=_([44,32,0],\"i8\",we),He.__str137=_([111,112,101,114,97,116,111,114,0],\"i8\",we),He.__str139=_([41,32,0],\"i8\",we),He.__str140=_([32,40,0],\"i8\",we),He.__str141=_([41,32,58,32,40,0],\"i8\",we),He.__str142=_([117,108,0],\"i8\",we),He.__str143=_([108,108,0],\"i8\",we),He.__str144=_([117,108,108,0],\"i8\",we),He.__str145=_([102,97,108,115,101,0],\"i8\",we),He.__str146=_([116,114,117,101,0],\"i8\",we),He.__str147=_([32,114,101,115,116,114,105,99,116,0],\"i8\",we),He.__str148=_([32,118,111,108,97,116,105,108,101,0],\"i8\",we),He.__str149=_([32,99,111,110,115,116,0],\"i8\",we),He.__str150=_([99,111,109,112,108,101,120,32,0],\"i8\",we),He.__str151=_([105,109,97,103,105,110,97,114,121,32,0],\"i8\",we),ei=_([116,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,0,0,0,0,97,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,9,0,0,0,98,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,17,0,0,0,0,0,0,0,12,0,0,0,115,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,70,0,0,0,0,0,0,0,12,0,0,0,105,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,49,0,0,0,0,0,0,0,13,0,0,0,111,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,49,0,0,0,0,0,0,0,13,0,0,0,100,0,0,0,0,0,0,0,13,0,0,0,0,0,0,0,50,0,0,0,0,0,0,0,14,0,0,0],[\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i8\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0],we),He.__str152=_([115,116,100,0],\"i8\",we),He.__str153=_([115,116,100,58,58,97,108,108,111,99,97,116,111,114,0],\"i8\",we),He.__str154=_([97,108,108,111,99,97,116,111,114,0],\"i8\",we),He.__str155=_([115,116,100,58,58,98,97,115,105,99,95,115,116,114,105,110,103,0],\"i8\",we),He.__str156=_([98,97,115,105,99,95,115,116,114,105,110,103,0],\"i8\",we),He.__str157=_([115,116,100,58,58,115,116,114,105,110,103,0],\"i8\",we),He.__str158=_([115,116,100,58,58,98,97,115,105,99,95,115,116,114,105,110,103,60,99,104,97,114,44,32,115,116,100,58,58,99,104,97,114,95,116,114,97,105,116,115,60,99,104,97,114,62,44,32,115,116,100,58,58,97,108,108,111,99,97,116,111,114,60,99,104,97,114,62,32,62,0],\"i8\",we),He.__str159=_([115,116,100,58,58,105,115,116,114,101,97,109,0],\"i8\",we),He.__str160=_([115,116,100,58,58,98,97,115,105,99,95,105,115,116,114,101,97,109,60,99,104,97,114,44,32,115,116,100,58,58,99,104,97,114,95,116,114,97,105,116,115,60,99,104,97,114,62,32,62,0],\"i8\",we),He.__str161=_([98,97,115,105,99,95,105,115,116,114,101,97,109,0],\"i8\",we),He.__str162=_([115,116,100,58,58,111,115,116,114,101,97,109,0],\"i8\",we),He.__str163=_([115,116,100,58,58,98,97,115,105,99,95,111,115,116,114,101,97,109,60,99,104,97,114,44,32,115,116,100,58,58,99,104,97,114,95,116,114,97,105,116,115,60,99,104,97,114,62,32,62,0],\"i8\",we),He.__str164=_([98,97,115,105,99,95,111,115,116,114,101,97,109,0],\"i8\",we),He.__str165=_([115,116,100,58,58,105,111,115,116,114,101,97,109,0],\"i8\",we),He.__str166=_([115,116,100,58,58,98,97,115,105,99,95,105,111,115,116,114,101,97,109,60,99,104,97,114,44,32,115,116,100,58,58,99,104,97,114,95,116,114,97,105,116,115,60,99,104,97,114,62,32,62,0],\"i8\",we),He.__str167=_([98,97,115,105,99,95,105,111,115,116,114,101,97,109,0],\"i8\",we),He.__str168=_([115,116,114,105,110,103,32,108,105,116,101,114,97,108,0],\"i8\",we),He.__str169=_([40,97,110,111,110,121,109,111,117,115,32,110,97,109,101,115,112,97,99,101,41,0],\"i8\",we),He._symbol_demangle_dashed_null=_([45,45,110,117,108,108,45,45,0],\"i8\",we),He.__str170=_([37,115,37,115,0],\"i8\",we),He.__str1171=_([111,112,101,114,97,116,111,114,32,110,101,119,0],\"i8\",we),He.__str2172=_([111,112,101,114,97,116,111,114,32,100,101,108,101,116,101,0],\"i8\",we),He.__str3173=_([111,112,101,114,97,116,111,114,61,0],\"i8\",we),He.__str4174=_([111,112,101,114,97,116,111,114,62,62,0],\"i8\",we),He.__str5175=_([111,112,101,114,97,116,111,114,60,60,0],\"i8\",we),He.__str6176=_([111,112,101,114,97,116,111,114,33,0],\"i8\",we),He.__str7177=_([111,112,101,114,97,116,111,114,61,61,0],\"i8\",we),He.__str8178=_([111,112,101,114,97,116,111,114,33,61,0],\"i8\",we),He.__str9179=_([111,112,101,114,97,116,111,114,91,93,0],\"i8\",we),He.__str10180=_([111,112,101,114,97,116,111,114,32,0],\"i8\",we),He.__str11181=_([111,112,101,114,97,116,111,114,45,62,0],\"i8\",we),He.__str12182=_([111,112,101,114,97,116,111,114,42,0],\"i8\",we),He.__str13183=_([111,112,101,114,97,116,111,114,43,43,0],\"i8\",we),He.__str14184=_([111,112,101,114,97,116,111,114,45,45,0],\"i8\",we),He.__str15185=_([111,112,101,114,97,116,111,114,45,0],\"i8\",we),He.__str16186=_([111,112,101,114,97,116,111,114,43,0],\"i8\",we),He.__str17187=_([111,112,101,114,97,116,111,114,38,0],\"i8\",we),He.__str18188=_([111,112,101,114,97,116,111,114,45,62,42,0],\"i8\",we),He.__str19189=_([111,112,101,114,97,116,111,114,47,0],\"i8\",we),He.__str20190=_([111,112,101,114,97,116,111,114,37,0],\"i8\",we),He.__str21191=_([111,112,101,114,97,116,111,114,60,0],\"i8\",we),He.__str22192=_([111,112,101,114,97,116,111,114,60,61,0],\"i8\",we),He.__str23193=_([111,112,101,114,97,116,111,114,62,0],\"i8\",we),He.__str24194=_([111,112,101,114,97,116,111,114,62,61,0],\"i8\",we),He.__str25195=_([111,112,101,114,97,116,111,114,44,0],\"i8\",we),He.__str26196=_([111,112,101,114,97,116,111,114,40,41,0],\"i8\",we),He.__str27197=_([111,112,101,114,97,116,111,114,126,0],\"i8\",we),He.__str28198=_([111,112,101,114,97,116,111,114,94,0],\"i8\",we),He.__str29199=_([111,112,101,114,97,116,111,114,124,0],\"i8\",we),He.__str30200=_([111,112,101,114,97,116,111,114,38,38,0],\"i8\",we),He.__str31201=_([111,112,101,114,97,116,111,114,124,124,0],\"i8\",we),He.__str32202=_([111,112,101,114,97,116,111,114,42,61,0],\"i8\",we),He.__str33203=_([111,112,101,114,97,116,111,114,43,61,0],\"i8\",we),He.__str34204=_([111,112,101,114,97,116,111,114,45,61,0],\"i8\",we),He.__str35205=_([111,112,101,114,97,116,111,114,47,61,0],\"i8\",we),He.__str36206=_([111,112,101,114,97,116,111,114,37,61,0],\"i8\",we),He.__str37207=_([111,112,101,114,97,116,111,114,62,62,61,0],\"i8\",we),He.__str38208=_([111,112,101,114,97,116,111,114,60,60,61,0],\"i8\",we),He.__str39209=_([111,112,101,114,97,116,111,114,38,61,0],\"i8\",we),He.__str40210=_([111,112,101,114,97,116,111,114,124,61,0],\"i8\",we),He.__str41211=_([111,112,101,114,97,116,111,114,94,61,0],\"i8\",we),He.__str42212=_([96,118,102,116,97,98,108,101,39,0],\"i8\",we),He.__str43213=_([96,118,98,116,97,98,108,101,39,0],\"i8\",we),He.__str44214=_([96,118,99,97,108,108,39,0],\"i8\",we),He.__str45215=_([96,116,121,112,101,111,102,39,0],\"i8\",we),He.__str46216=_([96,108,111,99,97,108,32,115,116,97,116,105,99,32,103,117,97,114,100,39,0],\"i8\",we),He.__str47217=_([96,115,116,114,105,110,103,39,0],\"i8\",we),He.__str48218=_([96,118,98,97,115,101,32,100,101,115,116,114,117,99,116,111,114,39,0],\"i8\",we),He.__str49219=_([96,118,101,99,116,111,114,32,100,101,108,101,116,105,110,103,32,100,101,115,116,114,117,99,116,111,114,39,0],\"i8\",we),He.__str50220=_([96,100,101,102,97,117,108,116,32,99,111,110,115,116,114,117,99,116,111,114,32,99,108,111,115,117,114,101,39,0],\"i8\",we),He.__str51221=_([96,115,99,97,108,97,114,32,100,101,108,101,116,105,110,103,32,100,101,115,116,114,117,99,116,111,114,39,0],\"i8\",we),He.__str52222=_([96,118,101,99,116,111,114,32,99,111,110,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str53223=_([96,118,101,99,116,111,114,32,100,101,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str54224=_([96,118,101,99,116,111,114,32,118,98,97,115,101,32,99,111,110,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str55225=_([96,118,105,114,116,117,97,108,32,100,105,115,112,108,97,99,101,109,101,110,116,32,109,97,112,39,0],\"i8\",we),He.__str56226=_([96,101,104,32,118,101,99,116,111,114,32,99,111,110,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str57227=_([96,101,104,32,118,101,99,116,111,114,32,100,101,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str58228=_([96,101,104,32,118,101,99,116,111,114,32,118,98,97,115,101,32,99,111,110,115,116,114,117,99,116,111,114,32,105,116,101,114,97,116,111,114,39,0],\"i8\",we),He.__str59229=_([96,99,111,112,121,32,99,111,110,115,116,114,117,99,116,111,114,32,99,108,111,115,117,114,101,39,0],\"i8\",we),He.__str60230=_([37,115,37,115,32,96,82,84,84,73,32,84,121,112,101,32,68,101,115,99,114,105,112,116,111,114,39,0],\"i8\",we),He.__str61231=_([96,82,84,84,73,32,66,97,115,101,32,67,108,97,115,115,32,68,101,115,99,114,105,112,116,111,114,32,97,116,32,40,37,115,44,37,115,44,37,115,44,37,115,41,39,0],\"i8\",we),He.__str62232=_([96,82,84,84,73,32,66,97,115,101,32,67,108,97,115,115,32,65,114,114,97,121,39,0],\"i8\",we),He.__str63233=_([96,82,84,84,73,32,67,108,97,115,115,32,72,105,101,114,97,114,99,104,121,32,68,101,115,99,114,105,112,116,111,114,39,0],\"i8\",we),He.__str64234=_([96,82,84,84,73,32,67,111,109,112,108,101,116,101,32,79,98,106,101,99,116,32,76,111,99,97,116,111,114,39,0],\"i8\",we),He.__str65235=_([96,108,111,99,97,108,32,118,102,116,97,98,108,101,39,0],\"i8\",we),He.__str66236=_([96,108,111,99,97,108,32,118,102,116,97,98,108,101,32,99,111,110,115,116,114,117,99,116,111,114,32,99,108,111,115,117,114,101,39,0],\"i8\",we),He.__str67237=_([111,112,101,114,97,116,111,114,32,110,101,119,91,93,0],\"i8\",we),He.__str68238=_([111,112,101,114,97,116,111,114,32,100,101,108,101,116,101,91,93,0],\"i8\",we),He.__str69239=_([96,112,108,97,99,101,109,101,110,116,32,100,101,108,101,116,101,32,99,108,111,115,117,114,101,39,0],\"i8\",we),He.__str70240=_([96,112,108,97,99,101,109,101,110,116,32,100,101,108,101,116,101,91,93,32,99,108,111,115,117,114,101,39,0],\"i8\",we),He.__str71241=_([126,37,115,0],\"i8\",we),He.__str72242=_([117,110,100,110,97,109,101,46,99,0],\"i8\",we),He.___func___symbol_demangle=_([115,121,109,98,111,108,95,100,101,109,97,110,103,108,101,0],\"i8\",we),He.__str73243=_([115,121,109,45,62,114,101,115,117,108,116,0],\"i8\",we),He.___func___handle_template=_([104,97,110,100,108,101,95,116,101,109,112,108,97,116,101,0],\"i8\",we),He.__str74244=_([42,115,121,109,45,62,99,117,114,114,101,110,116,32,61,61,32,39,36,39,0],\"i8\",we),He.___func___str_array_get_ref=_([115,116,114,95,97,114,114,97,121,95,103,101,116,95,114,101,102,0],\"i8\",we),He.__str75245=_([99,114,101,102,0],\"i8\",we),He.__str76246=_([112,114,105,118,97,116,101,58,32,0],\"i8\",we),He.__str77247=_([112,114,111,116,101,99,116,101,100,58,32,0],\"i8\",we),He.__str78248=_([112,117,98,108,105,99,58,32,0],\"i8\",we),He.__str79249=_([115,116,97,116,105,99,32,0],\"i8\",we),He.__str80250=_([118,105,114,116,117,97,108,32,0],\"i8\",we),He.__str81251=_([91,116,104,117,110,107,93,58,37,115,0],\"i8\",we),He.__str82252=_([37,115,96,97,100,106,117,115,116,111,114,123,37,115,125,39,32,0],\"i8\",we),He.__str83253=_([37,115,32,37,115,0],\"i8\",we),He.__str84254=_([118,111,105,100,0],\"i8\",we),He.__str85255=_([37,115,37,115,37,115,0],\"i8\",we),He.__str86256=_([37,115,37,115,37,115,37,115,37,115,37,115,37,115,37,115,37,115,37,115,37,115,0],\"i8\",we),He.__str87257=_([32,0],\"i8\",we),He.__str88258=_([100,108,108,95,101,120,112,111,114,116,32,0],\"i8\",we),He.__str89259=_([99,100,101,99,108,0],\"i8\",we),He.__str90260=_([112,97,115,99,97,108,0],\"i8\",we),He.__str91261=_([116,104,105,115,99,97,108,108,0],\"i8\",we),He.__str92262=_([115,116,100,99,97,108,108,0],\"i8\",we),He.__str93263=_([102,97,115,116,99,97,108,108,0],\"i8\",we),He.__str94264=_([99,108,114,99,97,108,108,0],\"i8\",we),He.__str95265=_([95,95,100,108,108,95,101,120,112,111,114,116,32,0],\"i8\",we),He.__str96266=_([95,95,99,100,101,99,108,0],\"i8\",we),He.__str97267=_([95,95,112,97,115,99,97,108,0],\"i8\",we),He.__str98268=_([95,95,116,104,105,115,99,97,108,108,0],\"i8\",we),He.__str99269=_([95,95,115,116,100,99,97,108,108,0],\"i8\",we),He.__str100270=_([95,95,102,97,115,116,99,97,108,108,0],\"i8\",we),He.__str101271=_([95,95,99,108,114,99,97,108,108,0],\"i8\",we),He.__str102272=_([95,95,112,116,114,54,52,0],\"i8\",we),He.__str103273=_([99,111,110,115,116,0],\"i8\",we),He.__str104274=_([118,111,108,97,116,105,108,101,0],\"i8\",we),He.__str105275=_([99,111,110,115,116,32,118,111,108,97,116,105,108,101,0],\"i8\",we),He.___func___get_class_string=_([103,101,116,95,99,108,97,115,115,95,115,116,114,105,110,103,0],\"i8\",we),He.__str106276=_([97,45,62,101,108,116,115,91,105,93,0],\"i8\",we),He.__str107277=_([123,102,111,114,32,96,37,115,39,125,0],\"i8\",we),He.__str108278=_([37,115,37,115,37,115,37,115,37,115,37,115,37,115,37,115,0],\"i8\",we),He.__str109279=_([96,37,115,39,0],\"i8\",we),He.__str110280=_([46,46,46,0],\"i8\",we),He.__str111281=_([37,99,118,111,105,100,37,99,0],\"i8\",we),He.__str112282=_([37,115,44,37,115,0],\"i8\",we),He.__str113283=_([37,99,37,115,37,115,32,37,99,0],\"i8\",we),He.__str114284=_([37,99,37,115,37,115,37,99,0],\"i8\",we),He.___func___str_array_push=_([115,116,114,95,97,114,114,97,121,95,112,117,115,104,0],\"i8\",we),He.__str115285=_([112,116,114,0],\"i8\",we),He.__str116286=_([97,0],\"i8\",we),He.__str117287=_([97,45,62,101,108,116,115,91,97,45,62,110,117,109,93,0],\"i8\",we),He.__str118288=_([37,115,37,100,0],\"i8\",we),He.__str119289=_([45,0],\"i8\",we),ii=_(1,\"i8\",we),He.___func___demangle_datatype=_([100,101,109,97,110,103,108,101,95,100,97,116,97,116,121,112,101,0],\"i8\",we),He.__str121291=_([99,116,0],\"i8\",we),He.__str122292=_([117,110,105,111,110,32,0],\"i8\",we),He.__str123293=_([115,116,114,117,99,116,32,0],\"i8\",we),He.__str124294=_([99,108,97,115,115,32,0],\"i8\",we),He.__str125295=_([99,111,105,110,116,101,114,102,97,99,101,32,0],\"i8\",we),He.__str126296=_([96,116,101,109,112,108,97,116,101,45,112,97,114,97,109,101,116,101,114,45,37,115,39,0],\"i8\",we),He.__str127297=_([37,115,37,115,32,40,37,115,42,0],\"i8\",we),He.__str128298=_([41,37,115,0],\"i8\",we),He.__str129299=_([101,110,117,109,32,37,115,0],\"i8\",we),He.__str130300=_([96,116,101,109,112,108,97,116,101,45,112,97,114,97,109,101,116,101,114,37,115,39,0],\"i8\",we),He.__str131301=_([123,37,115,44,37,115,125,0],\"i8\",we),He.__str132302=_([123,37,115,44,37,115,44,37,115,125,0],\"i8\",we),He.__str133303=_([96,110,111,110,45,116,121,112,101,45,116,101,109,112,108,97,116,101,45,112,97,114,97,109,101,116,101,114,37,115,39,0],\"i8\",we),He.__str134304=_([32,95,95,112,116,114,54,52,0],\"i8\",we),He.__str135305=_([32,38,37,115,0],\"i8\",we),He.__str136306=_([32,38,37,115,32,118,111,108,97,116,105,108,101,0],\"i8\",we),He.__str137307=_([32,42,37,115,0],\"i8\",we),He.__str138308=_([32,42,37,115,32,99,111,110,115,116,0],\"i8\",we),He.__str139309=_([32,42,37,115,32,118,111,108,97,116,105,108,101,0],\"i8\",we),He.__str140310=_([32,42,37,115,32,99,111,110,115,116,32,118,111,108,97,116,105,108,101,0],\"i8\",we),He.__str141311=_([32,40,37,115,37,115,41,0],\"i8\",we),He.__str142312=_([32,40,37,115,41,0],\"i8\",we),He.__str143313=_([37,115,91,37,115,93,0],\"i8\",we),He.__str144314=_([37,115,32,37,115,37,115,0],\"i8\",we),He.__str145315=_([115,105,103,110,101,100,32,99,104,97,114,0],\"i8\",we),He.__str146316=_([99,104,97,114,0],\"i8\",we),He.__str147317=_([117,110,115,105,103,110,101,100,32,99,104,97,114,0],\"i8\",we),He.__str148318=_([115,104,111,114,116,0],\"i8\",we),He.__str149319=_([117,110,115,105,103,110,101,100,32,115,104,111,114,116,0],\"i8\",we),He.__str150320=_([105,110,116,0],\"i8\",we),He.__str151321=_([117,110,115,105,103,110,101,100,32,105,110,116,0],\"i8\",we),He.__str152322=_([108,111,110,103,0],\"i8\",we),He.__str153323=_([117,110,115,105,103,110,101,100,32,108,111,110,103,0],\"i8\",we),He.__str154324=_([102,108,111,97,116,0],\"i8\",we),He.__str155325=_([100,111,117,98,108,101,0],\"i8\",we),He.__str156326=_([108,111,110,103,32,100,111,117,98,108,101,0],\"i8\",we),He.__str157327=_([95,95,105,110,116,56,0],\"i8\",we),He.__str158328=_([117,110,115,105,103,110,101,100,32,95,95,105,110,116,56,0],\"i8\",we),He.__str159329=_([95,95,105,110,116,49,54,0],\"i8\",we),He.__str160330=_([117,110,115,105,103,110,101,100,32,95,95,105,110,116,49,54,0],\"i8\",we),He.__str161331=_([95,95,105,110,116,51,50,0],\"i8\",we),He.__str162332=_([117,110,115,105,103,110,101,100,32,95,95,105,110,116,51,50,0],\"i8\",we),He.__str163333=_([95,95,105,110,116,54,52,0],\"i8\",we),He.__str164334=_([117,110,115,105,103,110,101,100,32,95,95,105,110,116,54,52,0],\"i8\",we),\nHe.__str165335=_([95,95,105,110,116,49,50,56,0],\"i8\",we),He.__str166336=_([117,110,115,105,103,110,101,100,32,95,95,105,110,116,49,50,56,0],\"i8\",we),He.__str167337=_([98,111,111,108,0],\"i8\",we),He.__str168338=_([119,99,104,97,114,95,116,0],\"i8\",we),vi=_(468,[\"i32\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0,\"*\",0,0,0,\"i32\",0,0,0],we),ti=_(24,\"i32\",we),He.__str339=_([109,97,120,32,115,121,115,116,101,109,32,98,121,116,101,115,32,61,32,37,49,48,108,117,10,0],\"i8\",we),He.__str1340=_([115,121,115,116,101,109,32,98,121,116,101,115,32,32,32,32,32,61,32,37,49,48,108,117,10,0],\"i8\",we),He.__str2341=_([105,110,32,117,115,101,32,98,121,116,101,115,32,32,32,32,32,61,32,37,49,48,108,117,10,0],\"i8\",we),fi=_([ue],\"i8\",we),_i=_(1,\"void ()*\",we),si=_([0,0,0,0,0,0,0,0,6,0,0,0,8,0,0,0,10,0,0,0],[\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0],we),_(1,\"void*\",we),He.__str3342=_([115,116,100,58,58,98,97,100,95,97,108,108,111,99,0],\"i8\",we),ni=_([0,0,0,0,0,0,0,0,6,0,0,0,12,0,0,0,14,0,0,0],[\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0,\"*\",0,0,0],we),_(1,\"void*\",we),He.__str14343=_([98,97,100,95,97,114,114,97,121,95,110,101,119,95,108,101,110,103,116,104,0],\"i8\",we),He.__ZTSSt9bad_alloc=_([83,116,57,98,97,100,95,97,108,108,111,99,0],\"i8\",we),bi=_(12,\"*\",we),He.__ZTSSt20bad_array_new_length=_([83,116,50,48,98,97,100,95,97,114,114,97,121,95,110,101,119,95,108,101,110,103,116,104,0],\"i8\",we),ki=_(12,\"*\",we),Se[ri>>2]=0|He.__str,Se[ri+4>>2]=0|He.__str1,Se[ri+16>>2]=0|He.__str2,Se[ri+20>>2]=0|He.__str3,Se[ri+32>>2]=0|He.__str4,Se[ri+36>>2]=0|He.__str5,Se[ri+48>>2]=0|He.__str6,Se[ri+52>>2]=0|He.__str7,Se[ri+64>>2]=0|He.__str8,Se[ri+68>>2]=0|He.__str7,Se[ri+80>>2]=0|He.__str9,Se[ri+84>>2]=0|He.__str10,Se[ri+96>>2]=0|He.__str11,Se[ri+100>>2]=0|He.__str12,Se[ri+112>>2]=0|He.__str13,Se[ri+116>>2]=0|He.__str14,Se[ri+128>>2]=0|He.__str15,Se[ri+132>>2]=0|He.__str16,Se[ri+144>>2]=0|He.__str17,Se[ri+148>>2]=0|He.__str18,Se[ri+160>>2]=0|He.__str19,Se[ri+164>>2]=0|He.__str20,Se[ri+176>>2]=0|He.__str21,Se[ri+180>>2]=0|He.__str22,Se[ri+192>>2]=0|He.__str23,Se[ri+196>>2]=0|He.__str24,Se[ri+208>>2]=0|He.__str25,Se[ri+212>>2]=0|He.__str26,Se[ri+224>>2]=0|He.__str27,Se[ri+228>>2]=0|He.__str28,Se[ri+240>>2]=0|He.__str29,Se[ri+244>>2]=0|He.__str30,Se[ri+256>>2]=0|He.__str31,Se[ri+260>>2]=0|He.__str32,Se[ri+272>>2]=0|He.__str33,Se[ri+276>>2]=0|He.__str34,Se[ri+288>>2]=0|He.__str35,Se[ri+292>>2]=0|He.__str36,Se[ri+304>>2]=0|He.__str37,Se[ri+308>>2]=0|He.__str38,Se[ri+320>>2]=0|He.__str39,Se[ri+324>>2]=0|He.__str40,Se[ri+336>>2]=0|He.__str41,Se[ri+340>>2]=0|He.__str42,Se[ri+352>>2]=0|He.__str43,Se[ri+356>>2]=0|He.__str44,Se[ri+368>>2]=0|He.__str45,Se[ri+372>>2]=0|He.__str46,Se[ri+384>>2]=0|He.__str47,Se[ri+388>>2]=0|He.__str48,Se[ri+400>>2]=0|He.__str49,Se[ri+404>>2]=0|He.__str119289,Se[ri+416>>2]=0|He.__str51,Se[ri+420>>2]=0|He.__str20,Se[ri+432>>2]=0|He.__str52,Se[ri+436>>2]=0|He.__str53,Se[ri+448>>2]=0|He.__str54,Se[ri+452>>2]=0|He.__str55,Se[ri+464>>2]=0|He.__str56,Se[ri+468>>2]=0|He.__str57,Se[ri+480>>2]=0|He.__str58,Se[ri+484>>2]=0|He.__str119289,Se[ri+496>>2]=0|He.__str59,Se[ri+500>>2]=0|He.__str60,Se[ri+512>>2]=0|He.__str61,Se[ri+516>>2]=0|He.__str62,Se[ri+528>>2]=0|He.__str63,Se[ri+532>>2]=0|He.__str64,Se[ri+544>>2]=0|He.__str65,Se[ri+548>>2]=0|He.__str66,Se[ri+560>>2]=0|He.__str67,Se[ri+564>>2]=0|He.__str68,Se[ri+576>>2]=0|He.__str69,Se[ri+580>>2]=0|He.__str70,Se[ri+592>>2]=0|He.__str71,Se[ri+596>>2]=0|He.__str72,Se[ri+608>>2]=0|He.__str73,Se[ri+612>>2]=0|He.__str74,Se[ri+624>>2]=0|He.__str75,Se[ri+628>>2]=0|He.__str76,Se[ri+640>>2]=0|He.__str77,Se[ri+644>>2]=0|He.__str72,Se[ri+656>>2]=0|He.__str78,Se[ri+660>>2]=0|He.__str79,Se[ri+672>>2]=0|He.__str80,Se[ri+676>>2]=0|He.__str81,Se[ri+688>>2]=0|He.__str82,Se[ri+692>>2]=0|He.__str83,Se[ri+704>>2]=0|He.__str84,Se[ri+708>>2]=0|He.__str85,Se[ri+720>>2]=0|He.__str86,Se[ri+724>>2]=0|He.__str87,Se[ri+736>>2]=0|He.__str88,Se[ri+740>>2]=0|He.__str89,Se[ri+752>>2]=0|He.__str90,Se[ri+756>>2]=0|He.__str91,Se[ri+768>>2]=0|He.__str92,Se[ri+772>>2]=0|He.__str91,Se[ai>>2]=0|He.__str145315,Se[ai+8>>2]=0|He.__str145315,Se[ai+20>>2]=0|He.__str167337,Se[ai+28>>2]=0|He.__str95,Se[ai+40>>2]=0|He.__str146316,Se[ai+48>>2]=0|He.__str97,Se[ai+60>>2]=0|He.__str155325,Se[ai+68>>2]=0|He.__str155325,Se[ai+80>>2]=0|He.__str156326,Se[ai+88>>2]=0|He.__str156326,Se[ai+100>>2]=0|He.__str154324,Se[ai+108>>2]=0|He.__str154324,Se[ai+120>>2]=0|He.__str101,Se[ai+128>>2]=0|He.__str101,Se[ai+140>>2]=0|He.__str147317,Se[ai+148>>2]=0|He.__str147317,Se[ai+160>>2]=0|He.__str150320,Se[ai+168>>2]=0|He.__str150320,Se[ai+180>>2]=0|He.__str151321,Se[ai+188>>2]=0|He.__str105,Se[ai+220>>2]=0|He.__str152322,Se[ai+228>>2]=0|He.__str152322,Se[ai+240>>2]=0|He.__str153323,Se[ai+248>>2]=0|He.__str153323,Se[ai+260>>2]=0|He.__str165335,Se[ai+268>>2]=0|He.__str165335,Se[ai+280>>2]=0|He.__str166336,Se[ai+288>>2]=0|He.__str166336,Se[ai+360>>2]=0|He.__str148318,Se[ai+368>>2]=0|He.__str148318,Se[ai+380>>2]=0|He.__str149319,Se[ai+388>>2]=0|He.__str149319,Se[ai+420>>2]=0|He.__str84254,Se[ai+428>>2]=0|He.__str84254,Se[ai+440>>2]=0|He.__str168338,Se[ai+448>>2]=0|He.__str146316,Se[ai+460>>2]=0|He.__str114,Se[ai+468>>2]=0|He.__str152322,Se[ai+480>>2]=0|He.__str115,Se[ai+488>>2]=0|He.__str115,Se[ai+500>>2]=0|He.__str110280,Se[ai+508>>2]=0|He.__str110280,Se[ei+4>>2]=0|He.__str152,Se[ei+12>>2]=0|He.__str152,Se[ei+32>>2]=0|He.__str153,Se[ei+40>>2]=0|He.__str153,Se[ei+48>>2]=0|He.__str154,Se[ei+60>>2]=0|He.__str155,Se[ei+68>>2]=0|He.__str155,Se[ei+76>>2]=0|He.__str156,Se[ei+88>>2]=0|He.__str157,Se[ei+96>>2]=0|He.__str158,Se[ei+104>>2]=0|He.__str156,Se[ei+116>>2]=0|He.__str159,Se[ei+124>>2]=0|He.__str160,Se[ei+132>>2]=0|He.__str161,Se[ei+144>>2]=0|He.__str162,Se[ei+152>>2]=0|He.__str163,Se[ei+160>>2]=0|He.__str164,Se[ei+172>>2]=0|He.__str165,Se[ei+180>>2]=0|He.__str166,Se[ei+188>>2]=0|He.__str167,Se[si+4>>2]=bi,Se[ni+4>>2]=ki,oi=_([2,0,0,0,0],[\"i8*\",0,0,0,0],we),Se[bi>>2]=oi+8|0,Se[bi+4>>2]=0|He.__ZTSSt9bad_alloc,Se[bi+8>>2]=li,Se[ki>>2]=oi+8|0,Se[ki+4>>2]=0|He.__ZTSSt20bad_array_new_length,Se[ki+8>>2]=bi,ui=16,ci=6,hi=18,di=6,wi=6,pe=[0,0,Jr,0,va,0,ya,0,ga,0,wa,0,Sa,0,pa,0,Ea,0,ma,0],Module.FUNCTION_TABLE=pe,Module.run=ee,Module.preRun&&Module.preRun(),0==Ke){ee()}Module.postRun&&Module.postRun(),Module.___cxa_demangle=G;var pi=v(\"__cxa_demangle\",\"string\",[\"string\",\"string\",\"number\",\"number\"]);return function(r){return pi(r,\"\",1,0)}}();\n`;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Flamechart = void 0;\nconst utils_1 = require(\"./utils\");\nconst math_1 = require(\"./math\");\nclass Flamechart {\n    constructor(source) {\n        this.source = source;\n        // Bottom to top\n        this.layers = [];\n        this.totalWeight = 0;\n        this.minFrameWidth = 1;\n        const stack = [];\n        const openFrame = (node, value) => {\n            const parent = utils_1.lastOf(stack);\n            const frame = {\n                node,\n                parent,\n                children: [],\n                start: value,\n                end: value,\n            };\n            if (parent) {\n                parent.children.push(frame);\n            }\n            stack.push(frame);\n        };\n        this.minFrameWidth = Infinity;\n        const closeFrame = (node, value) => {\n            console.assert(stack.length > 0);\n            const stackTop = stack.pop();\n            stackTop.end = value;\n            if (stackTop.end - stackTop.start === 0)\n                return;\n            const layerIndex = stack.length;\n            while (this.layers.length <= layerIndex)\n                this.layers.push([]);\n            this.layers[layerIndex].push(stackTop);\n            this.minFrameWidth = Math.min(this.minFrameWidth, stackTop.end - stackTop.start);\n        };\n        this.totalWeight = source.getTotalWeight();\n        source.forEachCall(openFrame, closeFrame);\n        if (!isFinite(this.minFrameWidth))\n            this.minFrameWidth = 1;\n    }\n    getTotalWeight() {\n        return this.totalWeight;\n    }\n    getLayers() {\n        return this.layers;\n    }\n    getColorBucketForFrame(frame) {\n        return this.source.getColorBucketForFrame(frame);\n    }\n    getMinFrameWidth() {\n        return this.minFrameWidth;\n    }\n    formatValue(v) {\n        return this.source.formatValue(v);\n    }\n    getClampedViewportWidth(viewportWidth) {\n        const maxWidth = this.getTotalWeight();\n        // In order to avoid floating point error, we cap the maximum zoom. In\n        // particular, it's important that at the maximum zoom level, the total\n        // trace size + a viewport width is not equal to the trace size due to\n        // floating point rounding.\n        //\n        // For instance, if the profile's total weight is 2^60, and the viewport\n        // size is 1, trying to move one viewport width right will result in no\n        // change because 2^60 + 1 = 2^60 in floating point arithmetic. JavaScript\n        // numbers are 64 bit floats, and therefore have 53 mantissa bits. You can\n        // see this for yourself in the console. Try:\n        //\n        //   > Math.pow(2, 60) + 1 === Math.pow(2, 60)\n        //   true\n        //   > Math.pow(2, 53) + 1 === Math.pow(2, 53)\n        //   true\n        //   > Math.pow(2, 52) + 1 === Math.pow(2, 52)\n        //   false\n        //\n        // We use 2^40 as a cap instead, since we want to be able to make small\n        // adjustments within a viewport width.\n        //\n        // For reference, this will still allow you to zoom until 1 nanosecond fills\n        // the screen in a profile with a duration of over 18 minutes.\n        //\n        //   > Math.pow(2, 40) / (60 * Math.pow(10, 9))\n        //   18.325193796266667\n        //\n        const maxZoom = Math.pow(2, 40);\n        // In addition to capping zoom to avoid floating point error, we further cap\n        // zoom to avoid letting you zoom in so that the smallest element more than\n        // fills the screen, since that probably isn't useful. The final zoom cap is\n        // determined by the minimum zoom of either 2^40x zoom or the necessary zoom\n        // for the smallest frame to fill the screen three times.\n        const minWidth = math_1.clamp(3 * this.getMinFrameWidth(), maxWidth / maxZoom, maxWidth);\n        return math_1.clamp(viewportWidth, minWidth, maxWidth);\n    }\n    // Given a desired config-space viewport rectangle, clamp the rectangle so\n    // that it fits within the given flamechart. This prevents the viewport from\n    // extending past the bounds of the flamechart or zooming in too far.\n    getClampedConfigSpaceViewportRect({ configSpaceViewportRect, renderInverted, }) {\n        const configSpaceSize = new math_1.Vec2(this.getTotalWeight(), this.getLayers().length);\n        const width = this.getClampedViewportWidth(configSpaceViewportRect.size.x);\n        const size = configSpaceViewportRect.size.withX(width);\n        const origin = math_1.Vec2.clamp(configSpaceViewportRect.origin, new math_1.Vec2(0, renderInverted ? 0 : -1), math_1.Vec2.max(math_1.Vec2.zero, configSpaceSize.minus(size).plus(new math_1.Vec2(0, 1))));\n        return new math_1.Rect(origin, configSpaceViewportRect.size.withX(width));\n    }\n}\nexports.Flamechart = Flamechart;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Rect = exports.AffineTransform = exports.Vec2 = exports.clamp = void 0;\nfunction clamp(x, minVal, maxVal) {\n    if (x < minVal)\n        return minVal;\n    if (x > maxVal)\n        return maxVal;\n    return x;\n}\nexports.clamp = clamp;\nlet Vec2 = /** @class */ (() => {\n    class Vec2 {\n        constructor(x, y) {\n            this.x = x;\n            this.y = y;\n        }\n        withX(x) {\n            return new Vec2(x, this.y);\n        }\n        withY(y) {\n            return new Vec2(this.x, y);\n        }\n        plus(other) {\n            return new Vec2(this.x + other.x, this.y + other.y);\n        }\n        minus(other) {\n            return new Vec2(this.x - other.x, this.y - other.y);\n        }\n        times(scalar) {\n            return new Vec2(this.x * scalar, this.y * scalar);\n        }\n        timesPointwise(other) {\n            return new Vec2(this.x * other.x, this.y * other.y);\n        }\n        dividedByPointwise(other) {\n            return new Vec2(this.x / other.x, this.y / other.y);\n        }\n        dot(other) {\n            return this.x * other.x + this.y * other.y;\n        }\n        equals(other) {\n            return this.x === other.x && this.y === other.y;\n        }\n        approxEquals(other, epsilon = 1e-9) {\n            return Math.abs(this.x - other.x) < epsilon && Math.abs(this.y - other.y) < epsilon;\n        }\n        length2() {\n            return this.dot(this);\n        }\n        length() {\n            return Math.sqrt(this.length2());\n        }\n        abs() {\n            return new Vec2(Math.abs(this.x), Math.abs(this.y));\n        }\n        static min(a, b) {\n            return new Vec2(Math.min(a.x, b.x), Math.min(a.y, b.y));\n        }\n        static max(a, b) {\n            return new Vec2(Math.max(a.x, b.x), Math.max(a.y, b.y));\n        }\n        static clamp(v, min, max) {\n            return new Vec2(clamp(v.x, min.x, max.x), clamp(v.y, min.y, max.y));\n        }\n        flatten() {\n            return [this.x, this.y];\n        }\n    }\n    Vec2.zero = new Vec2(0, 0);\n    Vec2.unit = new Vec2(1, 1);\n    return Vec2;\n})();\nexports.Vec2 = Vec2;\nclass AffineTransform {\n    constructor(m00 = 1, m01 = 0, m02 = 0, m10 = 0, m11 = 1, m12 = 0) {\n        this.m00 = m00;\n        this.m01 = m01;\n        this.m02 = m02;\n        this.m10 = m10;\n        this.m11 = m11;\n        this.m12 = m12;\n    }\n    withScale(s) {\n        let { m00, m01, m02, m10, m11, m12 } = this;\n        m00 = s.x;\n        m11 = s.y;\n        return new AffineTransform(m00, m01, m02, m10, m11, m12);\n    }\n    static withScale(s) {\n        return new AffineTransform().withScale(s);\n    }\n    scaledBy(s) {\n        return AffineTransform.withScale(s).times(this);\n    }\n    getScale() {\n        return new Vec2(this.m00, this.m11);\n    }\n    withTranslation(t) {\n        let { m00, m01, m02, m10, m11, m12 } = this;\n        m02 = t.x;\n        m12 = t.y;\n        return new AffineTransform(m00, m01, m02, m10, m11, m12);\n    }\n    static withTranslation(t) {\n        return new AffineTransform().withTranslation(t);\n    }\n    getTranslation() {\n        return new Vec2(this.m02, this.m12);\n    }\n    translatedBy(t) {\n        return AffineTransform.withTranslation(t).times(this);\n    }\n    static betweenRects(from, to) {\n        return AffineTransform.withTranslation(from.origin.times(-1))\n            .scaledBy(new Vec2(to.size.x / from.size.x, to.size.y / from.size.y))\n            .translatedBy(to.origin);\n    }\n    times(other) {\n        const m00 = this.m00 * other.m00 + this.m01 * other.m10;\n        const m01 = this.m00 * other.m01 + this.m01 * other.m11;\n        const m02 = this.m00 * other.m02 + this.m01 * other.m12 + this.m02;\n        const m10 = this.m10 * other.m00 + this.m11 * other.m10;\n        const m11 = this.m10 * other.m01 + this.m11 * other.m11;\n        const m12 = this.m10 * other.m02 + this.m11 * other.m12 + this.m12;\n        return new AffineTransform(m00, m01, m02, m10, m11, m12);\n    }\n    equals(other) {\n        return (this.m00 == other.m00 &&\n            this.m01 == other.m01 &&\n            this.m02 == other.m02 &&\n            this.m10 == other.m10 &&\n            this.m11 == other.m11 &&\n            this.m12 == other.m12);\n    }\n    approxEquals(other, epsilon = 1e-9) {\n        return (Math.abs(this.m00 - other.m00) < epsilon &&\n            Math.abs(this.m01 - other.m01) < epsilon &&\n            Math.abs(this.m02 - other.m02) < epsilon &&\n            Math.abs(this.m10 - other.m10) < epsilon &&\n            Math.abs(this.m11 - other.m11) < epsilon &&\n            Math.abs(this.m12 - other.m12) < epsilon);\n    }\n    timesScalar(s) {\n        const { m00, m01, m02, m10, m11, m12 } = this;\n        return new AffineTransform(s * m00, s * m01, s * m02, s * m10, s * m11, s * m12);\n    }\n    det() {\n        const { m00, m01, m02, m10, m11, m12 } = this;\n        const m20 = 0;\n        const m21 = 0;\n        const m22 = 1;\n        return (m00 * (m11 * m22 - m12 * m21) - m01 * (m10 * m22 - m12 * m20) + m02 * (m10 * m21 - m11 * m20));\n    }\n    adj() {\n        const { m00, m01, m02, m10, m11, m12 } = this;\n        const m20 = 0;\n        const m21 = 0;\n        const m22 = 1;\n        // Adjugate matrix (a) is the transpose of the\n        // cofactor matrix (c).\n        //\n        // 00 01 02\n        // 10 11 12\n        // 20 21 22\n        const a00 = /* c00 = */ +(m11 * m22 - m12 * m21);\n        const a01 = /* c10 = */ -(m01 * m22 - m02 * m21);\n        const a02 = /* c20 = */ +(m01 * m12 - m02 * m11);\n        const a10 = /* c01 = */ -(m10 * m22 - m12 * m20);\n        const a11 = /* c11 = */ +(m00 * m22 - m02 * m20);\n        const a12 = /* c21 = */ -(m00 * m12 - m02 * m10);\n        return new AffineTransform(a00, a01, a02, a10, a11, a12);\n    }\n    inverted() {\n        const det = this.det();\n        if (det === 0)\n            return null;\n        const adj = this.adj();\n        return adj.timesScalar(1 / det);\n    }\n    transformVector(v) {\n        return new Vec2(v.x * this.m00 + v.y * this.m01, v.x * this.m10 + v.y * this.m11);\n    }\n    inverseTransformVector(v) {\n        const inv = this.inverted();\n        if (!inv)\n            return null;\n        return inv.transformVector(v);\n    }\n    transformPosition(v) {\n        return new Vec2(v.x * this.m00 + v.y * this.m01 + this.m02, v.x * this.m10 + v.y * this.m11 + this.m12);\n    }\n    inverseTransformPosition(v) {\n        const inv = this.inverted();\n        if (!inv)\n            return null;\n        return inv.transformPosition(v);\n    }\n    transformRect(r) {\n        const size = this.transformVector(r.size);\n        const origin = this.transformPosition(r.origin);\n        if (size.x < 0 && size.y < 0) {\n            return new Rect(origin.plus(size), size.abs());\n        }\n        else if (size.x < 0) {\n            return new Rect(origin.withX(origin.x + size.x), size.abs());\n        }\n        else if (size.y < 0) {\n            return new Rect(origin.withY(origin.y + size.y), size.abs());\n        }\n        return new Rect(origin, size);\n    }\n    inverseTransformRect(r) {\n        const inv = this.inverted();\n        if (!inv)\n            return null;\n        return inv.transformRect(r);\n    }\n    flatten() {\n        // Flatten into GLSL format\n        // prettier-ignore\n        return [\n            this.m00, this.m10, 0,\n            this.m01, this.m11, 0,\n            this.m02, this.m12, 1,\n        ];\n    }\n}\nexports.AffineTransform = AffineTransform;\nlet Rect = /** @class */ (() => {\n    class Rect {\n        constructor(origin, size) {\n            this.origin = origin;\n            this.size = size;\n        }\n        isEmpty() {\n            return this.width() == 0 || this.height() == 0;\n        }\n        width() {\n            return this.size.x;\n        }\n        height() {\n            return this.size.y;\n        }\n        left() {\n            return this.origin.x;\n        }\n        right() {\n            return this.left() + this.width();\n        }\n        top() {\n            return this.origin.y;\n        }\n        bottom() {\n            return this.top() + this.height();\n        }\n        topLeft() {\n            return this.origin;\n        }\n        topRight() {\n            return this.origin.plus(new Vec2(this.width(), 0));\n        }\n        bottomRight() {\n            return this.origin.plus(this.size);\n        }\n        bottomLeft() {\n            return this.origin.plus(new Vec2(0, this.height()));\n        }\n        withOrigin(origin) {\n            return new Rect(origin, this.size);\n        }\n        withSize(size) {\n            return new Rect(this.origin, size);\n        }\n        closestPointTo(p) {\n            return new Vec2(clamp(p.x, this.left(), this.right()), clamp(p.y, this.top(), this.bottom()));\n        }\n        distanceFrom(p) {\n            return p.minus(this.closestPointTo(p)).length();\n        }\n        contains(p) {\n            return this.distanceFrom(p) === 0;\n        }\n        hasIntersectionWith(other) {\n            const top = Math.max(this.top(), other.top());\n            const bottom = Math.max(top, Math.min(this.bottom(), other.bottom()));\n            if (bottom - top === 0)\n                return false;\n            const left = Math.max(this.left(), other.left());\n            const right = Math.max(left, Math.min(this.right(), other.right()));\n            if (right - left === 0)\n                return false;\n            return true;\n        }\n        intersectWith(other) {\n            const topLeft = Vec2.max(this.topLeft(), other.topLeft());\n            const bottomRight = Vec2.max(topLeft, Vec2.min(this.bottomRight(), other.bottomRight()));\n            return new Rect(topLeft, bottomRight.minus(topLeft));\n        }\n        equals(other) {\n            return this.origin.equals(other.origin) && this.size.equals(other.size);\n        }\n        approxEquals(other) {\n            return this.origin.approxEquals(other.origin) && this.size.approxEquals(other.size);\n        }\n        area() {\n            return this.size.x * this.size.y;\n        }\n    }\n    Rect.empty = new Rect(Vec2.zero, Vec2.zero);\n    Rect.unit = new Rect(Vec2.zero, Vec2.unit);\n    Rect.NDC = new Rect(new Vec2(-1, -1), new Vec2(2, 2));\n    return Rect;\n})();\nexports.Rect = Rect;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.CallTreeProfileBuilder = exports.StackListProfileBuilder = exports.Profile = exports.CallTreeNode = exports.Frame = exports.HasWeights = void 0;\nconst utils_1 = require(\"./utils\");\nconst value_formatters_1 = require(\"./value-formatters\");\nconst demangleCppModule = Promise.resolve().then(() => __importStar(require('./demangle-cpp')));\n// Force eager loading of the module\ndemangleCppModule.then(() => { });\nclass HasWeights {\n    constructor() {\n        this.selfWeight = 0;\n        this.totalWeight = 0;\n    }\n    getSelfWeight() {\n        return this.selfWeight;\n    }\n    getTotalWeight() {\n        return this.totalWeight;\n    }\n    addToTotalWeight(delta) {\n        this.totalWeight += delta;\n    }\n    addToSelfWeight(delta) {\n        this.selfWeight += delta;\n    }\n    overwriteWeightWith(other) {\n        this.selfWeight = other.selfWeight;\n        this.totalWeight = other.totalWeight;\n    }\n}\nexports.HasWeights = HasWeights;\nlet Frame = /** @class */ (() => {\n    class Frame extends HasWeights {\n        constructor(info) {\n            super();\n            this.key = info.key;\n            this.name = info.name;\n            this.file = info.file;\n            this.line = info.line;\n            this.col = info.col;\n        }\n        static getOrInsert(set, info) {\n            return set.getOrInsert(new Frame(info));\n        }\n    }\n    Frame.root = new Frame({\n        key: '(speedscope root)',\n        name: '(speedscope root)',\n    });\n    return Frame;\n})();\nexports.Frame = Frame;\nclass CallTreeNode extends HasWeights {\n    constructor(frame, parent) {\n        super();\n        this.frame = frame;\n        this.parent = parent;\n        this.children = [];\n        // If a node is \"frozen\", it means it should no longer be mutated.\n        this.frozen = false;\n    }\n    isRoot() {\n        return this.frame === Frame.root;\n    }\n    isFrozen() {\n        return this.frozen;\n    }\n    freeze() {\n        this.frozen = true;\n    }\n}\nexports.CallTreeNode = CallTreeNode;\nclass Profile {\n    constructor(totalWeight = 0) {\n        this.name = '';\n        this.frames = new utils_1.KeyedSet();\n        // Profiles store two call-trees.\n        //\n        // The \"append order\" call tree is the one in which nodes are ordered in\n        // whatever order they were appended to their parent.\n        //\n        // The \"grouped\" call tree is one in which each node has at most one child per\n        // frame. Nodes are ordered in decreasing order of weight\n        this.appendOrderCalltreeRoot = new CallTreeNode(Frame.root, null);\n        this.groupedCalltreeRoot = new CallTreeNode(Frame.root, null);\n        // List of references to CallTreeNodes at the top of the\n        // stack at the time of the sample.\n        this.samples = [];\n        this.weights = [];\n        this.valueFormatter = new value_formatters_1.RawValueFormatter();\n        this.totalNonIdleWeight = null;\n        this.totalWeight = totalWeight;\n    }\n    getAppendOrderCalltreeRoot() {\n        return this.appendOrderCalltreeRoot;\n    }\n    getGroupedCalltreeRoot() {\n        return this.groupedCalltreeRoot;\n    }\n    formatValue(v) {\n        return this.valueFormatter.format(v);\n    }\n    setValueFormatter(f) {\n        this.valueFormatter = f;\n    }\n    getWeightUnit() {\n        return this.valueFormatter.unit;\n    }\n    getName() {\n        return this.name;\n    }\n    setName(name) {\n        this.name = name;\n    }\n    getTotalWeight() {\n        return this.totalWeight;\n    }\n    getTotalNonIdleWeight() {\n        if (this.totalNonIdleWeight === null) {\n            this.totalNonIdleWeight = this.groupedCalltreeRoot.children.reduce((n, c) => n + c.getTotalWeight(), 0);\n        }\n        return this.totalNonIdleWeight;\n    }\n    // This is private because it should only be called in the ProfileBuilder\n    // classes. Once a Profile instance has been constructed, it should be treated\n    // as immutable.\n    sortGroupedCallTree() {\n        function visit(node) {\n            node.children.sort((a, b) => -(a.getTotalWeight() - b.getTotalWeight()));\n            node.children.forEach(visit);\n        }\n        visit(this.groupedCalltreeRoot);\n    }\n    forEachCallGrouped(openFrame, closeFrame) {\n        function visit(node, start) {\n            if (node.frame !== Frame.root) {\n                openFrame(node, start);\n            }\n            let childTime = 0;\n            node.children.forEach(function (child) {\n                visit(child, start + childTime);\n                childTime += child.getTotalWeight();\n            });\n            if (node.frame !== Frame.root) {\n                closeFrame(node, start + node.getTotalWeight());\n            }\n        }\n        visit(this.groupedCalltreeRoot, 0);\n    }\n    forEachCall(openFrame, closeFrame) {\n        let prevStack = [];\n        let value = 0;\n        let sampleIndex = 0;\n        for (let stackTop of this.samples) {\n            // Find lowest common ancestor of the current stack and the previous one\n            let lca = null;\n            // This is O(n^2), but n should be relatively small here (stack height),\n            // so hopefully this isn't much of a problem\n            for (lca = stackTop; lca && lca.frame != Frame.root && prevStack.indexOf(lca) === -1; lca = lca.parent) { }\n            // Close frames that are no longer open\n            while (prevStack.length > 0 && utils_1.lastOf(prevStack) != lca) {\n                const node = prevStack.pop();\n                closeFrame(node, value);\n            }\n            // Open frames that are now becoming open\n            const toOpen = [];\n            for (let node = stackTop; node && node.frame != Frame.root && node != lca; node = node.parent) {\n                toOpen.push(node);\n            }\n            toOpen.reverse();\n            for (let node of toOpen) {\n                openFrame(node, value);\n            }\n            prevStack = prevStack.concat(toOpen);\n            value += this.weights[sampleIndex++];\n        }\n        // Close frames that are open at the end of the trace\n        for (let i = prevStack.length - 1; i >= 0; i--) {\n            closeFrame(prevStack[i], value);\n        }\n    }\n    forEachFrame(fn) {\n        this.frames.forEach(fn);\n    }\n    getProfileWithRecursionFlattened() {\n        const builder = new CallTreeProfileBuilder();\n        const stack = [];\n        const framesInStack = new Set();\n        function openFrame(node, value) {\n            if (framesInStack.has(node.frame)) {\n                stack.push(null);\n            }\n            else {\n                framesInStack.add(node.frame);\n                stack.push(node);\n                builder.enterFrame(node.frame, value);\n            }\n        }\n        function closeFrame(node, value) {\n            const stackTop = stack.pop();\n            if (stackTop) {\n                framesInStack.delete(stackTop.frame);\n                builder.leaveFrame(stackTop.frame, value);\n            }\n        }\n        this.forEachCall(openFrame, closeFrame);\n        const flattenedProfile = builder.build();\n        flattenedProfile.name = this.name;\n        flattenedProfile.valueFormatter = this.valueFormatter;\n        // When constructing a profile with recursion flattened,\n        // counter-intuitive things can happen to \"self time\" measurements\n        // for functions.\n        // For example, given the following list of stacks w/ weights:\n        //\n        // a 1\n        // a;b;a 1\n        // a;b;a;b;a 1\n        // a;b;a 1\n        //\n        // The resulting profile with recursion flattened out will look like this:\n        //\n        // a 1\n        // a;b 3\n        //\n        // Which is useful to view, but it's counter-intuitive to move self-time\n        // for frames around, since analyzing the self-time of functions is an important\n        // thing to be able to do accurately, and we don't want this to change when recursion\n        // is flattened. To work around that, we'll just copy the weights directly from the\n        // un-flattened profile.\n        this.forEachFrame(f => {\n            flattenedProfile.frames.getOrInsert(f).overwriteWeightWith(f);\n        });\n        return flattenedProfile;\n    }\n    getInvertedProfileForCallersOf(focalFrameInfo) {\n        const focalFrame = Frame.getOrInsert(this.frames, focalFrameInfo);\n        const builder = new StackListProfileBuilder();\n        // TODO(jlfwong): Could construct this at profile\n        // construction time rather than on demand.\n        const nodes = [];\n        function visit(node) {\n            if (node.frame === focalFrame) {\n                nodes.push(node);\n            }\n            else {\n                for (let child of node.children) {\n                    visit(child);\n                }\n            }\n        }\n        visit(this.appendOrderCalltreeRoot);\n        for (let node of nodes) {\n            const stack = [];\n            for (let n = node; n != null && n.frame !== Frame.root; n = n.parent) {\n                stack.push(n.frame);\n            }\n            builder.appendSampleWithWeight(stack, node.getTotalWeight());\n        }\n        const ret = builder.build();\n        ret.name = this.name;\n        ret.valueFormatter = this.valueFormatter;\n        return ret;\n    }\n    getProfileForCalleesOf(focalFrameInfo) {\n        const focalFrame = Frame.getOrInsert(this.frames, focalFrameInfo);\n        const builder = new StackListProfileBuilder();\n        function recordSubtree(focalFrameNode) {\n            const stack = [];\n            function visit(node) {\n                stack.push(node.frame);\n                builder.appendSampleWithWeight(stack, node.getSelfWeight());\n                for (let child of node.children) {\n                    visit(child);\n                }\n                stack.pop();\n            }\n            visit(focalFrameNode);\n        }\n        function findCalls(node) {\n            if (node.frame === focalFrame) {\n                recordSubtree(node);\n            }\n            else {\n                for (let child of node.children) {\n                    findCalls(child);\n                }\n            }\n        }\n        findCalls(this.appendOrderCalltreeRoot);\n        const ret = builder.build();\n        ret.name = this.name;\n        ret.valueFormatter = this.valueFormatter;\n        return ret;\n    }\n    // Demangle symbols for readability\n    demangle() {\n        return __awaiter(this, void 0, void 0, function* () {\n            let demangleCpp = null;\n            for (let frame of this.frames) {\n                // This function converts a mangled C++ name such as \"__ZNK7Support6ColorFeqERKS0_\"\n                // into a human-readable symbol (in this case \"Support::ColorF::==(Support::ColorF&)\")\n                if (frame.name.startsWith('__Z')) {\n                    if (!demangleCpp) {\n                        demangleCpp = (yield demangleCppModule).demangleCpp;\n                    }\n                    frame.name = demangleCpp(frame.name);\n                }\n            }\n        });\n    }\n    remapNames(callback) {\n        for (let frame of this.frames) {\n            frame.name = callback(frame.name);\n        }\n    }\n}\nexports.Profile = Profile;\nclass StackListProfileBuilder extends Profile {\n    constructor() {\n        super(...arguments);\n        this.pendingSample = null;\n    }\n    _appendSample(stack, weight, useAppendOrder) {\n        if (isNaN(weight))\n            throw new Error('invalid weight');\n        let node = useAppendOrder ? this.appendOrderCalltreeRoot : this.groupedCalltreeRoot;\n        let framesInStack = new Set();\n        for (let frameInfo of stack) {\n            const frame = Frame.getOrInsert(this.frames, frameInfo);\n            const last = useAppendOrder\n                ? utils_1.lastOf(node.children)\n                : node.children.find(c => c.frame === frame);\n            if (last && !last.isFrozen() && last.frame == frame) {\n                node = last;\n            }\n            else {\n                const parent = node;\n                node = new CallTreeNode(frame, node);\n                parent.children.push(node);\n            }\n            node.addToTotalWeight(weight);\n            // It's possible for the same frame to occur multiple\n            // times in the same call stack due to either direct\n            // or indirect recursion. We want to avoid counting that\n            // frame multiple times for a single sample, we so just\n            // track all of the unique frames that participated in\n            // this call stack, then add to their weight at the end.\n            framesInStack.add(node.frame);\n        }\n        node.addToSelfWeight(weight);\n        if (useAppendOrder) {\n            for (let child of node.children) {\n                child.freeze();\n            }\n        }\n        if (useAppendOrder) {\n            node.frame.addToSelfWeight(weight);\n            for (let frame of framesInStack) {\n                frame.addToTotalWeight(weight);\n            }\n            if (node === utils_1.lastOf(this.samples)) {\n                this.weights[this.weights.length - 1] += weight;\n            }\n            else {\n                this.samples.push(node);\n                this.weights.push(weight);\n            }\n        }\n    }\n    appendSampleWithWeight(stack, weight) {\n        if (weight === 0) {\n            // Samples with zero weight have no effect, so let's ignore them\n            return;\n        }\n        if (weight < 0) {\n            throw new Error('Samples must have positive weights');\n        }\n        this._appendSample(stack, weight, true);\n        this._appendSample(stack, weight, false);\n    }\n    appendSampleWithTimestamp(stack, timestamp) {\n        if (this.pendingSample) {\n            if (timestamp < this.pendingSample.centralTimestamp) {\n                throw new Error('Timestamps received out of order');\n            }\n            const endTimestamp = (timestamp + this.pendingSample.centralTimestamp) / 2;\n            this.appendSampleWithWeight(this.pendingSample.stack, endTimestamp - this.pendingSample.startTimestamp);\n            this.pendingSample = { stack, startTimestamp: endTimestamp, centralTimestamp: timestamp };\n        }\n        else {\n            this.pendingSample = { stack, startTimestamp: timestamp, centralTimestamp: timestamp };\n        }\n    }\n    build() {\n        if (this.pendingSample) {\n            if (this.samples.length > 0) {\n                this.appendSampleWithWeight(this.pendingSample.stack, this.pendingSample.centralTimestamp - this.pendingSample.startTimestamp);\n            }\n            else {\n                // There is only a single sample. In this case, units will be meaningless,\n                // so we'll append with a weight of 1 and also clear any value formatter\n                this.appendSampleWithWeight(this.pendingSample.stack, 1);\n                this.setValueFormatter(new value_formatters_1.RawValueFormatter());\n            }\n        }\n        this.totalWeight = Math.max(this.totalWeight, this.weights.reduce((a, b) => a + b, 0));\n        this.sortGroupedCallTree();\n        return this;\n    }\n}\nexports.StackListProfileBuilder = StackListProfileBuilder;\n// As an alternative API for importing profiles more efficiently, provide a\n// way to open & close frames directly without needing to construct tons of\n// arrays as intermediaries.\nclass CallTreeProfileBuilder extends Profile {\n    constructor() {\n        super(...arguments);\n        this.appendOrderStack = [this.appendOrderCalltreeRoot];\n        this.groupedOrderStack = [this.groupedCalltreeRoot];\n        this.framesInStack = new Map();\n        this.stack = [];\n        this.lastValue = 0;\n    }\n    addWeightsToFrames(value) {\n        const delta = value - this.lastValue;\n        for (let frame of this.framesInStack.keys()) {\n            frame.addToTotalWeight(delta);\n        }\n        const stackTop = utils_1.lastOf(this.stack);\n        if (stackTop) {\n            stackTop.addToSelfWeight(delta);\n        }\n    }\n    addWeightsToNodes(value, stack) {\n        const delta = value - this.lastValue;\n        for (let node of stack) {\n            node.addToTotalWeight(delta);\n        }\n        const stackTop = utils_1.lastOf(stack);\n        if (stackTop) {\n            stackTop.addToSelfWeight(delta);\n        }\n    }\n    _enterFrame(frame, value, useAppendOrder) {\n        let stack = useAppendOrder ? this.appendOrderStack : this.groupedOrderStack;\n        this.addWeightsToNodes(value, stack);\n        let prevTop = utils_1.lastOf(stack);\n        if (prevTop) {\n            if (useAppendOrder) {\n                const delta = value - this.lastValue;\n                if (delta > 0) {\n                    this.samples.push(prevTop);\n                    this.weights.push(value - this.lastValue);\n                }\n                else if (delta < 0) {\n                    throw new Error(`Samples must be provided in increasing order of cumulative value. Last sample was ${this.lastValue}, this sample was ${value}`);\n                }\n            }\n            const last = useAppendOrder\n                ? utils_1.lastOf(prevTop.children)\n                : prevTop.children.find(c => c.frame === frame);\n            let node;\n            if (last && !last.isFrozen() && last.frame == frame) {\n                node = last;\n            }\n            else {\n                node = new CallTreeNode(frame, prevTop);\n                prevTop.children.push(node);\n            }\n            stack.push(node);\n        }\n    }\n    enterFrame(frameInfo, value) {\n        const frame = Frame.getOrInsert(this.frames, frameInfo);\n        this.addWeightsToFrames(value);\n        this._enterFrame(frame, value, true);\n        this._enterFrame(frame, value, false);\n        this.stack.push(frame);\n        const frameCount = this.framesInStack.get(frame) || 0;\n        this.framesInStack.set(frame, frameCount + 1);\n        this.lastValue = value;\n    }\n    _leaveFrame(frame, value, useAppendOrder) {\n        let stack = useAppendOrder ? this.appendOrderStack : this.groupedOrderStack;\n        this.addWeightsToNodes(value, stack);\n        if (useAppendOrder) {\n            const leavingStackTop = this.appendOrderStack.pop();\n            if (leavingStackTop == null) {\n                throw new Error(`Trying to leave ${frame.key} when stack is empty`);\n            }\n            if (this.lastValue == null) {\n                throw new Error(`Trying to leave a ${frame.key} before any have been entered`);\n            }\n            leavingStackTop.freeze();\n            if (leavingStackTop.frame.key !== frame.key) {\n                throw new Error(`Tried to leave frame \"${frame.name}\" while frame \"${leavingStackTop.frame.name}\" was at the top at ${value}`);\n            }\n            const delta = value - this.lastValue;\n            if (delta > 0) {\n                this.samples.push(leavingStackTop);\n                this.weights.push(value - this.lastValue);\n            }\n            else if (delta < 0) {\n                throw new Error(`Samples must be provided in increasing order of cumulative value. Last sample was ${this\n                    .lastValue}, this sample was ${value}`);\n            }\n        }\n        else {\n            this.groupedOrderStack.pop();\n        }\n    }\n    leaveFrame(frameInfo, value) {\n        const frame = Frame.getOrInsert(this.frames, frameInfo);\n        this.addWeightsToFrames(value);\n        this._leaveFrame(frame, value, true);\n        this._leaveFrame(frame, value, false);\n        this.stack.pop();\n        const frameCount = this.framesInStack.get(frame);\n        if (frameCount == null)\n            return;\n        if (frameCount === 1) {\n            this.framesInStack.delete(frame);\n        }\n        else {\n            this.framesInStack.set(frame, frameCount - 1);\n        }\n        this.lastValue = value;\n        this.totalWeight = Math.max(this.totalWeight, this.lastValue);\n    }\n    build() {\n        // Each stack is expected to contain a single node which we initialize to be\n        // the root node.\n        if (this.appendOrderStack.length > 1 || this.groupedOrderStack.length > 1) {\n            throw new Error('Tried to complete profile construction with a non-empty stack');\n        }\n        this.sortGroupedCallTree();\n        return this;\n    }\n}\nexports.CallTreeProfileBuilder = CallTreeProfileBuilder;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decodeBase64 = exports.lazyStatic = exports.memoizeByReference = exports.memoizeByShallowEquality = exports.objectsHaveShallowEquality = exports.noop = exports.binarySearch = exports.triangle = exports.fract = exports.formatPercent = exports.zeroPad = exports.itReduce = exports.itForEach = exports.itMap = exports.KeyedSet = exports.getOrThrow = exports.getOrElse = exports.getOrInsert = exports.sortBy = exports.lastOf = void 0;\nfunction lastOf(ts) {\n    return ts[ts.length - 1] || null;\n}\nexports.lastOf = lastOf;\nfunction sortBy(ts, key) {\n    function comparator(a, b) {\n        const keyA = key(a);\n        const keyB = key(b);\n        return keyA < keyB ? -1 : keyA > keyB ? 1 : 0;\n    }\n    ts.sort(comparator);\n}\nexports.sortBy = sortBy;\nfunction getOrInsert(map, k, fallback) {\n    if (!map.has(k))\n        map.set(k, fallback(k));\n    return map.get(k);\n}\nexports.getOrInsert = getOrInsert;\nfunction getOrElse(map, k, fallback) {\n    if (!map.has(k))\n        return fallback(k);\n    return map.get(k);\n}\nexports.getOrElse = getOrElse;\nfunction getOrThrow(map, k) {\n    if (!map.has(k)) {\n        throw new Error(`Expected key ${k}`);\n    }\n    return map.get(k);\n}\nexports.getOrThrow = getOrThrow;\nclass KeyedSet {\n    constructor() {\n        this.map = new Map();\n    }\n    getOrInsert(t) {\n        const key = t.key;\n        const existing = this.map.get(key);\n        if (existing)\n            return existing;\n        this.map.set(key, t);\n        return t;\n    }\n    forEach(fn) {\n        this.map.forEach(fn);\n    }\n    [Symbol.iterator]() {\n        return this.map.values();\n    }\n}\nexports.KeyedSet = KeyedSet;\nfunction* itMap(it, f) {\n    for (let t of it) {\n        yield f(t);\n    }\n}\nexports.itMap = itMap;\nfunction itForEach(it, f) {\n    for (let t of it) {\n        f(t);\n    }\n}\nexports.itForEach = itForEach;\nfunction itReduce(it, f, init) {\n    let accum = init;\n    for (let t of it) {\n        accum = f(accum, t);\n    }\n    return accum;\n}\nexports.itReduce = itReduce;\nfunction zeroPad(s, width) {\n    return new Array(Math.max(width - s.length, 0) + 1).join('0') + s;\n}\nexports.zeroPad = zeroPad;\nfunction formatPercent(percent) {\n    let formattedPercent = `${percent.toFixed(0)}%`;\n    if (percent === 100)\n        formattedPercent = '100%';\n    else if (percent > 99)\n        formattedPercent = '>99%';\n    else if (percent < 0.01)\n        formattedPercent = '<0.01%';\n    else if (percent < 1)\n        formattedPercent = `${percent.toFixed(2)}%`;\n    else if (percent < 10)\n        formattedPercent = `${percent.toFixed(1)}%`;\n    return formattedPercent;\n}\nexports.formatPercent = formatPercent;\nfunction fract(x) {\n    return x - Math.floor(x);\n}\nexports.fract = fract;\nfunction triangle(x) {\n    return 2.0 * Math.abs(fract(x) - 0.5) - 1.0;\n}\nexports.triangle = triangle;\nfunction binarySearch(lo, hi, f, target, targetRangeSize = 1) {\n    console.assert(!isNaN(targetRangeSize) && !isNaN(target));\n    while (true) {\n        if (hi - lo <= targetRangeSize)\n            return [lo, hi];\n        const mid = (hi + lo) / 2;\n        const val = f(mid);\n        if (val < target)\n            lo = mid;\n        else\n            hi = mid;\n    }\n}\nexports.binarySearch = binarySearch;\nfunction noop(...args) { }\nexports.noop = noop;\nfunction objectsHaveShallowEquality(a, b) {\n    for (let key in a) {\n        if (a[key] !== b[key])\n            return false;\n    }\n    for (let key in b) {\n        if (a[key] !== b[key])\n            return false;\n    }\n    return true;\n}\nexports.objectsHaveShallowEquality = objectsHaveShallowEquality;\nfunction memoizeByShallowEquality(cb) {\n    let last = null;\n    return (args) => {\n        let result;\n        if (last == null) {\n            result = cb(args);\n            last = { args, result };\n            return result;\n        }\n        else if (objectsHaveShallowEquality(last.args, args)) {\n            return last.result;\n        }\n        else {\n            last.args = args;\n            last.result = cb(args);\n            return last.result;\n        }\n    };\n}\nexports.memoizeByShallowEquality = memoizeByShallowEquality;\nfunction memoizeByReference(cb) {\n    let last = null;\n    return (args) => {\n        let result;\n        if (last == null) {\n            result = cb(args);\n            last = { args, result };\n            return result;\n        }\n        else if (last.args === args) {\n            return last.result;\n        }\n        else {\n            last.args = args;\n            last.result = cb(args);\n            return last.result;\n        }\n    };\n}\nexports.memoizeByReference = memoizeByReference;\nfunction lazyStatic(cb) {\n    let last = null;\n    return () => {\n        if (last == null) {\n            last = { result: cb() };\n        }\n        return last.result;\n    };\n}\nexports.lazyStatic = lazyStatic;\nconst base64lookupTable = lazyStatic(() => {\n    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    const ret = new Map();\n    for (let i = 0; i < alphabet.length; i++) {\n        ret.set(alphabet.charAt(i), i);\n    }\n    ret.set('=', -1);\n    return ret;\n});\n// NOTE: There are probably simpler solutions to this problem, but I have this written already, so\n// until we run into problems with this, let's just use this.\n//\n// See: https://developer.mozilla.org/en-US/docs/Web/API/WindowBase64/Base64_encoding_and_decoding#The_Unicode_Problem#The_Unicode_Problem\nfunction decodeBase64(encoded) {\n    // Reference: https://www.rfc-editor.org/rfc/rfc4648.txt\n    const lookupTable = base64lookupTable();\n    // 3 byte groups are represented as sequneces of 4 characters.\n    //\n    // \"The encoding process represents 24-bit groups of input bits as output\n    //  strings of 4 encoded characters.\"\n    //\n    // \"Special processing is performed if fewer than 24 bits are available\n    //  at the end of the data being encoded.  A full encoding quantum is\n    //  always completed at the end of a quantity.  When fewer than 24 input\n    //  bits are available in an input group bits with value zero are added\n    //  (on the right) to form an integral number of 6-bit groups.\"\n    if (encoded.length % 4 !== 0) {\n        throw new Error(`Invalid length for base64 encoded string. Expected length % 4 = 0, got length = ${encoded.length}`);\n    }\n    const quartetCount = encoded.length / 4;\n    let byteCount;\n    // Special processing is performed if fewer than 24 bits are available\n    // at the end of the data being encoded.  A full encoding quantum is\n    // always completed at the end of a quantity.  When fewer than 24 input\n    // bits are available in an input group, bits with value zero are added\n    // (on the right) to form an integral number of 6-bit groups.  Padding\n    // at the end of the data is performed using the '=' character.  Since\n    // all base 64 input is an integral number of octets, only the following\n    // cases can arise:\n    //\n    // (1) The final quantum of encoding input is an integral multiple of 24\n    //     bits; here, the final unit of encoded output will be an integral\n    //     multiple of 4 characters with no \"=\" padding.\n    //\n    // (2) The final quantum of encoding input is exactly 8 bits; here, the\n    //     final unit of encoded output will be two characters followed by\n    //     two \"=\" padding characters.\n    //\n    // (3) The final quantum of encoding input is exactly 16 bits; here, the\n    //     final unit of encoded output will be three characters followed by\n    //     one \"=\" padding character.\n    if (encoded.length >= 4) {\n        if (encoded.charAt(encoded.length - 1) === '=') {\n            if (encoded.charAt(encoded.length - 2) === '=') {\n                // Case (2)\n                byteCount = quartetCount * 3 - 2;\n            }\n            else {\n                // Case (3)\n                byteCount = quartetCount * 3 - 1;\n            }\n        }\n        else {\n            // Case (1)\n            byteCount = quartetCount * 3;\n        }\n    }\n    else {\n        // Case (1)\n        byteCount = quartetCount * 3;\n    }\n    const bytes = new Uint8Array(byteCount);\n    let offset = 0;\n    for (let i = 0; i < quartetCount; i++) {\n        const enc1 = encoded.charAt(i * 4 + 0);\n        const enc2 = encoded.charAt(i * 4 + 1);\n        const enc3 = encoded.charAt(i * 4 + 2);\n        const enc4 = encoded.charAt(i * 4 + 3);\n        const sextet1 = lookupTable.get(enc1);\n        const sextet2 = lookupTable.get(enc2);\n        const sextet3 = lookupTable.get(enc3);\n        const sextet4 = lookupTable.get(enc4);\n        if (sextet1 == null || sextet2 == null || sextet3 == null || sextet4 == null) {\n            throw new Error(`Invalid quartet at indices ${i * 4} .. ${i * 4 + 3}: ${encoded.substring(i * 4, i * 4 + 3)}`);\n        }\n        bytes[offset++] = (sextet1 << 2) | (sextet2 >> 4);\n        if (enc3 !== '=') {\n            bytes[offset++] = ((sextet2 & 15) << 4) | (sextet3 >> 2);\n        }\n        if (enc4 !== '=') {\n            bytes[offset++] = ((sextet3 & 7) << 6) | sextet4;\n        }\n    }\n    if (offset !== byteCount) {\n        throw new Error(`Expected to decode ${byteCount} bytes, but only decoded ${offset})`);\n    }\n    return bytes;\n}\nexports.decodeBase64 = decodeBase64;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ByteFormatter = exports.TimeFormatter = exports.RawValueFormatter = void 0;\nconst utils_1 = require(\"./utils\");\nclass RawValueFormatter {\n    constructor() {\n        this.unit = 'none';\n    }\n    format(v) {\n        return v.toLocaleString();\n    }\n}\nexports.RawValueFormatter = RawValueFormatter;\nclass TimeFormatter {\n    constructor(unit) {\n        this.unit = unit;\n        if (unit === 'nanoseconds')\n            this.multiplier = 1e-9;\n        else if (unit === 'microseconds')\n            this.multiplier = 1e-6;\n        else if (unit === 'milliseconds')\n            this.multiplier = 1e-3;\n        else\n            this.multiplier = 1;\n    }\n    formatUnsigned(v) {\n        const s = v * this.multiplier;\n        if (s / 60 >= 1) {\n            const minutes = Math.floor(s / 60);\n            const seconds = Math.floor(s - minutes * 60).toString();\n            return `${minutes}:${utils_1.zeroPad(seconds, 2)}`;\n        }\n        if (s / 1 >= 1)\n            return `${s.toFixed(2)}s`;\n        if (s / 1e-3 >= 1)\n            return `${(s / 1e-3).toFixed(2)}ms`;\n        if (s / 1e-6 >= 1)\n            return `${(s / 1e-6).toFixed(2)}µs`;\n        else\n            return `${(s / 1e-9).toFixed(2)}ns`;\n    }\n    format(v) {\n        return `${v < 0 ? '-' : ''}${this.formatUnsigned(Math.abs(v))}`;\n    }\n}\nexports.TimeFormatter = TimeFormatter;\nclass ByteFormatter {\n    constructor() {\n        this.unit = 'bytes';\n    }\n    format(v) {\n        if (v < 1024)\n            return `${v.toFixed(0)} B`;\n        v /= 1024;\n        if (v < 1024)\n            return `${v.toFixed(2)} KB`;\n        v /= 1024;\n        if (v < 1024)\n            return `${v.toFixed(2)} MB`;\n        v /= 1024;\n        return `${v.toFixed(2)} GB`;\n    }\n}\nexports.ByteFormatter = ByteFormatter;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) __createBinding(exports, m, p);\n}\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./import/chrome\"), exports);\n__exportStar(require(\"./lib/flamechart\"), exports);\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    generator._invoke = makeInvokeMethod(innerFn, self, context);\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  IteratorPrototype[iteratorSymbol] = function () {\n    return this;\n  };\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;\n  GeneratorFunctionPrototype.constructor = GeneratorFunction;\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    this._invoke = enqueue;\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  AsyncIterator.prototype[asyncIteratorSymbol] = function () {\n    return this;\n  };\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var method = delegate.iterator[context.method];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method always terminates the yield* loop.\n      context.delegate = null;\n\n      if (context.method === \"throw\") {\n        // Note: [\"return\"] must be used for ES3 parsing compatibility.\n        if (delegate.iterator[\"return\"]) {\n          // If the delegate iterator has a return method, give it a\n          // chance to clean up.\n          context.method = \"return\";\n          context.arg = undefined;\n          maybeInvokeDelegate(delegate, context);\n\n          if (context.method === \"throw\") {\n            // If maybeInvokeDelegate(context) changed context.method from\n            // \"return\" to \"throw\", let that override the TypeError below.\n            return ContinueSentinel;\n          }\n        }\n\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a 'throw' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  Gp[iteratorSymbol] = function() {\n    return this;\n  };\n\n  Gp.toString = function() {\n    return \"[object Generator]\";\n  };\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(object) {\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "export const CHANGE_LOG_URL =\n  'https://github.com/facebook/react/blob/main/packages/react-devtools/CHANGELOG.md';\n\nexport const UNSUPPORTED_VERSION_URL =\n  'https://reactjs.org/blog/2019/08/15/new-react-devtools.html#how-do-i-get-the-old-version-back';\n\nexport const REACT_DEVTOOLS_WORKPLACE_URL =\n  'https://fburl.com/react-devtools-workplace-group';\n\nimport type {\n  Theme,\n  DisplayDensity,\n} from './devtools/views/Settings/SettingsContext';\n\nexport const THEME_STYLES: {[style: Theme | DisplayDensity]: any, ...} = {\n  light: {\n    '--color-attribute-name': '#ef6632',\n    '--color-attribute-name-not-editable': '#23272f',\n    '--color-attribute-name-inverted': 'rgba(255, 255, 255, 0.7)',\n    '--color-attribute-value': '#1a1aa6',\n    '--color-attribute-value-inverted': '#ffffff',\n    '--color-attribute-editable-value': '#1a1aa6',\n    '--color-background': '#ffffff',\n    '--color-background-hover': 'rgba(0, 136, 250, 0.1)',\n    '--color-background-inactive': '#e5e5e5',\n    '--color-background-invalid': '#fff0f0',\n    '--color-background-selected': '#0088fa',\n    '--color-button-background': '#ffffff',\n    '--color-button-background-focus': '#ededed',\n    '--color-button-background-hover': 'rgba(0, 0, 0, 0.2)',\n    '--color-button': '#5f6673',\n    '--color-button-disabled': '#cfd1d5',\n    '--color-button-active': '#0088fa',\n    '--color-button-focus': '#23272f',\n    '--color-button-hover': '#23272f',\n    '--color-border': '#eeeeee',\n    '--color-commit-did-not-render-fill': '#cfd1d5',\n    '--color-commit-did-not-render-fill-text': '#000000',\n    '--color-commit-did-not-render-pattern': '#cfd1d5',\n    '--color-commit-did-not-render-pattern-text': '#333333',\n    '--color-commit-gradient-0': '#37afa9',\n    '--color-commit-gradient-1': '#63b19e',\n    '--color-commit-gradient-2': '#80b393',\n    '--color-commit-gradient-3': '#97b488',\n    '--color-commit-gradient-4': '#abb67d',\n    '--color-commit-gradient-5': '#beb771',\n    '--color-commit-gradient-6': '#cfb965',\n    '--color-commit-gradient-7': '#dfba57',\n    '--color-commit-gradient-8': '#efbb49',\n    '--color-commit-gradient-9': '#febc38',\n    '--color-commit-gradient-text': '#000000',\n    '--color-component-name': '#6a51b2',\n    '--color-component-name-inverted': '#ffffff',\n    '--color-component-badge-background': '#e6e6e6',\n    '--color-component-badge-background-inverted': 'rgba(255, 255, 255, 0.25)',\n    '--color-component-badge-count': '#777d88',\n    '--color-component-badge-count-inverted': 'rgba(255, 255, 255, 0.7)',\n    '--color-console-error-badge-text': '#ffffff',\n    '--color-console-error-background': '#fff0f0',\n    '--color-console-error-border': '#ffd6d6',\n    '--color-console-error-icon': '#eb3941',\n    '--color-console-error-text': '#fe2e31',\n    '--color-console-warning-badge-text': '#000000',\n    '--color-console-warning-background': '#fffbe5',\n    '--color-console-warning-border': '#fff5c1',\n    '--color-console-warning-icon': '#f4bd00',\n    '--color-console-warning-text': '#64460c',\n    '--color-context-background': 'rgba(0,0,0,.9)',\n    '--color-context-background-hover': 'rgba(255, 255, 255, 0.1)',\n    '--color-context-background-selected': '#178fb9',\n    '--color-context-border': '#3d424a',\n    '--color-context-text': '#ffffff',\n    '--color-context-text-selected': '#ffffff',\n    '--color-dim': '#777d88',\n    '--color-dimmer': '#cfd1d5',\n    '--color-dimmest': '#eff0f1',\n    '--color-error-background': 'hsl(0, 100%, 97%)',\n    '--color-error-border': 'hsl(0, 100%, 92%)',\n    '--color-error-text': '#ff0000',\n    '--color-expand-collapse-toggle': '#777d88',\n    '--color-forget-badge-background': '#2683e2',\n    '--color-forget-badge-background-inverted': '#1a6bbc',\n    '--color-forget-text': '#fff',\n    '--color-link': '#0000ff',\n    '--color-modal-background': 'rgba(255, 255, 255, 0.75)',\n    '--color-bridge-version-npm-background': '#eff0f1',\n    '--color-bridge-version-npm-text': '#000000',\n    '--color-bridge-version-number': '#0088fa',\n    '--color-primitive-hook-badge-background': '#e5e5e5',\n    '--color-primitive-hook-badge-text': '#5f6673',\n    '--color-record-active': '#fc3a4b',\n    '--color-record-hover': '#3578e5',\n    '--color-record-inactive': '#0088fa',\n    '--color-resize-bar': '#eeeeee',\n    '--color-resize-bar-active': '#dcdcdc',\n    '--color-resize-bar-border': '#d1d1d1',\n    '--color-resize-bar-dot': '#333333',\n    '--color-timeline-internal-module': '#d1d1d1',\n    '--color-timeline-internal-module-hover': '#c9c9c9',\n    '--color-timeline-internal-module-text': '#444',\n    '--color-timeline-native-event': '#ccc',\n    '--color-timeline-native-event-hover': '#aaa',\n    '--color-timeline-network-primary': '#fcf3dc',\n    '--color-timeline-network-primary-hover': '#f0e7d1',\n    '--color-timeline-network-secondary': '#efc457',\n    '--color-timeline-network-secondary-hover': '#e3ba52',\n    '--color-timeline-priority-background': '#f6f6f6',\n    '--color-timeline-priority-border': '#eeeeee',\n    '--color-timeline-user-timing': '#c9cacd',\n    '--color-timeline-user-timing-hover': '#93959a',\n    '--color-timeline-react-idle': '#d3e5f6',\n    '--color-timeline-react-idle-hover': '#c3d9ef',\n    '--color-timeline-react-render': '#9fc3f3',\n    '--color-timeline-react-render-hover': '#83afe9',\n    '--color-timeline-react-render-text': '#11365e',\n    '--color-timeline-react-commit': '#c88ff0',\n    '--color-timeline-react-commit-hover': '#b281d6',\n    '--color-timeline-react-commit-text': '#3e2c4a',\n    '--color-timeline-react-layout-effects': '#b281d6',\n    '--color-timeline-react-layout-effects-hover': '#9d71bd',\n    '--color-timeline-react-layout-effects-text': '#3e2c4a',\n    '--color-timeline-react-passive-effects': '#b281d6',\n    '--color-timeline-react-passive-effects-hover': '#9d71bd',\n    '--color-timeline-react-passive-effects-text': '#3e2c4a',\n    '--color-timeline-react-schedule': '#9fc3f3',\n    '--color-timeline-react-schedule-hover': '#2683E2',\n    '--color-timeline-react-suspense-rejected': '#f1cc14',\n    '--color-timeline-react-suspense-rejected-hover': '#ffdf37',\n    '--color-timeline-react-suspense-resolved': '#a6e59f',\n    '--color-timeline-react-suspense-resolved-hover': '#89d281',\n    '--color-timeline-react-suspense-unresolved': '#c9cacd',\n    '--color-timeline-react-suspense-unresolved-hover': '#93959a',\n    '--color-timeline-thrown-error': '#ee1638',\n    '--color-timeline-thrown-error-hover': '#da1030',\n    '--color-timeline-text-color': '#000000',\n    '--color-timeline-text-dim-color': '#ccc',\n    '--color-timeline-react-work-border': '#eeeeee',\n    '--color-search-match': 'yellow',\n    '--color-search-match-current': '#f7923b',\n    '--color-selected-tree-highlight-active': 'rgba(0, 136, 250, 0.1)',\n    '--color-selected-tree-highlight-inactive': 'rgba(0, 0, 0, 0.05)',\n    '--color-scroll-caret': 'rgba(150, 150, 150, 0.5)',\n    '--color-tab-selected-border': '#0088fa',\n    '--color-text': '#000000',\n    '--color-text-invalid': '#ff0000',\n    '--color-text-selected': '#ffffff',\n    '--color-toggle-background-invalid': '#fc3a4b',\n    '--color-toggle-background-on': '#0088fa',\n    '--color-toggle-background-off': '#cfd1d5',\n    '--color-toggle-text': '#ffffff',\n    '--color-warning-background': '#fb3655',\n    '--color-warning-background-hover': '#f82042',\n    '--color-warning-text-color': '#ffffff',\n    '--color-warning-text-color-inverted': '#fd4d69',\n\n    // The styles below should be kept in sync with 'root.css'\n    // They are repeated there because they're used by e.g. tooltips or context menus\n    // which get rendered outside of the DOM subtree (where normal theme/styles are written).\n    '--color-scroll-thumb': '#c2c2c2',\n    '--color-scroll-track': '#fafafa',\n    '--color-tooltip-background': 'rgba(0, 0, 0, 0.9)',\n    '--color-tooltip-text': '#ffffff',\n  },\n  dark: {\n    '--color-attribute-name': '#9d87d2',\n    '--color-attribute-name-not-editable': '#ededed',\n    '--color-attribute-name-inverted': '#282828',\n    '--color-attribute-value': '#cedae0',\n    '--color-attribute-value-inverted': '#ffffff',\n    '--color-attribute-editable-value': 'yellow',\n    '--color-background': '#282c34',\n    '--color-background-hover': 'rgba(255, 255, 255, 0.1)',\n    '--color-background-inactive': '#3d424a',\n    '--color-background-invalid': '#5c0000',\n    '--color-background-selected': '#178fb9',\n    '--color-button-background': '#282c34',\n    '--color-button-background-focus': '#3d424a',\n    '--color-button-background-hover': 'rgba(255, 255, 255, 0.2)',\n    '--color-button': '#afb3b9',\n    '--color-button-active': '#61dafb',\n    '--color-button-disabled': '#4f5766',\n    '--color-button-focus': '#a2e9fc',\n    '--color-button-hover': '#ededed',\n    '--color-border': '#3d424a',\n    '--color-commit-did-not-render-fill': '#777d88',\n    '--color-commit-did-not-render-fill-text': '#000000',\n    '--color-commit-did-not-render-pattern': '#666c77',\n    '--color-commit-did-not-render-pattern-text': '#ffffff',\n    '--color-commit-gradient-0': '#37afa9',\n    '--color-commit-gradient-1': '#63b19e',\n    '--color-commit-gradient-2': '#80b393',\n    '--color-commit-gradient-3': '#97b488',\n    '--color-commit-gradient-4': '#abb67d',\n    '--color-commit-gradient-5': '#beb771',\n    '--color-commit-gradient-6': '#cfb965',\n    '--color-commit-gradient-7': '#dfba57',\n    '--color-commit-gradient-8': '#efbb49',\n    '--color-commit-gradient-9': '#febc38',\n    '--color-commit-gradient-text': '#000000',\n    '--color-component-name': '#61dafb',\n    '--color-component-name-inverted': '#282828',\n    '--color-component-badge-background': '#5e6167',\n    '--color-component-badge-background-inverted': '#46494e',\n    '--color-component-badge-count': '#8f949d',\n    '--color-component-badge-count-inverted': 'rgba(255, 255, 255, 0.85)',\n    '--color-console-error-badge-text': '#000000',\n    '--color-console-error-background': '#290000',\n    '--color-console-error-border': '#5c0000',\n    '--color-console-error-icon': '#eb3941',\n    '--color-console-error-text': '#fc7f7f',\n    '--color-console-warning-badge-text': '#000000',\n    '--color-console-warning-background': '#332b00',\n    '--color-console-warning-border': '#665500',\n    '--color-console-warning-icon': '#f4bd00',\n    '--color-console-warning-text': '#f5f2ed',\n    '--color-context-background': 'rgba(255,255,255,.95)',\n    '--color-context-background-hover': 'rgba(0, 136, 250, 0.1)',\n    '--color-context-background-selected': '#0088fa',\n    '--color-context-border': '#eeeeee',\n    '--color-context-text': '#000000',\n    '--color-context-text-selected': '#ffffff',\n    '--color-dim': '#8f949d',\n    '--color-dimmer': '#777d88',\n    '--color-dimmest': '#4f5766',\n    '--color-error-background': '#200',\n    '--color-error-border': '#900',\n    '--color-error-text': '#f55',\n    '--color-expand-collapse-toggle': '#8f949d',\n    '--color-forget-badge-background': '#2683e2',\n    '--color-forget-badge-background-inverted': '#1a6bbc',\n    '--color-forget-text': '#fff',\n    '--color-link': '#61dafb',\n    '--color-modal-background': 'rgba(0, 0, 0, 0.75)',\n    '--color-bridge-version-npm-background': 'rgba(0, 0, 0, 0.25)',\n    '--color-bridge-version-npm-text': '#ffffff',\n    '--color-bridge-version-number': 'yellow',\n    '--color-primitive-hook-badge-background': 'rgba(0, 0, 0, 0.25)',\n    '--color-primitive-hook-badge-text': 'rgba(255, 255, 255, 0.7)',\n    '--color-record-active': '#fc3a4b',\n    '--color-record-hover': '#a2e9fc',\n    '--color-record-inactive': '#61dafb',\n    '--color-resize-bar': '#282c34',\n    '--color-resize-bar-active': '#31363f',\n    '--color-resize-bar-border': '#3d424a',\n    '--color-resize-bar-dot': '#cfd1d5',\n    '--color-timeline-internal-module': '#303542',\n    '--color-timeline-internal-module-hover': '#363b4a',\n    '--color-timeline-internal-module-text': '#7f8899',\n    '--color-timeline-native-event': '#b2b2b2',\n    '--color-timeline-native-event-hover': '#949494',\n    '--color-timeline-network-primary': '#fcf3dc',\n    '--color-timeline-network-primary-hover': '#e3dbc5',\n    '--color-timeline-network-secondary': '#efc457',\n    '--color-timeline-network-secondary-hover': '#d6af4d',\n    '--color-timeline-priority-background': '#1d2129',\n    '--color-timeline-priority-border': '#282c34',\n    '--color-timeline-user-timing': '#c9cacd',\n    '--color-timeline-user-timing-hover': '#93959a',\n    '--color-timeline-react-idle': '#3d485b',\n    '--color-timeline-react-idle-hover': '#465269',\n    '--color-timeline-react-render': '#2683E2',\n    '--color-timeline-react-render-hover': '#1a76d4',\n    '--color-timeline-react-render-text': '#11365e',\n    '--color-timeline-react-commit': '#731fad',\n    '--color-timeline-react-commit-hover': '#611b94',\n    '--color-timeline-react-commit-text': '#e5c1ff',\n    '--color-timeline-react-layout-effects': '#611b94',\n    '--color-timeline-react-layout-effects-hover': '#51167a',\n    '--color-timeline-react-layout-effects-text': '#e5c1ff',\n    '--color-timeline-react-passive-effects': '#611b94',\n    '--color-timeline-react-passive-effects-hover': '#51167a',\n    '--color-timeline-react-passive-effects-text': '#e5c1ff',\n    '--color-timeline-react-schedule': '#2683E2',\n    '--color-timeline-react-schedule-hover': '#1a76d4',\n    '--color-timeline-react-suspense-rejected': '#f1cc14',\n    '--color-timeline-react-suspense-rejected-hover': '#e4c00f',\n    '--color-timeline-react-suspense-resolved': '#a6e59f',\n    '--color-timeline-react-suspense-resolved-hover': '#89d281',\n    '--color-timeline-react-suspense-unresolved': '#c9cacd',\n    '--color-timeline-react-suspense-unresolved-hover': '#93959a',\n    '--color-timeline-thrown-error': '#fb3655',\n    '--color-timeline-thrown-error-hover': '#f82042',\n    '--color-timeline-text-color': '#282c34',\n    '--color-timeline-text-dim-color': '#555b66',\n    '--color-timeline-react-work-border': '#3d424a',\n    '--color-search-match': 'yellow',\n    '--color-search-match-current': '#f7923b',\n    '--color-selected-tree-highlight-active': 'rgba(23, 143, 185, 0.15)',\n    '--color-selected-tree-highlight-inactive': 'rgba(255, 255, 255, 0.05)',\n    '--color-scroll-caret': '#4f5766',\n    '--color-shadow': 'rgba(0, 0, 0, 0.5)',\n    '--color-tab-selected-border': '#178fb9',\n    '--color-text': '#ffffff',\n    '--color-text-invalid': '#ff8080',\n    '--color-text-selected': '#ffffff',\n    '--color-toggle-background-invalid': '#fc3a4b',\n    '--color-toggle-background-on': '#178fb9',\n    '--color-toggle-background-off': '#777d88',\n    '--color-toggle-text': '#ffffff',\n    '--color-warning-background': '#ee1638',\n    '--color-warning-background-hover': '#da1030',\n    '--color-warning-text-color': '#ffffff',\n    '--color-warning-text-color-inverted': '#ee1638',\n\n    // The styles below should be kept in sync with 'root.css'\n    // They are repeated there because they're used by e.g. tooltips or context menus\n    // which get rendered outside of the DOM subtree (where normal theme/styles are written).\n    '--color-scroll-thumb': '#afb3b9',\n    '--color-scroll-track': '#313640',\n    '--color-tooltip-background': 'rgba(255, 255, 255, 0.95)',\n    '--color-tooltip-text': '#000000',\n  },\n  compact: {\n    '--font-size-monospace-small': '9px',\n    '--font-size-monospace-normal': '11px',\n    '--font-size-monospace-large': '15px',\n    '--font-size-sans-small': '10px',\n    '--font-size-sans-normal': '12px',\n    '--font-size-sans-large': '14px',\n    '--line-height-data': '18px',\n  },\n  comfortable: {\n    '--font-size-monospace-small': '10px',\n    '--font-size-monospace-normal': '13px',\n    '--font-size-monospace-large': '17px',\n    '--font-size-sans-small': '12px',\n    '--font-size-sans-normal': '14px',\n    '--font-size-sans-large': '16px',\n    '--line-height-data': '22px',\n  },\n};\n\n// HACK\n//\n// Sometimes the inline target is rendered before root styles are applied,\n// which would result in e.g. NaN itemSize being passed to react-window list.\nconst COMFORTABLE_LINE_HEIGHT: number = parseInt(\n  THEME_STYLES.comfortable['--line-height-data'],\n  10,\n);\nconst COMPACT_LINE_HEIGHT: number = parseInt(\n  THEME_STYLES.compact['--line-height-data'],\n  10,\n);\n\nexport {COMFORTABLE_LINE_HEIGHT, COMPACT_LINE_HEIGHT};\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nexport {\n  COMFORTABLE_LINE_HEIGHT,\n  COMPACT_LINE_HEIGHT,\n} from 'react-devtools-shared/src/devtools/constants.js';\n\nexport const REACT_TOTAL_NUM_LANES = 31;\n\n// Increment this number any time a backwards breaking change is made to the profiler metadata.\nexport const SCHEDULING_PROFILER_VERSION = 1;\n\nexport const SNAPSHOT_MAX_HEIGHT = 60;\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\n/**\n * An error thrown when an invalid profile could not be processed.\n */\nexport default class InvalidProfileError extends Error {}\n", "function areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (newInputs[i] !== lastInputs[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport memoize from 'memoize-one';\n\nimport type {\n  BatchUID,\n  Milliseconds,\n  ReactMeasure,\n  TimelineData,\n} from '../types';\n\nfunction unmemoizedGetBatchRange(\n  batchUID: BatchUID,\n  data: TimelineData,\n  minStartTime?: number = 0,\n): [Milliseconds, Milliseconds] {\n  const measures = data.batchUIDToMeasuresMap.get(batchUID);\n  if (measures == null || measures.length === 0) {\n    throw Error(`Could not find measures with batch UID \"${batchUID}\"`);\n  }\n\n  const lastMeasure = ((measures[measures.length - 1]: any): ReactMeasure);\n  const stopTime = lastMeasure.timestamp + lastMeasure.duration;\n\n  if (stopTime < minStartTime) {\n    return [0, 0];\n  }\n\n  let startTime = minStartTime;\n  for (let index = 0; index < measures.length; index++) {\n    const measure = measures[index];\n    if (measure.timestamp >= minStartTime) {\n      startTime = measure.timestamp;\n      break;\n    }\n  }\n\n  return [startTime, stopTime];\n}\n\nexport const getBatchRange: (\n  batchUID: BatchUID,\n  data: TimelineData,\n  minStartTime?: number,\n) => [Milliseconds, Milliseconds] = memoize(unmemoizedGetBatchRange);\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport {\n  importFromChromeTimeline,\n  Flamechart as SpeedscopeFlamechart,\n} from '@elg/speedscope';\nimport type {TimelineEvent} from '@elg/speedscope';\nimport type {\n  ErrorStackFrame,\n  BatchUID,\n  Flamechart,\n  Milliseconds,\n  NativeEvent,\n  NetworkMeasure,\n  Phase,\n  ReactLane,\n  ReactComponentMeasure,\n  ReactComponentMeasureType,\n  ReactMeasure,\n  ReactMeasureType,\n  TimelineData,\n  SchedulingEvent,\n  SuspenseEvent,\n} from '../types';\nimport {\n  REACT_TOTAL_NUM_LANES,\n  SCHEDULING_PROFILER_VERSION,\n  SNAPSHOT_MAX_HEIGHT,\n} from '../constants';\nimport InvalidProfileError from './InvalidProfileError';\nimport {getBatchRange} from '../utils/getBatchRange';\nimport ErrorStackParser from 'error-stack-parser';\n\ntype MeasureStackElement = {\n  type: ReactMeasureType,\n  depth: number,\n  measure: ReactMeasure,\n  startTime: Milliseconds,\n  stopTime?: Milliseconds,\n};\n\ntype ProcessorState = {\n  asyncProcessingPromises: Promise<any>[],\n  batchUID: BatchUID,\n  currentReactComponentMeasure: ReactComponentMeasure | null,\n  internalModuleCurrentStackFrame: ErrorStackFrame | null,\n  internalModuleStackStringSet: Set<string>,\n  measureStack: MeasureStackElement[],\n  nativeEventStack: NativeEvent[],\n  nextRenderShouldGenerateNewBatchID: boolean,\n  potentialLongEvents: Array<[NativeEvent, BatchUID]>,\n  potentialLongNestedUpdate: SchedulingEvent | null,\n  potentialLongNestedUpdates: Array<[SchedulingEvent, BatchUID]>,\n  potentialSuspenseEventsOutsideOfTransition: Array<\n    [SuspenseEvent, ReactLane[]],\n  >,\n  requestIdToNetworkMeasureMap: Map<string, NetworkMeasure>,\n  uidCounter: BatchUID,\n  unresolvedSuspenseEvents: Map<string, SuspenseEvent>,\n};\n\nconst NATIVE_EVENT_DURATION_THRESHOLD = 20;\nconst NESTED_UPDATE_DURATION_THRESHOLD = 20;\n\nconst WARNING_STRINGS = {\n  LONG_EVENT_HANDLER:\n    'An event handler scheduled a big update with React. Consider using the Transition API to defer some of this work.',\n  NESTED_UPDATE:\n    'A big nested update was scheduled during layout. ' +\n    'Nested updates require React to re-render synchronously before the browser can paint. ' +\n    'Consider delaying this update by moving it to a passive effect (useEffect).',\n  SUSPEND_DURING_UPDATE:\n    'A component suspended during an update which caused a fallback to be shown. ' +\n    \"Consider using the Transition API to avoid hiding components after they've been mounted.\",\n};\n\n// Exported for tests\nexport function getLanesFromTransportDecimalBitmask(\n  laneBitmaskString: string,\n): ReactLane[] {\n  const laneBitmask = parseInt(laneBitmaskString, 10);\n\n  // As negative numbers are stored in two's complement format, our bitmask\n  // checks will be thrown off by them.\n  if (laneBitmask < 0) {\n    return [];\n  }\n\n  const lanes = [];\n  let powersOfTwo = 0;\n  while (powersOfTwo <= REACT_TOTAL_NUM_LANES) {\n    if ((1 << powersOfTwo) & laneBitmask) {\n      lanes.push(powersOfTwo);\n    }\n    powersOfTwo++;\n  }\n  return lanes;\n}\n\nfunction updateLaneToLabelMap(\n  profilerData: TimelineData,\n  laneLabelTuplesString: string,\n): void {\n  // These marks appear multiple times in the data;\n  // We only need to extact them once.\n  if (profilerData.laneToLabelMap.size === 0) {\n    const laneLabelTuples = laneLabelTuplesString.split(',');\n    for (let laneIndex = 0; laneIndex < laneLabelTuples.length; laneIndex++) {\n      // The numeric lane value (e.g. 64) isn't important.\n      // The profiler parses and stores the lane's position within the bitmap,\n      // (e.g. lane 1 is index 0, lane 16 is index 4).\n      profilerData.laneToLabelMap.set(laneIndex, laneLabelTuples[laneIndex]);\n    }\n  }\n}\n\nlet profilerVersion = null;\n\nfunction getLastType(stack: $PropertyType<ProcessorState, 'measureStack'>) {\n  if (stack.length > 0) {\n    const {type} = stack[stack.length - 1];\n    return type;\n  }\n  return null;\n}\n\nfunction getDepth(stack: $PropertyType<ProcessorState, 'measureStack'>) {\n  if (stack.length > 0) {\n    const {depth, type} = stack[stack.length - 1];\n    return type === 'render-idle' ? depth : depth + 1;\n  }\n  return 0;\n}\n\nfunction markWorkStarted(\n  type: ReactMeasureType,\n  startTime: Milliseconds,\n  lanes: ReactLane[],\n  currentProfilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const {batchUID, measureStack} = state;\n  const depth = getDepth(measureStack);\n\n  const measure: ReactMeasure = {\n    type,\n    batchUID,\n    depth,\n    lanes,\n    timestamp: startTime,\n    duration: 0,\n  };\n\n  state.measureStack.push({depth, measure, startTime, type});\n\n  // This array is pre-initialized when the batchUID is generated.\n  const measures = currentProfilerData.batchUIDToMeasuresMap.get(batchUID);\n  if (measures != null) {\n    measures.push(measure);\n  } else {\n    currentProfilerData.batchUIDToMeasuresMap.set(state.batchUID, [measure]);\n  }\n\n  // This array is pre-initialized before processing starts.\n  lanes.forEach(lane => {\n    ((currentProfilerData.laneToReactMeasureMap.get(\n      lane,\n    ): any): ReactMeasure[]).push(measure);\n  });\n}\n\nfunction markWorkCompleted(\n  type: ReactMeasureType,\n  stopTime: Milliseconds,\n  currentProfilerData: TimelineData,\n  stack: $PropertyType<ProcessorState, 'measureStack'>,\n) {\n  if (stack.length === 0) {\n    console.error(\n      'Unexpected type \"%s\" completed at %sms while stack is empty.',\n      type,\n      stopTime,\n    );\n    // Ignore work \"completion\" user timing mark that doesn't complete anything\n    return;\n  }\n\n  const last = stack[stack.length - 1];\n  if (last.type !== type) {\n    console.error(\n      'Unexpected type \"%s\" completed at %sms before \"%s\" completed.',\n      type,\n      stopTime,\n      last.type,\n    );\n  }\n\n  // $FlowFixMe[incompatible-use]\n  const {measure, startTime} = stack.pop();\n  if (!measure) {\n    console.error('Could not find matching measure for type \"%s\".', type);\n  }\n\n  // $FlowFixMe[cannot-write] This property should not be writable outside of this function.\n  measure.duration = stopTime - startTime;\n}\n\nfunction throwIfIncomplete(\n  type: ReactMeasureType,\n  stack: $PropertyType<ProcessorState, 'measureStack'>,\n) {\n  const lastIndex = stack.length - 1;\n  if (lastIndex >= 0) {\n    const last = stack[lastIndex];\n    if (last.stopTime === undefined && last.type === type) {\n      throw new InvalidProfileError(\n        `Unexpected type \"${type}\" started before \"${last.type}\" completed.`,\n      );\n    }\n  }\n}\n\nfunction processEventDispatch(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const data = event.args.data;\n  const type = data.type;\n\n  if (type.startsWith('react-')) {\n    const stackTrace = data.stackTrace;\n    if (stackTrace) {\n      const topFrame = stackTrace[stackTrace.length - 1];\n      if (topFrame.url.includes('/react-dom.')) {\n        // Filter out fake React events dispatched by invokeGuardedCallbackDev.\n        return;\n      }\n    }\n  }\n\n  // Reduce noise from events like DOMActivate, load/unload, etc. which are usually not relevant\n  if (\n    type === 'blur' ||\n    type === 'click' ||\n    type === 'input' ||\n    type.startsWith('focus') ||\n    type.startsWith('key') ||\n    type.startsWith('mouse') ||\n    type.startsWith('pointer')\n  ) {\n    const duration = event.dur / 1000;\n\n    let depth = 0;\n\n    while (state.nativeEventStack.length > 0) {\n      const prevNativeEvent =\n        state.nativeEventStack[state.nativeEventStack.length - 1];\n      const prevStopTime = prevNativeEvent.timestamp + prevNativeEvent.duration;\n\n      if (timestamp < prevStopTime) {\n        depth = prevNativeEvent.depth + 1;\n        break;\n      } else {\n        state.nativeEventStack.pop();\n      }\n    }\n\n    const nativeEvent = {\n      depth,\n      duration,\n      timestamp,\n      type,\n      warning: null,\n    };\n\n    profilerData.nativeEvents.push(nativeEvent);\n\n    // Keep track of curent event in case future ones overlap.\n    // We separate them into different vertical lanes in this case.\n    state.nativeEventStack.push(nativeEvent);\n  }\n}\n\nfunction processResourceFinish(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const requestId = event.args.data.requestId;\n  const networkMeasure = state.requestIdToNetworkMeasureMap.get(requestId);\n  if (networkMeasure != null) {\n    networkMeasure.finishTimestamp = timestamp;\n    if (networkMeasure.firstReceivedDataTimestamp === 0) {\n      networkMeasure.firstReceivedDataTimestamp = timestamp;\n    }\n    if (networkMeasure.lastReceivedDataTimestamp === 0) {\n      networkMeasure.lastReceivedDataTimestamp = timestamp;\n    }\n\n    // Clean up now that the resource is done.\n    state.requestIdToNetworkMeasureMap.delete(event.args.data.requestId);\n  }\n}\n\nfunction processResourceReceivedData(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const requestId = event.args.data.requestId;\n  const networkMeasure = state.requestIdToNetworkMeasureMap.get(requestId);\n  if (networkMeasure != null) {\n    if (networkMeasure.firstReceivedDataTimestamp === 0) {\n      networkMeasure.firstReceivedDataTimestamp = timestamp;\n    }\n    networkMeasure.lastReceivedDataTimestamp = timestamp;\n    networkMeasure.finishTimestamp = timestamp;\n  }\n}\n\nfunction processResourceReceiveResponse(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const requestId = event.args.data.requestId;\n  const networkMeasure = state.requestIdToNetworkMeasureMap.get(requestId);\n  if (networkMeasure != null) {\n    networkMeasure.receiveResponseTimestamp = timestamp;\n  }\n}\n\nfunction processScreenshot(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const encodedSnapshot = event.args.snapshot; // Base 64 encoded\n\n  const snapshot = {\n    height: 0,\n    image: null,\n    imageSource: `data:image/png;base64,${encodedSnapshot}`,\n    timestamp,\n    width: 0,\n  };\n\n  // Delay processing until we've extracted snapshot dimensions.\n  let resolveFn = ((null: any): Function);\n  state.asyncProcessingPromises.push(\n    new Promise(resolve => {\n      resolveFn = resolve;\n    }),\n  );\n\n  // Parse the Base64 image data to determine native size.\n  // This will be used later to scale for display within the thumbnail strip.\n  fetch(snapshot.imageSource)\n    .then(response => response.blob())\n    .then(blob => {\n      // $FlowFixMe[cannot-resolve-name] createImageBitmap\n      createImageBitmap(blob).then(bitmap => {\n        snapshot.height = bitmap.height;\n        snapshot.width = bitmap.width;\n\n        resolveFn();\n      });\n    });\n\n  profilerData.snapshots.push(snapshot);\n}\n\nfunction processResourceSendRequest(\n  event: TimelineEvent,\n  timestamp: Milliseconds,\n  profilerData: TimelineData,\n  state: ProcessorState,\n) {\n  const data = event.args.data;\n  const requestId = data.requestId;\n\n  const availableDepths = new Array<boolean>(\n    state.requestIdToNetworkMeasureMap.size + 1,\n  ).fill(true);\n  state.requestIdToNetworkMeasureMap.forEach(({depth}) => {\n    availableDepths[depth] = false;\n  });\n\n  let depth = 0;\n  for (let i = 0; i < availableDepths.length; i++) {\n    if (availableDepths[i]) {\n      depth = i;\n      break;\n    }\n  }\n\n  const networkMeasure: NetworkMeasure = {\n    depth,\n    finishTimestamp: 0,\n    firstReceivedDataTimestamp: 0,\n    lastReceivedDataTimestamp: 0,\n    requestId,\n    requestMethod: data.requestMethod,\n    priority: data.priority,\n    sendRequestTimestamp: timestamp,\n    receiveResponseTimestamp: 0,\n    url: data.url,\n  };\n\n  state.requestIdToNetworkMeasureMap.set(requestId, networkMeasure);\n\n  profilerData.networkMeasures.push(networkMeasure);\n  networkMeasure.sendRequestTimestamp = timestamp;\n}\n\nfunction processTimelineEvent(\n  event: TimelineEvent,\n  /** Finalized profiler data up to `event`. May be mutated. */\n  currentProfilerData: TimelineData,\n  /** Intermediate processor state. May be mutated. */\n  state: ProcessorState,\n) {\n  const {cat, name, ts, ph} = event;\n\n  const startTime = (ts - currentProfilerData.startTime) / 1000;\n\n  switch (cat) {\n    case 'disabled-by-default-devtools.screenshot':\n      processScreenshot(event, startTime, currentProfilerData, state);\n      break;\n    case 'devtools.timeline':\n      switch (name) {\n        case 'EventDispatch':\n          processEventDispatch(event, startTime, currentProfilerData, state);\n          break;\n        case 'ResourceFinish':\n          processResourceFinish(event, startTime, currentProfilerData, state);\n          break;\n        case 'ResourceReceivedData':\n          processResourceReceivedData(\n            event,\n            startTime,\n            currentProfilerData,\n            state,\n          );\n          break;\n        case 'ResourceReceiveResponse':\n          processResourceReceiveResponse(\n            event,\n            startTime,\n            currentProfilerData,\n            state,\n          );\n          break;\n        case 'ResourceSendRequest':\n          processResourceSendRequest(\n            event,\n            startTime,\n            currentProfilerData,\n            state,\n          );\n          break;\n      }\n      break;\n    case 'blink.user_timing':\n      if (name.startsWith('--react-version-')) {\n        const [reactVersion] = name.slice(16).split('-');\n        currentProfilerData.reactVersion = reactVersion;\n      } else if (name.startsWith('--profiler-version-')) {\n        const [versionString] = name.slice(19).split('-');\n        profilerVersion = parseInt(versionString, 10);\n        if (profilerVersion !== SCHEDULING_PROFILER_VERSION) {\n          throw new InvalidProfileError(\n            `This version of profiling data (${versionString}) is not supported by the current profiler.`,\n          );\n        }\n      } else if (name.startsWith('--react-lane-labels-')) {\n        const [laneLabelTuplesString] = name.slice(20).split('-');\n        updateLaneToLabelMap(currentProfilerData, laneLabelTuplesString);\n      } else if (name.startsWith('--component-')) {\n        processReactComponentMeasure(\n          name,\n          startTime,\n          currentProfilerData,\n          state,\n        );\n      } else if (name.startsWith('--schedule-render-')) {\n        const [laneBitmaskString] = name.slice(18).split('-');\n\n        currentProfilerData.schedulingEvents.push({\n          type: 'schedule-render',\n          lanes: getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          timestamp: startTime,\n          warning: null,\n        });\n      } else if (name.startsWith('--schedule-forced-update-')) {\n        const [laneBitmaskString, componentName] = name.slice(25).split('-');\n\n        const forceUpdateEvent = {\n          type: 'schedule-force-update',\n          lanes: getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          componentName,\n          timestamp: startTime,\n          warning: null,\n        };\n\n        // If this is a nested update, make a note of it.\n        // Once we're done processing events, we'll check to see if it was a long update and warn about it.\n        if (state.measureStack.find(({type}) => type === 'commit')) {\n          state.potentialLongNestedUpdate = forceUpdateEvent;\n        }\n\n        currentProfilerData.schedulingEvents.push(forceUpdateEvent);\n      } else if (name.startsWith('--schedule-state-update-')) {\n        const [laneBitmaskString, componentName] = name.slice(24).split('-');\n\n        const stateUpdateEvent = {\n          type: 'schedule-state-update',\n          lanes: getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          componentName,\n          timestamp: startTime,\n          warning: null,\n        };\n\n        // If this is a nested update, make a note of it.\n        // Once we're done processing events, we'll check to see if it was a long update and warn about it.\n        if (state.measureStack.find(({type}) => type === 'commit')) {\n          state.potentialLongNestedUpdate = stateUpdateEvent;\n        }\n\n        currentProfilerData.schedulingEvents.push(stateUpdateEvent);\n      } else if (name.startsWith('--error-')) {\n        const [componentName, phase, message] = name.slice(8).split('-');\n\n        currentProfilerData.thrownErrors.push({\n          componentName,\n          message,\n          phase: ((phase: any): Phase),\n          timestamp: startTime,\n          type: 'thrown-error',\n        });\n      } else if (name.startsWith('--suspense-suspend-')) {\n        const [id, componentName, phase, laneBitmaskString, promiseName] = name\n          .slice(19)\n          .split('-');\n        const lanes = getLanesFromTransportDecimalBitmask(laneBitmaskString);\n\n        const availableDepths = new Array<boolean>(\n          state.unresolvedSuspenseEvents.size + 1,\n        ).fill(true);\n        state.unresolvedSuspenseEvents.forEach(({depth}) => {\n          availableDepths[depth] = false;\n        });\n\n        let depth = 0;\n        for (let i = 0; i < availableDepths.length; i++) {\n          if (availableDepths[i]) {\n            depth = i;\n            break;\n          }\n        }\n\n        // TODO (timeline) Maybe we should calculate depth in post,\n        // so unresolved Suspense requests don't take up space.\n        // We can't know if they'll be resolved or not at this point.\n        // We'll just give them a default (fake) duration width.\n\n        const suspenseEvent = {\n          componentName,\n          depth,\n          duration: null,\n          id,\n          phase: ((phase: any): Phase),\n          promiseName: promiseName || null,\n          resolution: 'unresolved',\n          timestamp: startTime,\n          type: 'suspense',\n          warning: null,\n        };\n\n        if (phase === 'update') {\n          // If a component suspended during an update, we should verify that it was during a transition.\n          // We need the lane metadata to verify this though.\n          // Since that data is only logged during commit, we may not have it yet.\n          // Store these events for post-processing then.\n          state.potentialSuspenseEventsOutsideOfTransition.push([\n            suspenseEvent,\n            lanes,\n          ]);\n        }\n\n        currentProfilerData.suspenseEvents.push(suspenseEvent);\n        state.unresolvedSuspenseEvents.set(id, suspenseEvent);\n      } else if (name.startsWith('--suspense-resolved-')) {\n        const [id] = name.slice(20).split('-');\n        const suspenseEvent = state.unresolvedSuspenseEvents.get(id);\n        if (suspenseEvent != null) {\n          state.unresolvedSuspenseEvents.delete(id);\n\n          suspenseEvent.duration = startTime - suspenseEvent.timestamp;\n          suspenseEvent.resolution = 'resolved';\n        }\n      } else if (name.startsWith('--suspense-rejected-')) {\n        const [id] = name.slice(20).split('-');\n        const suspenseEvent = state.unresolvedSuspenseEvents.get(id);\n        if (suspenseEvent != null) {\n          state.unresolvedSuspenseEvents.delete(id);\n\n          suspenseEvent.duration = startTime - suspenseEvent.timestamp;\n          suspenseEvent.resolution = 'rejected';\n        }\n      } else if (name.startsWith('--render-start-')) {\n        if (state.nextRenderShouldGenerateNewBatchID) {\n          state.nextRenderShouldGenerateNewBatchID = false;\n          state.batchUID = ((state.uidCounter++: any): BatchUID);\n        }\n\n        // If this render is the result of a nested update, make a note of it.\n        // Once we're done processing events, we'll check to see if it was a long update and warn about it.\n        if (state.potentialLongNestedUpdate !== null) {\n          state.potentialLongNestedUpdates.push([\n            state.potentialLongNestedUpdate,\n            state.batchUID,\n          ]);\n          state.potentialLongNestedUpdate = null;\n        }\n\n        const [laneBitmaskString] = name.slice(15).split('-');\n\n        throwIfIncomplete('render', state.measureStack);\n        if (getLastType(state.measureStack) !== 'render-idle') {\n          markWorkStarted(\n            'render-idle',\n            startTime,\n            getLanesFromTransportDecimalBitmask(laneBitmaskString),\n            currentProfilerData,\n            state,\n          );\n        }\n        markWorkStarted(\n          'render',\n          startTime,\n          getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          currentProfilerData,\n          state,\n        );\n\n        for (let i = 0; i < state.nativeEventStack.length; i++) {\n          const nativeEvent = state.nativeEventStack[i];\n          const stopTime = nativeEvent.timestamp + nativeEvent.duration;\n\n          // If React work was scheduled during an event handler, and the event had a long duration,\n          // it might be because the React render was long and stretched the event.\n          // It might also be that the React work was short and that something else stretched the event.\n          // Make a note of this event for now and we'll examine the batch of React render work later.\n          // (We can't know until we're done processing the React update anyway.)\n          if (stopTime > startTime) {\n            state.potentialLongEvents.push([nativeEvent, state.batchUID]);\n          }\n        }\n      } else if (\n        name.startsWith('--render-stop') ||\n        name.startsWith('--render-yield')\n      ) {\n        markWorkCompleted(\n          'render',\n          startTime,\n          currentProfilerData,\n          state.measureStack,\n        );\n      } else if (name.startsWith('--commit-start-')) {\n        state.nextRenderShouldGenerateNewBatchID = true;\n        const [laneBitmaskString] = name.slice(15).split('-');\n\n        markWorkStarted(\n          'commit',\n          startTime,\n          getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          currentProfilerData,\n          state,\n        );\n      } else if (name.startsWith('--commit-stop')) {\n        markWorkCompleted(\n          'commit',\n          startTime,\n          currentProfilerData,\n          state.measureStack,\n        );\n        markWorkCompleted(\n          'render-idle',\n          startTime,\n          currentProfilerData,\n          state.measureStack,\n        );\n      } else if (name.startsWith('--layout-effects-start-')) {\n        const [laneBitmaskString] = name.slice(23).split('-');\n\n        markWorkStarted(\n          'layout-effects',\n          startTime,\n          getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          currentProfilerData,\n          state,\n        );\n      } else if (name.startsWith('--layout-effects-stop')) {\n        markWorkCompleted(\n          'layout-effects',\n          startTime,\n          currentProfilerData,\n          state.measureStack,\n        );\n      } else if (name.startsWith('--passive-effects-start-')) {\n        const [laneBitmaskString] = name.slice(24).split('-');\n\n        markWorkStarted(\n          'passive-effects',\n          startTime,\n          getLanesFromTransportDecimalBitmask(laneBitmaskString),\n          currentProfilerData,\n          state,\n        );\n      } else if (name.startsWith('--passive-effects-stop')) {\n        markWorkCompleted(\n          'passive-effects',\n          startTime,\n          currentProfilerData,\n          state.measureStack,\n        );\n      } else if (name.startsWith('--react-internal-module-start-')) {\n        const stackFrameStart = name.slice(30);\n\n        if (!state.internalModuleStackStringSet.has(stackFrameStart)) {\n          state.internalModuleStackStringSet.add(stackFrameStart);\n\n          const parsedStackFrameStart = parseStackFrame(stackFrameStart);\n\n          state.internalModuleCurrentStackFrame = parsedStackFrameStart;\n        }\n      } else if (name.startsWith('--react-internal-module-stop-')) {\n        const stackFrameStop = name.slice(29);\n\n        if (!state.internalModuleStackStringSet.has(stackFrameStop)) {\n          state.internalModuleStackStringSet.add(stackFrameStop);\n\n          const parsedStackFrameStop = parseStackFrame(stackFrameStop);\n\n          if (\n            parsedStackFrameStop !== null &&\n            state.internalModuleCurrentStackFrame !== null\n          ) {\n            const parsedStackFrameStart = state.internalModuleCurrentStackFrame;\n\n            state.internalModuleCurrentStackFrame = null;\n\n            const range = [parsedStackFrameStart, parsedStackFrameStop];\n            const ranges = currentProfilerData.internalModuleSourceToRanges.get(\n              parsedStackFrameStart.fileName,\n            );\n            if (ranges == null) {\n              currentProfilerData.internalModuleSourceToRanges.set(\n                parsedStackFrameStart.fileName,\n                [range],\n              );\n            } else {\n              ranges.push(range);\n            }\n          }\n        }\n      } else if (ph === 'R' || ph === 'n') {\n        // User Timing mark\n        currentProfilerData.otherUserTimingMarks.push({\n          name,\n          timestamp: startTime,\n        });\n      } else if (ph === 'b') {\n        // TODO: Begin user timing measure\n      } else if (ph === 'e') {\n        // TODO: End user timing measure\n      } else if (ph === 'i' || ph === 'I') {\n        // Instant events.\n        // Note that the capital \"I\" is a deprecated value that exists in Chrome Canary traces.\n      } else {\n        throw new InvalidProfileError(\n          `Unrecognized event ${JSON.stringify(\n            event,\n          )}! This is likely a bug in this profiler tool.`,\n        );\n      }\n      break;\n  }\n}\n\nfunction assertNoOverlappingComponentMeasure(state: ProcessorState) {\n  if (state.currentReactComponentMeasure !== null) {\n    console.error(\n      'Component measure started while another measure in progress:',\n      state.currentReactComponentMeasure,\n    );\n  }\n}\n\nfunction assertCurrentComponentMeasureType(\n  state: ProcessorState,\n  type: ReactComponentMeasureType,\n): void {\n  if (state.currentReactComponentMeasure === null) {\n    console.error(\n      `Component measure type \"${type}\" stopped while no measure was in progress`,\n    );\n  } else if (state.currentReactComponentMeasure.type !== type) {\n    console.error(\n      `Component measure type \"${type}\" stopped while type ${state.currentReactComponentMeasure.type} in progress`,\n    );\n  }\n}\n\nfunction processReactComponentMeasure(\n  name: string,\n  startTime: Milliseconds,\n  currentProfilerData: TimelineData,\n  state: ProcessorState,\n): void {\n  if (name.startsWith('--component-render-start-')) {\n    const [componentName] = name.slice(25).split('-');\n\n    assertNoOverlappingComponentMeasure(state);\n\n    state.currentReactComponentMeasure = {\n      componentName,\n      timestamp: startTime,\n      duration: 0,\n      type: 'render',\n      warning: null,\n    };\n  } else if (name === '--component-render-stop') {\n    assertCurrentComponentMeasureType(state, 'render');\n\n    if (state.currentReactComponentMeasure !== null) {\n      const componentMeasure = state.currentReactComponentMeasure;\n      componentMeasure.duration = startTime - componentMeasure.timestamp;\n\n      state.currentReactComponentMeasure = null;\n\n      currentProfilerData.componentMeasures.push(componentMeasure);\n    }\n  } else if (name.startsWith('--component-layout-effect-mount-start-')) {\n    const [componentName] = name.slice(38).split('-');\n\n    assertNoOverlappingComponentMeasure(state);\n\n    state.currentReactComponentMeasure = {\n      componentName,\n      timestamp: startTime,\n      duration: 0,\n      type: 'layout-effect-mount',\n      warning: null,\n    };\n  } else if (name === '--component-layout-effect-mount-stop') {\n    assertCurrentComponentMeasureType(state, 'layout-effect-mount');\n\n    if (state.currentReactComponentMeasure !== null) {\n      const componentMeasure = state.currentReactComponentMeasure;\n      componentMeasure.duration = startTime - componentMeasure.timestamp;\n\n      state.currentReactComponentMeasure = null;\n\n      currentProfilerData.componentMeasures.push(componentMeasure);\n    }\n  } else if (name.startsWith('--component-layout-effect-unmount-start-')) {\n    const [componentName] = name.slice(40).split('-');\n\n    assertNoOverlappingComponentMeasure(state);\n\n    state.currentReactComponentMeasure = {\n      componentName,\n      timestamp: startTime,\n      duration: 0,\n      type: 'layout-effect-unmount',\n      warning: null,\n    };\n  } else if (name === '--component-layout-effect-unmount-stop') {\n    assertCurrentComponentMeasureType(state, 'layout-effect-unmount');\n\n    if (state.currentReactComponentMeasure !== null) {\n      const componentMeasure = state.currentReactComponentMeasure;\n      componentMeasure.duration = startTime - componentMeasure.timestamp;\n\n      state.currentReactComponentMeasure = null;\n\n      currentProfilerData.componentMeasures.push(componentMeasure);\n    }\n  } else if (name.startsWith('--component-passive-effect-mount-start-')) {\n    const [componentName] = name.slice(39).split('-');\n\n    assertNoOverlappingComponentMeasure(state);\n\n    state.currentReactComponentMeasure = {\n      componentName,\n      timestamp: startTime,\n      duration: 0,\n      type: 'passive-effect-mount',\n      warning: null,\n    };\n  } else if (name === '--component-passive-effect-mount-stop') {\n    assertCurrentComponentMeasureType(state, 'passive-effect-mount');\n\n    if (state.currentReactComponentMeasure !== null) {\n      const componentMeasure = state.currentReactComponentMeasure;\n      componentMeasure.duration = startTime - componentMeasure.timestamp;\n\n      state.currentReactComponentMeasure = null;\n\n      currentProfilerData.componentMeasures.push(componentMeasure);\n    }\n  } else if (name.startsWith('--component-passive-effect-unmount-start-')) {\n    const [componentName] = name.slice(41).split('-');\n\n    assertNoOverlappingComponentMeasure(state);\n\n    state.currentReactComponentMeasure = {\n      componentName,\n      timestamp: startTime,\n      duration: 0,\n      type: 'passive-effect-unmount',\n      warning: null,\n    };\n  } else if (name === '--component-passive-effect-unmount-stop') {\n    assertCurrentComponentMeasureType(state, 'passive-effect-unmount');\n\n    if (state.currentReactComponentMeasure !== null) {\n      const componentMeasure = state.currentReactComponentMeasure;\n      componentMeasure.duration = startTime - componentMeasure.timestamp;\n\n      state.currentReactComponentMeasure = null;\n\n      currentProfilerData.componentMeasures.push(componentMeasure);\n    }\n  }\n}\n\nfunction preprocessFlamechart(rawData: TimelineEvent[]): Flamechart {\n  let parsedData;\n  try {\n    parsedData = importFromChromeTimeline(rawData, 'react-devtools');\n  } catch (error) {\n    // Assume any Speedscope errors are caused by bad profiles\n    const errorToRethrow = new InvalidProfileError(error.message);\n    errorToRethrow.stack = error.stack;\n    throw errorToRethrow;\n  }\n\n  const profile = parsedData.profiles[0]; // TODO: Choose the main CPU thread only\n\n  const speedscopeFlamechart = new SpeedscopeFlamechart({\n    // $FlowFixMe[method-unbinding]\n    getTotalWeight: profile.getTotalWeight.bind(profile),\n    // $FlowFixMe[method-unbinding]\n    forEachCall: profile.forEachCall.bind(profile),\n    // $FlowFixMe[method-unbinding]\n    formatValue: profile.formatValue.bind(profile),\n    getColorBucketForFrame: () => 0,\n  });\n\n  const flamechart: Flamechart = speedscopeFlamechart.getLayers().map(layer =>\n    layer.map(\n      ({\n        start,\n        end,\n        node: {\n          frame: {name, file, line, col},\n        },\n      }) => ({\n        name,\n        timestamp: start / 1000,\n        duration: (end - start) / 1000,\n        scriptUrl: file,\n        locationLine: line,\n        locationColumn: col,\n      }),\n    ),\n  );\n\n  return flamechart;\n}\n\nfunction parseStackFrame(stackFrame: string): ErrorStackFrame | null {\n  const error = new Error();\n  error.stack = stackFrame;\n\n  const frames = ErrorStackParser.parse(error);\n\n  return frames.length === 1 ? frames[0] : null;\n}\n\nexport default async function preprocessData(\n  timeline: TimelineEvent[],\n): Promise<TimelineData> {\n  const flamechart = preprocessFlamechart(timeline);\n\n  const laneToReactMeasureMap: Map<ReactLane, Array<ReactMeasure>> = new Map();\n  for (let lane: ReactLane = 0; lane < REACT_TOTAL_NUM_LANES; lane++) {\n    laneToReactMeasureMap.set(lane, []);\n  }\n\n  const profilerData: TimelineData = {\n    batchUIDToMeasuresMap: new Map(),\n    componentMeasures: [],\n    duration: 0,\n    flamechart,\n    internalModuleSourceToRanges: new Map(),\n    laneToLabelMap: new Map(),\n    laneToReactMeasureMap,\n    nativeEvents: [],\n    networkMeasures: [],\n    otherUserTimingMarks: [],\n    reactVersion: null,\n    schedulingEvents: [],\n    snapshots: [],\n    snapshotHeight: 0,\n    startTime: 0,\n    suspenseEvents: [],\n    thrownErrors: [],\n  };\n\n  // Sort `timeline`. JSON Array Format trace events need not be ordered. See:\n  // https://docs.google.com/document/d/1CvAClvFfyA5R-PhYUmn5OOQtYMH4h6I0nSsKchNAySU/preview#heading=h.f2f0yd51wi15\n  timeline = timeline.filter(Boolean).sort((a, b) => (a.ts > b.ts ? 1 : -1));\n\n  // Events displayed in flamechart have timestamps relative to the profile\n  // event's startTime. Source: https://github.com/v8/v8/blob/44bd8fd7/src/inspector/js_protocol.json#L1486\n  //\n  // We'll thus expect there to be a 'Profile' event; if there is not one, we\n  // can deduce that there are no flame chart events. As we expect React\n  // scheduling profiling user timing marks to be recorded together with browser\n  // flame chart events, we can futher deduce that the data is invalid and we\n  // don't bother finding React events.\n  const indexOfProfileEvent = timeline.findIndex(\n    event => event.name === 'Profile',\n  );\n  if (indexOfProfileEvent === -1) {\n    return profilerData;\n  }\n\n  // Use Profile event's `startTime` as the start time to align with flame chart.\n  // TODO: Remove assumption that there'll only be 1 'Profile' event. If this\n  // assumption does not hold, the chart may start at the wrong time.\n  profilerData.startTime = timeline[indexOfProfileEvent].args.data.startTime;\n  profilerData.duration =\n    (timeline[timeline.length - 1].ts - profilerData.startTime) / 1000;\n\n  const state: ProcessorState = {\n    asyncProcessingPromises: [],\n    batchUID: 0,\n    currentReactComponentMeasure: null,\n    internalModuleCurrentStackFrame: null,\n    internalModuleStackStringSet: new Set(),\n    measureStack: [],\n    nativeEventStack: [],\n    nextRenderShouldGenerateNewBatchID: true,\n    potentialLongEvents: [],\n    potentialLongNestedUpdate: null,\n    potentialLongNestedUpdates: [],\n    potentialSuspenseEventsOutsideOfTransition: [],\n    requestIdToNetworkMeasureMap: new Map(),\n    uidCounter: 0,\n    unresolvedSuspenseEvents: new Map(),\n  };\n\n  timeline.forEach(event => processTimelineEvent(event, profilerData, state));\n\n  if (profilerVersion === null) {\n    if (\n      profilerData.schedulingEvents.length === 0 &&\n      profilerData.batchUIDToMeasuresMap.size === 0\n    ) {\n      // No profiler version could indicate data was logged using an older build of React,\n      // before an explicitly profiler version was included in the logging data.\n      // But it could also indicate that the website was either not using React, or using a production build.\n      // The easiest way to check for this case is to see if the data contains any scheduled updates or render work.\n      throw new InvalidProfileError(\n        'No React marks were found in the provided profile.' +\n          ' Please provide profiling data from an React application running in development or profiling mode.',\n      );\n    }\n\n    throw new InvalidProfileError(\n      `This version of profiling data is not supported by the current profiler.`,\n    );\n  }\n\n  // Validate that all events and measures are complete\n  const {measureStack} = state;\n  if (measureStack.length > 0) {\n    console.error('Incomplete events or measures', measureStack);\n  }\n\n  // Check for warnings.\n  state.potentialLongEvents.forEach(([nativeEvent, batchUID]) => {\n    // See how long the subsequent batch of React work was.\n    // Ignore any work that was already started.\n    const [startTime, stopTime] = getBatchRange(\n      batchUID,\n      profilerData,\n      nativeEvent.timestamp,\n    );\n    if (stopTime - startTime > NATIVE_EVENT_DURATION_THRESHOLD) {\n      nativeEvent.warning = WARNING_STRINGS.LONG_EVENT_HANDLER;\n    }\n  });\n  state.potentialLongNestedUpdates.forEach(([schedulingEvent, batchUID]) => {\n    // See how long the subsequent batch of React work was.\n    const [startTime, stopTime] = getBatchRange(batchUID, profilerData);\n    if (stopTime - startTime > NESTED_UPDATE_DURATION_THRESHOLD) {\n      // Don't warn about transition updates scheduled during the commit phase.\n      // e.g. useTransition, useDeferredValue\n      // These are allowed to be long-running.\n      if (\n        !schedulingEvent.lanes.some(\n          lane => profilerData.laneToLabelMap.get(lane) === 'Transition',\n        )\n      ) {\n        // FIXME: This warning doesn't account for \"nested updates\" that are\n        // spawned by useDeferredValue. Disabling temporarily until we figure\n        // out the right way to handle this.\n        // schedulingEvent.warning = WARNING_STRINGS.NESTED_UPDATE;\n      }\n    }\n  });\n  state.potentialSuspenseEventsOutsideOfTransition.forEach(\n    ([suspenseEvent, lanes]) => {\n      // HACK This is a bit gross but the numeric lane value might change between render versions.\n      if (\n        !lanes.some(\n          lane => profilerData.laneToLabelMap.get(lane) === 'Transition',\n        )\n      ) {\n        suspenseEvent.warning = WARNING_STRINGS.SUSPEND_DURING_UPDATE;\n      }\n    },\n  );\n\n  // Wait for any async processing to complete before returning.\n  // Since processing is done in a worker, async work must complete before data is serialized and returned.\n  await Promise.all(state.asyncProcessingPromises);\n\n  // Now that all images have been loaded, let's figure out the display size we're going to use for our thumbnails:\n  // both the ones rendered to the canvas and the ones shown on hover.\n  if (profilerData.snapshots.length > 0) {\n    // NOTE We assume a static window size here, which is not necessarily true but should be for most cases.\n    // Regardless, Chrome also sets a single size/ratio and stick with it- so we'll do the same.\n    const snapshot = profilerData.snapshots[0];\n\n    profilerData.snapshotHeight = Math.min(\n      snapshot.height,\n      SNAPSHOT_MAX_HEIGHT,\n    );\n  }\n\n  return profilerData;\n}\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as importFileModule from './importFile';\n\nexport const importFile = importFileModule.importFile;\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport 'regenerator-runtime/runtime';\n\nimport type {TimelineEvent} from '@elg/speedscope';\nimport type {ImportWorkerOutputData} from './index';\n\nimport preprocessData from './preprocessData';\nimport {readInputData} from './readInputData';\nimport InvalidProfileError from './InvalidProfileError';\n\nexport async function importFile(file: File): Promise<ImportWorkerOutputData> {\n  try {\n    const readFile = await readInputData(file);\n    const events: TimelineEvent[] = JSON.parse(readFile);\n    if (events.length === 0) {\n      throw new InvalidProfileError('No profiling data found in file.');\n    }\n\n    const processedData = await preprocessData(events);\n\n    return {\n      status: 'SUCCESS',\n      processedData,\n    };\n  } catch (error) {\n    if (error instanceof InvalidProfileError) {\n      return {\n        status: 'INVALID_PROFILE_ERROR',\n        error,\n      };\n    } else {\n      return {\n        status: 'UNEXPECTED_ERROR',\n        error,\n      };\n    }\n  }\n}\n", "/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport nullthrows from 'nullthrows';\nimport InvalidProfileError from './InvalidProfileError';\n\nexport const readInputData = (file: File): Promise<string> => {\n  if (!file.name.endsWith('.json')) {\n    throw new InvalidProfileError(\n      'Invalid file type. Only JSON performance profiles are supported',\n    );\n  }\n\n  const fileReader = new FileReader();\n\n  return new Promise((resolve, reject) => {\n    fileReader.onload = () => {\n      const result = nullthrows(fileReader.result);\n      if (typeof result === 'string') {\n        resolve(result);\n      }\n      reject(new InvalidProfileError('Input file was not read as a string'));\n    };\n\n    fileReader.onerror = () => reject(fileReader.error);\n\n    fileReader.readAsText(file);\n  });\n};\n"], "names": ["Object", "defineProperty", "exports", "value", "importFromOldV8CPUProfile", "importFromChromeCPUProfile", "importFromChromeTimeline", "isChromeTimeline", "profile_1", "require", "utils_1", "value_formatters_1", "v8cpuFormatter_1", "rawProfile", "Array", "isArray", "length", "first", "find", "e", "name", "events", "fileName", "cpuProfileByID", "Map", "pidTidById", "threadNameByPidTid", "sortBy", "ts", "event", "pidTid", "pid", "tid", "id", "set", "args", "data", "cpuProfile", "assign", "startTime", "endTime", "nodes", "samples", "timeDeltas", "get", "chunk", "concat", "console", "warn", "size", "profiles", "indexToView", "itForEach", "keys", "profileId", "threadName", "profile", "setName", "push", "Error", "callFrameToFrameInfo", "frameInfoForCallFrame", "callFrame", "getOrInsert", "functionName", "file", "url", "line", "lineNumber", "col", "columnNumber", "key", "shouldIgnoreFunction", "shouldPlaceOnTopOfPreviousStack", "chromeProfile", "CallTreeProfileBuilder", "nodeById", "node", "parent", "children", "childId", "child", "sampleTimes", "elapsed", "lastValidElapsed", "lastNodeId", "NaN", "i", "nodeId", "isNaN", "prevStack", "stackTop", "lca", "indexOf", "lastOf", "frame", "pop", "leaveFrame", "toOpen", "reverse", "enterFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Time<PERSON>ormatter", "build", "content", "chromeTreeToNodes", "treeToArray", "root", "visit", "scriptId", "hitCount", "map", "for<PERSON>ach", "head", "timestamps", "timestamp", "index", "cppfilt", "demangleCpp", "cache", "startsWith", "result", "undefined", "Function", "code", "slice", "Flamechart", "math_1", "constructor", "source", "this", "layers", "totalWeight", "minFrame<PERSON><PERSON><PERSON>", "stack", "Infinity", "getTotalWeight", "forEachCall", "start", "end", "assert", "layerIndex", "Math", "min", "isFinite", "getLayers", "getColorBucketForFrame", "getMinFrame<PERSON><PERSON><PERSON>", "formatValue", "v", "getClampedViewportWidth", "viewportWidth", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON>", "pow", "min<PERSON><PERSON><PERSON>", "clamp", "getClampedConfigSpaceViewportRect", "configSpaceViewportRect", "renderInverted", "configSpaceSize", "Vec2", "width", "x", "withX", "origin", "max", "zero", "minus", "plus", "Rect", "minVal", "maxVal", "AffineTransform", "y", "withY", "other", "times", "scalar", "timesPointwise", "dividedByPointwise", "dot", "equals", "approxEquals", "epsilon", "abs", "length2", "sqrt", "static", "a", "b", "flatten", "unit", "m00", "m01", "m02", "m10", "m11", "m12", "withScale", "s", "scaledBy", "getScale", "withTranslation", "t", "getTranslation", "translatedBy", "from", "to", "timesScalar", "det", "adj", "inverted", "transformVector", "inverseTransformVector", "inv", "transformPosition", "inverseTransformPosition", "transformRect", "r", "inverseTransformRect", "isEmpty", "height", "left", "right", "top", "bottom", "topLeft", "topRight", "bottomRight", "bottomLeft", "<PERSON><PERSON><PERSON><PERSON>", "withSize", "closestPointTo", "p", "distanceFrom", "contains", "hasIntersectionWith", "intersectWith", "area", "empty", "NDC", "__createBinding", "create", "o", "m", "k", "k2", "enumerable", "__setModuleDefault", "__importStar", "mod", "__esModule", "hasOwnProperty", "call", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "step", "next", "rejected", "done", "then", "apply", "StackListProfileBuilder", "Profile", "CallTreeNode", "<PERSON>ame", "HasWeights", "demangleCppModule", "selfWeight", "getSelfWeight", "addToTotalWeight", "delta", "addToSelfWeight", "overwriteWeightWith", "info", "super", "frozen", "isRoot", "isFrozen", "freeze", "frames", "KeyedSet", "appendOrderCalltreeRoot", "groupedCalltreeRoot", "weights", "valueFormatter", "RawV<PERSON>ueFormatter", "totalNonIdleWeight", "getAppendOrderCalltreeRoot", "getGroupedCalltreeRoot", "format", "f", "getWeightUnit", "getName", "getTotalNonIdleWeight", "reduce", "n", "c", "sortGroupedCallTree", "sort", "forEachCallGrouped", "openFrame", "closeFrame", "childTime", "sampleIndex", "forEachFrame", "fn", "getProfileWithRecursionFlattened", "builder", "framesInStack", "Set", "has", "add", "delete", "flattenedProfile", "getInvertedProfileForCallersOf", "focalFrameInfo", "focalFrame", "appendSampleWithWeight", "ret", "getProfileForCalleesOf", "findCalls", "focalFrameNode", "recordSubtree", "demangle", "remapNames", "callback", "arguments", "pendingSample", "_appendSample", "weight", "useAppendOrder", "frameInfo", "last", "appendSampleWithTimestamp", "centralTimestamp", "endTimestamp", "startTimestamp", "appendOrderStack", "groupedOrderStack", "lastValue", "addWeightsToFrames", "addWeightsToNodes", "_enterFrame", "prevTop", "frameCount", "_leaveFrame", "leavingStackTop", "decodeBase64", "lazyStatic", "memoizeByReference", "memoizeByShallowEquality", "objectsHaveShallowEquality", "noop", "binarySearch", "triangle", "fract", "formatPercent", "zeroPad", "itReduce", "itMap", "getOrThrow", "getOr<PERSON><PERSON>e", "keyA", "keyB", "fallback", "Symbol", "iterator", "values", "floor", "cb", "it", "init", "accum", "join", "percent", "formattedPercent", "toFixed", "lo", "hi", "target", "targetRangeSize", "mid", "base64lookupTable", "alphabet", "char<PERSON>t", "encoded", "lookupTable", "quartetCount", "byteCount", "bytes", "Uint8Array", "offset", "enc1", "enc2", "enc3", "enc4", "sextet1", "sextet2", "sextet3", "sextet4", "substring", "ByteFormatter", "toLocaleString", "multiplier", "formatUnsigned", "minutes", "seconds", "toString", "__exportStar", "factory", "define", "StackFrame", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "extractLocation", "urlLike", "parts", "exec", "replace", "split", "filter", "sanitizedLine", "location", "tokens", "locationParts", "functionNameRegex", "matches", "message", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "len", "argsRaw", "functionCall", "shift", "nullthrows", "framesToPop", "module", "_capitalize", "str", "toUpperCase", "_getter", "booleanProps", "numericProps", "stringProps", "props", "obj", "prototype", "getArgs", "set<PERSON>rgs", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "getLineNumber", "getColumnNumber", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "argsEndIndex", "lastIndexOf", "locationString", "Boolean", "j", "parseFloat", "Number", "String", "runtime", "Op", "hasOwn", "$Symbol", "iteratorSymbol", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "context", "Context", "_invoke", "state", "GenStateSuspendedStart", "method", "arg", "GenStateExecuting", "GenStateCompleted", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "ContinueSentinel", "sent", "_sent", "dispatchException", "abrupt", "record", "tryCatch", "type", "GenStateSuspendedYield", "makeInvokeMethod", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "Gp", "defineIteratorMethods", "AsyncIterator", "PromiseImpl", "invoke", "__await", "unwrapped", "previousPromise", "callInvokeWithMethodAndArg", "resultName", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "iter", "object", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "thrown", "<PERSON><PERSON><PERSON>", "regeneratorRuntime", "accidentalStrictMode", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "d", "definition", "prop", "REACT_TOTAL_NUM_LANES", "parseInt", "SCHEDULING_PROFILER_VERSION", "InvalidProfileError", "areInputsEqual", "newInputs", "lastInputs", "getBatchRange", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "memoize", "batchUID", "minStartTime", "measures", "batchUIDToMeasuresMap", "lastMeasure", "stopTime", "duration", "measure", "WARNING_STRINGS", "getLanesFromTransportDecimalBitmask", "laneBitmaskString", "laneBitmask", "lanes", "powersOfTwo", "profilerVersion", "markWorkStarted", "currentProfilerData", "measureStack", "depth", "<PERSON><PERSON><PERSON>h", "lane", "laneToReactMeasureMap", "mark<PERSON>orkCompleted", "assertNoOverlappingComponentMeasure", "currentReactComponentMeasure", "assertCurrentComponentMeasureType", "parseStackFrame", "stackFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "preprocessData", "timeline", "flamechart", "rawData", "parsedData", "errorTo<PERSON><PERSON><PERSON>", "SpeedscopeFlamechart", "bind", "layer", "scriptUrl", "locationLine", "locationColumn", "preprocessFlamechart", "profilerData", "componentMeasures", "internalModuleSourceToRanges", "laneToLabelMap", "nativeEvents", "networkMeasures", "otherUserTimingMarks", "reactVersion", "schedulingEvents", "snapshots", "snapshotHeight", "suspenseEvents", "thrownErrors", "indexOfProfileEvent", "findIndex", "asyncProcessingPromises", "internalModuleCurrentStackFrame", "internalModuleStackStringSet", "nativeEventStack", "nextRenderShouldGenerateNewBatchID", "potentialLongEvents", "potentialLongNestedUpdate", "potentialLongNestedUpdates", "potentialSuspenseEventsOutsideOfTransition", "requestIdToNetworkMeasureMap", "uid<PERSON><PERSON><PERSON>", "unresolvedSuspenseEvents", "cat", "ph", "snapshot", "image", "imageSource", "resolveFn", "fetch", "response", "blob", "createImageBitmap", "bitmap", "processScreenshot", "stackTrace", "includes", "dur", "prevNativeEvent", "nativeEvent", "warning", "processEventDispatch", "requestId", "networkMeasure", "finishTimestamp", "firstReceivedDataTimestamp", "lastReceivedDataTimestamp", "processResourceFinish", "processResourceReceivedData", "receiveResponseTimestamp", "processResourceReceiveResponse", "availableDepths", "fill", "requestMethod", "priority", "sendRequestTimestamp", "processResourceSendRequest", "versionString", "laneLabelTuplesString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "laneIndex", "updateLaneToLabelMap", "componentName", "componentMeasure", "processReactComponentMeasure", "forceUpdateEvent", "stateUpdateEvent", "phase", "<PERSON><PERSON><PERSON>", "suspenseEvent", "resolution", "lastIndex", "throwIfIncomplete", "getLastType", "stackFrameStart", "parsedStackFrameStart", "stackFrameStop", "parsedStackFrameStop", "range", "ranges", "JSON", "stringify", "processTimelineEvent", "schedulingEvent", "some", "all", "importFile", "readFile", "endsWith", "fileReader", "FileReader", "onload", "onerror", "readAsText", "readInputData", "status", "processedData"], "sourceRoot": ""}