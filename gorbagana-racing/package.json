{"name": "gorbagana-racing", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:next": "next dev", "build": "next build", "start": "NODE_ENV=production node server.js", "start:next": "next start", "lint": "next lint"}, "dependencies": {"@solana/wallet-adapter-backpack": "^0.1.14", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-wallets": "^0.19.37", "@solana/web3.js": "^1.98.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}