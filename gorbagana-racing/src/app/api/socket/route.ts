import { NextRequest } from 'next/server';
import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { MultiplayerGameServer } from '@/lib/socket-server';

let io: SocketIOServer;
let gameServer: MultiplayerGameServer;

export async function GET(req: NextRequest) {
  if (!io) {
    console.log('Initializing Socket.io server...');
    
    // Create HTTP server for Socket.io
    const httpServer = new HTTPServer();
    
    // Initialize multiplayer game server
    gameServer = new MultiplayerGameServer(httpServer);
    io = gameServer.getServer();
    
    console.log('Socket.io server initialized');
  }

  return new Response('Socket.io server is running', { status: 200 });
}

export async function POST(req: NextRequest) {
  // Handle Socket.io upgrade requests
  return new Response('Socket.io server is running', { status: 200 });
}
