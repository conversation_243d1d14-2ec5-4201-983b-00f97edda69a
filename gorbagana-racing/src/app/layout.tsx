import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { WalletContextProvider } from '@/components/WalletProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Gorbagana Grand Prix - Token-Powered Racing',
  description: 'A fast-paced multiplayer racing game on the Gorbagana testnet. Use tokens to power up and compete for prizes!',
  keywords: ['blockchain', 'racing', 'game', 'solana', 'gorbagana', 'multiplayer', 'tokens'],
  authors: [{ name: 'Gorbagana Racing Team' }],
  openGraph: {
    title: 'Gorbagana Grand Prix',
    description: 'Token-powered racing on the fastest blockchain',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <WalletContextProvider>
          <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
            <header className="border-b border-white/10 backdrop-blur-sm">
              <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      🏎️
                    </div>
                    <h1 className="text-2xl font-bold text-white">
                      Gorbagana Grand Prix
                    </h1>
                  </div>
                  <nav className="flex items-center gap-6">
                    <a href="#" className="text-white/80 hover:text-white transition-colors">
                      Races
                    </a>
                    <a href="#" className="text-white/80 hover:text-white transition-colors">
                      Leaderboard
                    </a>
                    <a href="#" className="text-white/80 hover:text-white transition-colors">
                      About
                    </a>
                  </nav>
                </div>
              </div>
            </header>
            <main className="container mx-auto px-4 py-8">
              {children}
            </main>
            <footer className="border-t border-white/10 mt-16">
              <div className="container mx-auto px-4 py-8">
                <div className="text-center text-white/60">
                  <p>Built for the Gorbagana testnet bounty</p>
                  <p className="mt-2 text-sm">
                    Powered by Solana • Built with Next.js • #GorbaganaTestnet
                  </p>
                </div>
              </div>
            </footer>
          </div>
        </WalletContextProvider>
      </body>
    </html>
  );
}
