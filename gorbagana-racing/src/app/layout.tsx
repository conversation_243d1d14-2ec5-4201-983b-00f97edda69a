import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { WalletContextProvider } from '@/components/WalletProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Gorbagana Race - Token-Powered Racing',
  description: 'A fast-paced multiplayer racing game on the Gorbagana testnet. Use tokens to power up and compete for prizes!',
  keywords: ['blockchain', 'racing', 'game', 'solana', 'gorbagana', 'multiplayer', 'tokens'],
  authors: [{ name: 'Gorbagana Racing Team' }],
  openGraph: {
    title: 'Gorbagana Car Race',
    description: 'Token-powered racing on the fastest blockchain',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <WalletContextProvider>
            <main className="container">
              {children}
            </main>
        </WalletContextProvider>
      </body>
    </html>
  );
}
