'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'game' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">🏎️ Gorbagana Racing</h1>
              <span className="text-sm text-white/70 bg-white/10 px-3 py-1 rounded-full">
                Free Play
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <nav className="hidden md:flex space-x-4">
                <button
                  onClick={() => setCurrentView('home')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'home' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Home
                </button>
                <button
                  onClick={() => setCurrentView('game')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'game' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Play
                </button>
                <button
                  onClick={() => setCurrentView('store')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'store' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Store
                </button>
                <button
                  onClick={() => setCurrentView('garage')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'garage' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Garage
                </button>
              </nav>
              <WalletButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        {currentView === 'home' && (
          <div className="space-y-8">
            {/* Hero Section */}
            <div className="text-center space-y-8">
              <div className="space-y-4">
                <h2 className="text-6xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent mb-4">
                  GORBAGANA RACING
                </h2>
                <p className="text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                  🏎️ High-speed thrills • 🪙 Collect tokens • ⚡ Speed boosts • 🏆 Beat your records!
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button
                  onClick={() => setCurrentView('game')}
                  className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-6 px-12 rounded-2xl text-xl transition-all transform hover:scale-105 shadow-2xl"
                >
                  🚀 START RACING NOW!
                </button>
                <div className="text-white/70 text-sm">
                  <div>✨ No wallet needed for testing</div>
                  <div>🎮 Play instantly & free</div>
                </div>
              </div>
            </div>

            {/* Game Features */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-6xl mb-4">🏎️</div>
                <h3 className="text-xl font-bold text-white mb-3">Lightning Fast</h3>
                <p className="text-white/80 leading-relaxed">
                  Smooth 60fps racing with instant controls. Feel the adrenaline as speed increases with every token!
                </p>
              </div>

              <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-6xl mb-4">🪙</div>
                <h3 className="text-xl font-bold text-white mb-3">Smart Mechanics</h3>
                <p className="text-white/80 leading-relaxed">
                  Collect tokens to boost your speed! Every 5 tokens = speed boost. Strategy meets skill!
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-6xl mb-4">🚀</div>
                <h3 className="text-xl font-bold text-white mb-3">Progressive Challenge</h3>
                <p className="text-white/80 leading-relaxed">
                  Multiple lives, increasing levels, and dynamic difficulty. How far can you go?
                </p>
              </div>
            </div>

            {/* How to Play */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">How to Play</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <h4 className="text-white font-semibold">Control Your Car</h4>
                      <p className="text-white/70 text-sm">Use arrow keys to move your yellow car around the track</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <h4 className="text-white font-semibold">Avoid Traffic</h4>
                      <p className="text-white/70 text-sm">Dodge the blue cars coming towards you</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <h4 className="text-white font-semibold">Survive & Score</h4>
                      <p className="text-white/70 text-sm">The longer you survive, the higher your score</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">4</div>
                    <div>
                      <h4 className="text-white font-semibold">Beat Your Record</h4>
                      <p className="text-white/70 text-sm">Challenge yourself to achieve higher scores</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'game' && (
          <div className="flex justify-center">
            <SimpleRacingGame />
          </div>
        )}

        {currentView === 'store' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">🏪 Car Store</h2>
            <p className="text-white/70 mb-6">Coming Soon! Buy new cars and upgrades with GORB tokens.</p>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🏎️</div>
                <h3 className="text-white font-medium">Sports Car</h3>
                <p className="text-white/60 text-sm">High speed, low handling</p>
                <p className="text-yellow-400 font-bold mt-2">500 GORB</p>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🚗</div>
                <h3 className="text-white font-medium">Racing Car</h3>
                <p className="text-white/60 text-sm">Balanced performance</p>
                <p className="text-yellow-400 font-bold mt-2">750 GORB</p>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🏁</div>
                <h3 className="text-white font-medium">Formula Car</h3>
                <p className="text-white/60 text-sm">Ultimate performance</p>
                <p className="text-yellow-400 font-bold mt-2">1000 GORB</p>
              </div>
            </div>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">🏠 Garage</h2>
            <p className="text-white/70 mb-6">Manage your car collection and upgrades.</p>
            <div className="bg-white/5 rounded-lg p-6">
              <h3 className="text-white font-medium mb-4">Current Car</h3>
              <div className="bg-yellow-500 w-16 h-24 mx-auto rounded mb-4"></div>
              <p className="text-white">Default Racing Car</p>
              <p className="text-white/60 text-sm">Speed: 75 | Handling: 80 | Acceleration: 70</p>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white/5 backdrop-blur-sm border-t border-white/10 mt-16">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="text-center">
            <p className="text-white/60">
              🏎️ Gorbagana Racing • Built for Gorbagana Testnet • Free to Play
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
