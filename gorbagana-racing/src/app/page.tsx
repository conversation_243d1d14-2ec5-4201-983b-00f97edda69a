'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'game' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-400 via-blue-500 to-purple-600">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">🏎️ Gorbagana Racing</h1>
              <span className="text-sm text-white/70 bg-white/10 px-3 py-1 rounded-full">
                Free Play
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <nav className="hidden md:flex space-x-4">
                <button
                  onClick={() => setCurrentView('home')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'home' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Home
                </button>
                <button
                  onClick={() => setCurrentView('game')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'game' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Play
                </button>
                <button
                  onClick={() => setCurrentView('store')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'store' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Store
                </button>
                <button
                  onClick={() => setCurrentView('garage')}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentView === 'garage' ? 'bg-white/20 text-white' : 'text-white/70 hover:text-white'
                  }`}
                >
                  Garage
                </button>
              </nav>
              <WalletButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        {currentView === 'home' && (
          <div className="space-y-8">
            {/* Hero Section */}
            <div className="text-center space-y-6">
              <h2 className="text-5xl font-bold text-white mb-4">
                Race to Victory!
              </h2>
              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                Experience the thrill of high-speed racing. Dodge traffic, collect tokens, and become the ultimate racing champion!
              </p>
              <button
                onClick={() => setCurrentView('game')}
                className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-4 px-8 rounded-xl text-lg transition-all transform hover:scale-105 shadow-lg"
              >
                🏁 Start Racing Now!
              </button>
            </div>

            {/* Game Features */}
            <div className="grid md:grid-cols-3 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-4xl mb-4">🏎️</div>
                <h3 className="text-lg font-semibold text-white mb-2">Fast-Paced Action</h3>
                <p className="text-white/70">
                  Experience smooth 60fps racing with responsive controls and realistic physics
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-lg font-semibold text-white mb-2">Skill-Based Gameplay</h3>
                <p className="text-white/70">
                  Master the art of dodging traffic and collecting tokens to achieve high scores
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-4xl mb-4">🏆</div>
                <h3 className="text-lg font-semibold text-white mb-2">Endless Challenge</h3>
                <p className="text-white/70">
                  Increasing difficulty and speed keep you on the edge of your seat
                </p>
              </div>
            </div>

            {/* How to Play */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6 text-center">How to Play</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">1</div>
                    <div>
                      <h4 className="text-white font-semibold">Control Your Car</h4>
                      <p className="text-white/70 text-sm">Use arrow keys to move your yellow car around the track</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">2</div>
                    <div>
                      <h4 className="text-white font-semibold">Avoid Traffic</h4>
                      <p className="text-white/70 text-sm">Dodge the blue cars coming towards you</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">3</div>
                    <div>
                      <h4 className="text-white font-semibold">Survive & Score</h4>
                      <p className="text-white/70 text-sm">The longer you survive, the higher your score</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-yellow-500 text-black rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">4</div>
                    <div>
                      <h4 className="text-white font-semibold">Beat Your Record</h4>
                      <p className="text-white/70 text-sm">Challenge yourself to achieve higher scores</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'game' && (
          <div className="flex justify-center">
            <SimpleRacingGame />
          </div>
        )}

        {currentView === 'store' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">🏪 Car Store</h2>
            <p className="text-white/70 mb-6">Coming Soon! Buy new cars and upgrades with GORB tokens.</p>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🏎️</div>
                <h3 className="text-white font-medium">Sports Car</h3>
                <p className="text-white/60 text-sm">High speed, low handling</p>
                <p className="text-yellow-400 font-bold mt-2">500 GORB</p>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🚗</div>
                <h3 className="text-white font-medium">Racing Car</h3>
                <p className="text-white/60 text-sm">Balanced performance</p>
                <p className="text-yellow-400 font-bold mt-2">750 GORB</p>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-2xl mb-2">🏁</div>
                <h3 className="text-white font-medium">Formula Car</h3>
                <p className="text-white/60 text-sm">Ultimate performance</p>
                <p className="text-yellow-400 font-bold mt-2">1000 GORB</p>
              </div>
            </div>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">🏠 Garage</h2>
            <p className="text-white/70 mb-6">Manage your car collection and upgrades.</p>
            <div className="bg-white/5 rounded-lg p-6">
              <h3 className="text-white font-medium mb-4">Current Car</h3>
              <div className="bg-yellow-500 w-16 h-24 mx-auto rounded mb-4"></div>
              <p className="text-white">Default Racing Car</p>
              <p className="text-white/60 text-sm">Speed: 75 | Handling: 80 | Acceleration: 70</p>
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white/5 backdrop-blur-sm border-t border-white/10 mt-16">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="text-center">
            <p className="text-white/60">
              🏎️ Gorbagana Racing • Built for Gorbagana Testnet • Free to Play
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
