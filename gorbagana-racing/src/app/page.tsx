'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';
import { PvPRacingGame } from '@/components/PvPRacingGame';
import { EnhancedCarStore } from '@/components/EnhancedCarStore';
import { WalletButton } from '@/components/WalletButton';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'endless' | 'pvp' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Simple Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">🏎️</span>
              <h1 className="text-xl font-bold text-white">Gorbagana Racing</h1>
            </div>
            <WalletButton />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {currentView === 'home' && (
          <div className="space-y-8">
            {/* Simple Title */}
            <div className="text-center">
              <h2 className="text-4xl font-bold text-white mb-4">Choose Game Mode</h2>
            </div>

            {/* Game Mode Buttons */}
            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <button
                onClick={() => setCurrentView('endless')}
                className="bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg p-6 text-center transition-colors"
              >
                <div className="text-4xl mb-3">🛣️</div>
                <h3 className="text-xl font-bold text-white mb-2">Endless</h3>
                <p className="text-gray-400 text-sm">Survive the highway</p>
              </button>

              <button
                onClick={() => setCurrentView('pvp')}
                className="bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg p-6 text-center transition-colors"
              >
                <div className="text-4xl mb-3">🏁</div>
                <h3 className="text-xl font-bold text-white mb-2">PvP Race</h3>
                <p className="text-gray-400 text-sm">Race vs computer</p>
              </button>

              <button
                onClick={() => setCurrentView('store')}
                className="bg-gray-800 hover:bg-gray-700 border border-gray-600 rounded-lg p-6 text-center transition-colors"
              >
                <div className="text-4xl mb-3">🏪</div>
                <h3 className="text-xl font-bold text-white mb-2">Store</h3>
                <p className="text-gray-400 text-sm">Buy cars & upgrades</p>
              </button>
            </div>
          </div>
        )}

        {(currentView === 'endless' || currentView === 'pvp') && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">
                {currentView === 'endless' ? '🛣️ Endless Mode' : '🏁 PvP Race'}
              </h2>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                ← Back
              </button>
            </div>

            {/* Game Layout */}
            <div className="grid lg:grid-cols-4 gap-8">
              {/* Game Area */}
              <div className="lg:col-span-3 flex justify-center">
                {currentView === 'endless' ? <SimpleRacingGame /> : <PvPRacingGame />}
              </div>

              {/* Side Info */}
              <div className="space-y-4">
                <div className="bg-gray-800 rounded-lg p-4">
                  <h3 className="text-white font-bold mb-3">Controls</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div>🎮 Arrow Keys - Move</div>
                    <div>🚗 Avoid cars</div>
                    <div>🪙 Collect tokens</div>
                    <div>🕳️ Avoid obstacles</div>
                  </div>
                </div>

                {currentView === 'pvp' && (
                  <div className="bg-gray-800 rounded-lg p-4">
                    <h3 className="text-white font-bold mb-3">PvP Mode</h3>
                    <div className="space-y-2 text-sm text-gray-300">
                      <div>🤖 Race vs AI</div>
                      <div>🏁 First to finish wins</div>
                      <div>⚡ No tokens needed</div>
                      <div>🏆 Test your skills</div>
                    </div>
                  </div>
                )}

                <div className="bg-gray-800 rounded-lg p-4">
                  <h3 className="text-white font-bold mb-3">Tips</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div>⚡ 5 tokens = speed boost</div>
                    <div>❤️ 3 lives total</div>
                    <div>📈 Speed increases with level</div>
                    <div>🎯 Aim for high distance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'store' && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">🏪 Store</h2>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                ← Back
              </button>
            </div>

            <EnhancedCarStore />
          </div>
        )}

        {currentView === 'garage' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">ℹ️ About</h2>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                ← Back
              </button>
            </div>

            <div className="bg-gray-800 rounded-lg p-6">
              <p className="text-gray-300 mb-4">
                Gorbagana Racing is a blockchain racing game. Race, collect tokens, and compete!
              </p>
              <div className="space-y-2 text-sm text-gray-400">
                <div>• Built for Gorbagana Testnet</div>
                <div>• Free to play</div>
                <div>• Collect GORB tokens</div>
                <div>• Buy cars and upgrades</div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}