'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'game' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600">
      {/* Simple Header with Wallet */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-white">🏎️ Gorbagana Racing</h1>
            <WalletButton />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        {/* Simple Navigation */}
        {currentView !== 'home' && (
          <div className="text-center mb-8">
            <button
              onClick={() => setCurrentView('home')}
              className="bg-white/10 hover:bg-white/20 text-white px-6 py-2 rounded-lg transition-colors"
            >
              ← Back to Home
            </button>
          </div>
        )}

        {currentView === 'home' && (
          <div className="text-center space-y-12">
            {/* Simple Hero */}
            <div className="space-y-6">
              <h2 className="text-7xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent">
                GORBAGANA RACING
              </h2>
              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                High-speed racing • Collect tokens • Beat your score!
              </p>
            </div>

            {/* Main Play Button */}
            <button
              onClick={() => setCurrentView('game')}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-8 px-16 rounded-3xl text-2xl transition-all transform hover:scale-105 shadow-2xl"
            >
              🏁 START RACING
            </button>

            {/* Simple Navigation */}
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setCurrentView('store')}
                className="bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-xl transition-colors"
              >
                🏪 Store
              </button>
              <button
                onClick={() => setCurrentView('garage')}
                className="bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-xl transition-colors"
              >
                🏠 Garage
              </button>
            </div>

            {/* Simple Instructions */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-md mx-auto">
              <h3 className="text-white font-bold mb-4">How to Play</h3>
              <div className="text-white/80 space-y-2">
                <div>🎮 Use arrow keys to control your car</div>
                <div>🚗 Avoid other cars</div>
                <div>🪙 Collect tokens for speed boosts</div>
                <div>🏆 Survive as long as possible</div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'game' && (
          <div className="flex justify-center">
            <SimpleRacingGame />
          </div>
        )}

        {currentView === 'store' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">🏪 Store</h2>
            <p className="text-white/70 text-xl mb-8">Coming Soon!</p>
            <p className="text-white/60">Buy cars and upgrades with GORB tokens</p>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center">
            <h2 className="text-4xl font-bold text-white mb-4">🏠 Garage</h2>
            <p className="text-white/70 text-xl mb-8">Coming Soon!</p>
            <p className="text-white/60">Manage your car collection</p>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white/5 backdrop-blur-sm border-t border-white/10 mt-16">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="text-center">
            <p className="text-white/60">
              🏎️ Gorbagana Racing • Built for Gorbagana Testnet • Free to Play
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
