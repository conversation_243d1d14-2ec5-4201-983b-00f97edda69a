'use client';

import React, { useState } from 'react';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';
import { PvPRacingGame } from '@/components/PvPRacingGame';
import { EnhancedCarStore } from '@/components/EnhancedCarStore';
import { WalletButton } from '@/components/WalletButton';

export default function Home() {
  const [currentView, setCurrentView] = useState<'home' | 'endless' | 'pvp' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Fixed Header */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-black/30 backdrop-blur-xl border-b border-purple-500/20">
        <div className="w-full px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setCurrentView('home')}
                className="flex items-center space-x-3 hover:scale-105 transition-transform"
              >
                <div className="relative">
                  <span className="text-3xl animate-pulse">🏎️</span>
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-30"></div>
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Gorbagana Racing
                  </h1>
                  <p className="text-purple-300/60 text-sm">Blockchain Racing Revolution</p>
                </div>
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-2 text-purple-300/80 text-sm">
                <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                <span>Gorbagana Testnet</span>
              </div>
              <WalletButton />
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pt-24 min-h-screen w-full">
        {currentView === 'home' && (
          <div className="w-full px-6 space-y-16 py-12">
            {/* Hero Section */}
            <div className="text-center space-y-8">
              <div className="relative">
                <h2 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent mb-6">
                  RACE THE FUTURE
                </h2>
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 blur-3xl -z-10"></div>
              </div>

              <p className="text-xl md:text-2xl text-purple-200/90 max-w-4xl mx-auto leading-relaxed">
                Experience the ultimate blockchain racing adventure. Collect NFT cars, earn GORB tokens,
                and compete in high-speed races on the Gorbagana network.
              </p>

              <div className="flex flex-wrap justify-center gap-4 text-purple-300/80">
                <div className="flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span>Live on Testnet</span>
                </div>
                <div className="flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span>Free to Play</span>
                </div>
                <div className="flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full">
                  <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                  <span>Earn Real Tokens</span>
                </div>
              </div>
            </div>

            {/* Game Mode Cards */}
            <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
              {/* Endless Mode */}
              <button
                onClick={() => setCurrentView('endless')}
                className="group relative bg-gradient-to-br from-purple-900/40 to-blue-900/40 hover:from-purple-800/60 hover:to-blue-800/60 border border-purple-500/30 hover:border-purple-400/50 rounded-2xl p-8 text-center transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 to-blue-600/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="text-6xl mb-6 group-hover:animate-bounce">🛣️</div>
                  <h3 className="text-2xl font-bold text-white mb-4">Endless Highway</h3>
                  <p className="text-purple-200/80 mb-6 leading-relaxed">
                    Survive the infinite highway! Dodge traffic, collect golden tokens, and see how far your skills can take you.
                  </p>
                  <div className="space-y-2 text-sm text-purple-300/70">
                    <div className="flex items-center justify-center space-x-2">
                      <span>⚡</span>
                      <span>Progressive difficulty</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>🪙</span>
                      <span>Collect GORB tokens</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>🏆</span>
                      <span>Beat your high score</span>
                    </div>
                  </div>
                </div>
              </button>

              {/* PvP Mode */}
              <button
                onClick={() => setCurrentView('pvp')}
                className="group relative bg-gradient-to-br from-red-900/40 to-orange-900/40 hover:from-red-800/60 hover:to-orange-800/60 border border-red-500/30 hover:border-red-400/50 rounded-2xl p-8 text-center transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-red-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-red-600/10 to-orange-600/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="text-6xl mb-6 group-hover:animate-bounce">🏁</div>
                  <h3 className="text-2xl font-bold text-white mb-4">PvP Racing</h3>
                  <p className="text-red-200/80 mb-6 leading-relaxed">
                    Challenge other racers in intense head-to-head competitions. Test your skills against AI opponents.
                  </p>
                  <div className="space-y-2 text-sm text-red-300/70">
                    <div className="flex items-center justify-center space-x-2">
                      <span>🤖</span>
                      <span>Race vs AI opponents</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>⚡</span>
                      <span>No tokens required</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>🏆</span>
                      <span>Winner takes glory</span>
                    </div>
                  </div>
                </div>
              </button>

              {/* Store */}
              <button
                onClick={() => setCurrentView('store')}
                className="group relative bg-gradient-to-br from-green-900/40 to-emerald-900/40 hover:from-green-800/60 hover:to-emerald-800/60 border border-green-500/30 hover:border-green-400/50 rounded-2xl p-8 text-center transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-green-600/10 to-emerald-600/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10">
                  <div className="text-6xl mb-6 group-hover:animate-bounce">🏪</div>
                  <h3 className="text-2xl font-bold text-white mb-4">Car Marketplace</h3>
                  <p className="text-green-200/80 mb-6 leading-relaxed">
                    Build your dream garage! Buy legendary cars, upgrade performance, and customize your racing experience.
                  </p>
                  <div className="space-y-2 text-sm text-green-300/70">
                    <div className="flex items-center justify-center space-x-2">
                      <span>🏎️</span>
                      <span>Collect NFT cars</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>⚙️</span>
                      <span>Performance upgrades</span>
                    </div>
                    <div className="flex items-center justify-center space-x-2">
                      <span>💎</span>
                      <span>Blockchain ownership</span>
                    </div>
                  </div>
                </div>
              </button>
            </div>

            {/* Features Section */}
            <div className="bg-black/20 backdrop-blur-xl rounded-3xl p-8 border border-purple-500/20">
              <h3 className="text-3xl font-bold text-center text-white mb-8">Why Choose Gorbagana Racing?</h3>
              <div className="grid md:grid-cols-3 gap-8">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-2xl mx-auto">
                    🔗
                  </div>
                  <h4 className="text-xl font-bold text-white">True Ownership</h4>
                  <p className="text-purple-200/70">
                    Your cars and upgrades are stored on the blockchain. Trade, sell, or keep them forever.
                  </p>
                </div>
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center text-2xl mx-auto">
                    ⚡
                  </div>
                  <h4 className="text-xl font-bold text-white">Lightning Fast</h4>
                  <p className="text-purple-200/70">
                    Built on Solana for instant transactions and smooth 60fps gameplay experience.
                  </p>
                </div>
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center text-2xl mx-auto">
                    💰
                  </div>
                  <h4 className="text-xl font-bold text-white">Earn While Playing</h4>
                  <p className="text-purple-200/70">
                    Collect GORB tokens through racing and use them to upgrade your garage.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {(currentView === 'endless' || currentView === 'pvp') && (
          <div className="w-full h-screen flex flex-col">
            {/* Game Header */}
            <div className="flex items-center justify-between bg-black/20 backdrop-blur-xl mx-6 mt-4 rounded-2xl p-4 border border-purple-500/20">
              <div className="flex items-center space-x-3">
                <div className="text-3xl">
                  {currentView === 'endless' ? '🛣️' : '🏁'}
                </div>
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    {currentView === 'endless' ? 'Endless Highway' : 'PvP Racing Arena'}
                  </h2>
                  <p className="text-purple-200/70 text-sm">
                    {currentView === 'endless'
                      ? 'Survive the infinite road and collect tokens'
                      : 'Race against AI opponents in real-time'
                    }
                  </p>
                </div>
              </div>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-4 py-2 rounded-xl transition-all transform hover:scale-105 shadow-lg"
              >
                ← Menu
              </button>
            </div>

            {/* Full Screen Game Layout */}
            <div className="flex-1 flex gap-6 p-6">
              {/* Large Game Area */}
              <div className="flex-1">
                <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-4 border border-purple-500/20 h-full">
                  {currentView === 'endless' ? <SimpleRacingGame /> : <PvPRacingGame />}
                </div>
              </div>

              {/* Compact Side Panel */}
              <div className="w-80 space-y-4">
                {/* Game Controls */}
                <div className="bg-gradient-to-br from-green-900/40 to-blue-900/40 backdrop-blur-xl rounded-xl p-4 border border-green-500/20">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center text-sm">
                      🎮
                    </div>
                    <h3 className="text-white font-bold">Game Control</h3>
                  </div>

                  {/* Start/Restart Button */}
                  <button
                    onClick={() => window.location.reload()}
                    className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-4 rounded-xl transition-all transform hover:scale-105 shadow-lg mb-3"
                  >
                    🔄 Restart Game
                  </button>

                  {/* Speed Controls */}
                  <div className="space-y-2">
                    <label className="text-white text-sm font-medium">Game Speed</label>
                    <div className="flex space-x-2">
                      <button className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-3 rounded text-sm">
                        🐌 Slow
                      </button>
                      <button className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm">
                        ⚡ Fast
                      </button>
                    </div>
                  </div>
                </div>

                {/* Controls Card */}
                <div className="bg-gradient-to-br from-blue-900/40 to-purple-900/40 backdrop-blur-xl rounded-xl p-4 border border-blue-500/20">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-sm">
                      ⌨️
                    </div>
                    <h3 className="text-white font-bold">Controls</h3>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2 text-blue-200">
                      <span className="bg-blue-500/20 px-2 py-1 rounded text-xs font-mono">←→</span>
                      <span>Steer left/right</span>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-200">
                      <span className="bg-blue-500/20 px-2 py-1 rounded text-xs font-mono">↑↓</span>
                      <span>Speed up/down</span>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-200">
                      <span className="bg-blue-500/20 px-2 py-1 rounded text-xs font-mono">R</span>
                      <span>Restart</span>
                    </div>
                  </div>
                </div>

                {/* Tips Card */}
                <div className="bg-gradient-to-br from-green-900/40 to-emerald-900/40 backdrop-blur-xl rounded-xl p-4 border border-green-500/20">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center text-sm">
                      💡
                    </div>
                    <h3 className="text-white font-bold">Tips</h3>
                  </div>
                  <div className="space-y-2 text-sm text-green-200">
                    <div>⚡ 5 tokens = speed boost</div>
                    <div>❤️ You have 3 lives</div>
                    <div>📈 Speed increases each level</div>
                  </div>
                </div>

                {/* Stats Card */}
                <div className="bg-gradient-to-br from-purple-900/40 to-pink-900/40 backdrop-blur-xl rounded-xl p-4 border border-purple-500/20">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-sm">
                      📊
                    </div>
                    <h3 className="text-white font-bold">Stats</h3>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between text-purple-200">
                      <span>Best:</span>
                      <span className="font-bold text-yellow-400">1,247m</span>
                    </div>
                    <div className="flex justify-between text-purple-200">
                      <span>Tokens:</span>
                      <span className="font-bold text-green-400">🪙 156</span>
                    </div>
                    <div className="flex justify-between text-purple-200">
                      <span>Wins:</span>
                      <span className="font-bold text-blue-400">🏆 23</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'store' && (
          <div className="w-full h-screen flex flex-col">
            {/* Store Header */}
            <div className="flex items-center justify-between bg-black/20 backdrop-blur-xl mx-6 mt-4 rounded-2xl p-4 border border-green-500/20">
              <div className="flex items-center space-x-3">
                <div className="text-3xl">🏪</div>
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                    Car Marketplace
                  </h2>
                  <p className="text-green-200/70 text-sm">
                    Build your dream garage with NFT cars and upgrades
                  </p>
                </div>
              </div>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-xl transition-all transform hover:scale-105 shadow-lg"
              >
                ← Menu
              </button>
            </div>

            {/* Store Content */}
            <div className="flex-1 mx-6 mb-6 bg-black/30 backdrop-blur-xl rounded-2xl p-6 border border-green-500/20 overflow-y-auto">
              <EnhancedCarStore />
            </div>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="space-y-8 py-8">
            {/* About Header */}
            <div className="flex items-center justify-between bg-black/20 backdrop-blur-xl rounded-2xl p-6 border border-blue-500/20">
              <div className="flex items-center space-x-4">
                <div className="text-4xl">ℹ️</div>
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                    About Gorbagana Racing
                  </h2>
                  <p className="text-blue-200/70">
                    The future of blockchain racing is here
                  </p>
                </div>
              </div>
              <button
                onClick={() => setCurrentView('home')}
                className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl transition-all transform hover:scale-105 shadow-lg"
              >
                ← Back to Menu
              </button>
            </div>

            {/* About Content */}
            <div className="grid md:grid-cols-2 gap-8">
              {/* Main Info */}
              <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-blue-500/20">
                <h3 className="text-2xl font-bold text-white mb-6">What is Gorbagana Racing?</h3>
                <p className="text-blue-200/80 mb-6 leading-relaxed">
                  Gorbagana Racing is a revolutionary blockchain racing game that combines high-speed action
                  with true digital ownership. Built on the Solana network, it offers lightning-fast gameplay
                  and seamless NFT integration.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <span className="text-green-400 text-xl">🎮</span>
                    <div>
                      <h4 className="text-white font-semibold">Play & Earn</h4>
                      <p className="text-blue-200/70 text-sm">Collect GORB tokens while racing</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="text-purple-400 text-xl">🏎️</span>
                    <div>
                      <h4 className="text-white font-semibold">Own Your Cars</h4>
                      <p className="text-blue-200/70 text-sm">NFT cars stored on blockchain</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <span className="text-yellow-400 text-xl">⚡</span>
                    <div>
                      <h4 className="text-white font-semibold">Lightning Fast</h4>
                      <p className="text-blue-200/70 text-sm">Built on Solana for instant transactions</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Technical Details */}
              <div className="bg-black/30 backdrop-blur-xl rounded-2xl p-8 border border-purple-500/20">
                <h3 className="text-2xl font-bold text-white mb-6">Technical Specs</h3>
                <div className="space-y-6">
                  <div>
                    <h4 className="text-purple-400 font-semibold mb-2">Blockchain</h4>
                    <p className="text-purple-200/80 text-sm">Solana Network (Testnet)</p>
                  </div>
                  <div>
                    <h4 className="text-purple-400 font-semibold mb-2">Token Standard</h4>
                    <p className="text-purple-200/80 text-sm">SPL Tokens (GORB)</p>
                  </div>
                  <div>
                    <h4 className="text-purple-400 font-semibold mb-2">NFT Standard</h4>
                    <p className="text-purple-200/80 text-sm">Metaplex NFTs for cars</p>
                  </div>
                  <div>
                    <h4 className="text-purple-400 font-semibold mb-2">Game Engine</h4>
                    <p className="text-purple-200/80 text-sm">React + Canvas (60fps)</p>
                  </div>
                </div>

                <div className="mt-8 pt-6 border-t border-purple-500/20">
                  <h4 className="text-white font-semibold mb-4">Connect With Us</h4>
                  <div className="flex space-x-4">
                    <button className="bg-purple-600/20 hover:bg-purple-600/40 text-purple-300 px-4 py-2 rounded-lg transition-colors">
                      Discord
                    </button>
                    <button className="bg-blue-600/20 hover:bg-blue-600/40 text-blue-300 px-4 py-2 rounded-lg transition-colors">
                      Twitter
                    </button>
                    <button className="bg-gray-600/20 hover:bg-gray-600/40 text-gray-300 px-4 py-2 rounded-lg transition-colors">
                      GitHub
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Modern Footer */}
      <footer className="bg-black/40 backdrop-blur-xl border-t border-purple-500/20 mt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-2xl">🏎️</span>
                <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Gorbagana Racing
                </span>
              </div>
              <p className="text-purple-200/70 text-sm">
                The ultimate blockchain racing experience. Own your cars, earn tokens, and race to victory.
              </p>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Game Modes</h4>
              <div className="space-y-2 text-sm text-purple-200/70">
                <div>🛣️ Endless Highway</div>
                <div>🏁 PvP Racing</div>
                <div>🏪 Car Marketplace</div>
              </div>
            </div>

            <div>
              <h4 className="text-white font-semibold mb-4">Network Status</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                  <span className="text-purple-200/70">Gorbagana Testnet Online</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                  <span className="text-purple-200/70">Game Servers Active</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t border-purple-500/20 mt-8 pt-6 text-center">
            <p className="text-purple-200/50 text-sm">
              © 2024 Gorbagana Racing. Built on Solana. Race responsibly.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}