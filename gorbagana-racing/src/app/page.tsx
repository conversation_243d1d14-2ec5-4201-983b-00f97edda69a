'use client';

import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { GameHub } from '@/components/GameHub';

export default function Home() {
  const { connected } = useWallet();

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-white">�️ Gorbagana Grand Prix</h1>
              <span className="text-sm text-white/70 bg-white/10 px-3 py-1 rounded-full">
                Enhanced Edition
              </span>
            </div>
            <WalletButton />
          </div>
        </div>
      </header>

      {/* Main Game Hub */}
      <GameHub />
    </div>
  );
}
