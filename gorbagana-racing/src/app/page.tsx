'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'endless' | 'pvp' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600">
      {/* Navigation Header */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <span className="text-3xl">🏎️</span>
                <h1 className="text-2xl font-bold text-white">Gorbagana Grand Prix</h1>
              </div>
              <nav className="hidden md:flex space-x-6">
                <button
                  onClick={() => setCurrentView('home')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'home' ? 'text-white font-semibold' : ''
                  }`}
                >
                  Races
                </button>
                <button
                  onClick={() => setCurrentView('store')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'store' ? 'text-white font-semibold' : ''
                  }`}
                >
                  Store
                </button>
                <button
                  onClick={() => setCurrentView('garage')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'garage' ? 'text-white font-semibold' : ''
                  }`}
                >
                  About
                </button>
              </nav>
            </div>
            <WalletButton />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {currentView === 'home' && (
          <div className="py-12">
            {/* Hero Section */}
            <div className="text-center space-y-8 mb-16">
              <h2 className="text-7xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent">
                GORBAGANA RACING
              </h2>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                Choose your racing experience • Collect tokens • Compete for prizes!
              </p>
            </div>

            {/* Game Modes */}
            <div className="max-w-5xl mx-auto grid md:grid-cols-3 gap-8 mb-16">
              {/* Endless Mode */}
              <div className="bg-gradient-to-br from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-center space-y-6">
                  <div className="text-6xl">🛣️</div>
                  <h3 className="text-2xl font-bold text-white">Endless Runner</h3>
                  <p className="text-white/80 leading-relaxed">
                    Survive the infinite highway! Dodge traffic, collect tokens, and see how far you can go.
                  </p>
                  <div className="space-y-2 text-sm text-white/70">
                    <div>• Infinite road with increasing speed</div>
                    <div>• Collect tokens for upgrades</div>
                    <div>• Beat your high score</div>
                  </div>
                  <button
                    onClick={() => setCurrentView('endless')}
                    className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-4 px-6 rounded-xl transition-all transform hover:scale-105"
                  >
                    🚀 Play Endless
                  </button>
                </div>
              </div>

              {/* PvP Mode */}
              <div className="bg-gradient-to-br from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-center space-y-6">
                  <div className="text-6xl">🏁</div>
                  <h3 className="text-2xl font-bold text-white">PvP Racing</h3>
                  <p className="text-white/80 leading-relaxed">
                    Race against other players! Stake tokens and winner takes all the prize pool.
                  </p>
                  <div className="space-y-2 text-sm text-white/70">
                    <div>• Stake tokens to enter</div>
                    <div>• Race against real players</div>
                    <div>• Winner takes the prize pool</div>
                  </div>
                  <button
                    onClick={() => setCurrentView('pvp')}
                    className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white font-bold py-4 px-6 rounded-xl transition-all transform hover:scale-105"
                  >
                    ⚔️ Race PvP
                  </button>
                </div>
              </div>

              {/* Store */}
              <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:border-white/40 transition-all transform hover:scale-105">
                <div className="text-center space-y-6">
                  <div className="text-6xl">🏪</div>
                  <h3 className="text-2xl font-bold text-white">Car Store</h3>
                  <p className="text-white/80 leading-relaxed">
                    Buy new cars and upgrades with your earned tokens. Customize your ride!
                  </p>
                  <div className="space-y-2 text-sm text-white/70">
                    <div>• Buy cars with different stats</div>
                    <div>• Upgrade speed & handling</div>
                    <div>• Customize appearance</div>
                  </div>
                  <button
                    onClick={() => setCurrentView('store')}
                    className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-4 px-6 rounded-xl transition-all transform hover:scale-105"
                  >
                    🛒 Visit Store
                  </button>
                </div>
              </div>
            </div>

            {/* Quick Instructions */}
            <div className="text-center max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-white mb-4">Controls</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-white/80">
                <div>🎮 Arrow Keys</div>
                <div>🚗 Steer & Speed</div>
                <div>🪙 Collect Tokens</div>
                <div>🏆 Win Races</div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'endless' && (
          <div className="py-8">
            <div className="text-center mb-6">
              <h2 className="text-4xl font-bold text-white mb-2">🛣️ Endless Runner</h2>
              <p className="text-white/70">Survive the infinite highway!</p>
              <button
                onClick={() => setCurrentView('home')}
                className="mt-4 bg-white/10 hover:bg-white/20 text-white px-6 py-2 rounded-lg transition-colors"
              >
                ← Back to Modes
              </button>
            </div>
            <div className="flex justify-center">
              <SimpleRacingGame />
            </div>
          </div>
        )}

        {currentView === 'pvp' && (
          <div className="text-center py-20">
            <h2 className="text-6xl font-bold text-white mb-8">🏁 PvP Racing</h2>
            <div className="max-w-2xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-white/70 text-xl mb-8">Coming Soon!</p>
              <p className="text-white/60 mb-8">
                Race against other players with token stakes. Winner takes all!
              </p>
              <div className="space-y-4 text-white/80">
                <div>🎯 Real-time multiplayer racing</div>
                <div>💰 Stake tokens to enter races</div>
                <div>🏆 Winner gets the entire prize pool</div>
                <div>⚡ Short, skill-based races</div>
              </div>
              <button
                onClick={() => setCurrentView('home')}
                className="mt-8 bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-lg transition-colors"
              >
                ← Back to Modes
              </button>
            </div>
          </div>
        )}

        {currentView === 'store' && (
          <div className="py-12">
            <div className="text-center mb-12">
              <h2 className="text-6xl font-bold text-white mb-4">🏪 Car Store</h2>
              <p className="text-white/70 text-xl">Buy cars and upgrades with your tokens</p>
              <button
                onClick={() => setCurrentView('home')}
                className="mt-4 bg-white/10 hover:bg-white/20 text-white px-6 py-2 rounded-lg transition-colors"
              >
                ← Back to Modes
              </button>
            </div>

            {/* Player Stats */}
            <div className="max-w-4xl mx-auto mb-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div>
                    <div className="text-3xl font-bold text-yellow-400">1,250</div>
                    <div className="text-white/70">GORB Tokens</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue-400">3</div>
                    <div className="text-white/70">Cars Owned</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-green-400">7</div>
                    <div className="text-white/70">Upgrades</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Store Tabs */}
            <div className="max-w-6xl mx-auto">
              <div className="flex justify-center mb-8">
                <div className="bg-white/10 rounded-xl p-1 flex">
                  <button className="bg-white/20 text-white px-6 py-3 rounded-lg font-medium">
                    🏎️ Cars
                  </button>
                  <button className="text-white/70 hover:text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    ⚙️ Upgrades
                  </button>
                  <button className="text-white/70 hover:text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    🎨 Skins
                  </button>
                </div>
              </div>

              {/* Cars Section */}
              <div className="grid md:grid-cols-3 gap-8">
                {/* Starter Car */}
                <div className="bg-gradient-to-br from-gray-500/20 to-gray-600/20 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <div className="text-center space-y-4">
                    <div className="text-4xl">🚗</div>
                    <h3 className="text-xl font-bold text-white">City Cruiser</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between text-white/80">
                        <span>Speed:</span>
                        <div className="flex">
                          {'★'.repeat(3)}{'☆'.repeat(2)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Handling:</span>
                        <div className="flex">
                          {'★'.repeat(4)}{'☆'.repeat(1)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Acceleration:</span>
                        <div className="flex">
                          {'★'.repeat(3)}{'☆'.repeat(2)}
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-600 text-green-100 px-4 py-2 rounded-lg font-bold">
                      OWNED
                    </div>
                  </div>
                </div>

                {/* Sports Car */}
                <div className="bg-gradient-to-br from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <div className="text-center space-y-4">
                    <div className="text-4xl">🏎️</div>
                    <h3 className="text-xl font-bold text-white">Lightning Bolt</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between text-white/80">
                        <span>Speed:</span>
                        <div className="flex">
                          {'★'.repeat(5)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Handling:</span>
                        <div className="flex">
                          {'★'.repeat(4)}{'☆'.repeat(1)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Acceleration:</span>
                        <div className="flex">
                          {'★'.repeat(4)}{'☆'.repeat(1)}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-2xl font-bold text-yellow-400">500 GORB</div>
                      <button className="w-full bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white font-bold py-3 px-6 rounded-xl transition-all">
                        Buy Now
                      </button>
                    </div>
                  </div>
                </div>

                {/* Hypercar */}
                <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                  <div className="text-center space-y-4">
                    <div className="text-4xl">🏁</div>
                    <h3 className="text-xl font-bold text-white">Quantum Racer</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between text-white/80">
                        <span>Speed:</span>
                        <div className="flex">
                          {'★'.repeat(5)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Handling:</span>
                        <div className="flex">
                          {'★'.repeat(5)}
                        </div>
                      </div>
                      <div className="flex justify-between text-white/80">
                        <span>Acceleration:</span>
                        <div className="flex">
                          {'★'.repeat(5)}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-2xl font-bold text-yellow-400">1,500 GORB</div>
                      <button className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-xl transition-all">
                        Buy Now
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Upgrades Preview */}
              <div className="mt-16">
                <h3 className="text-3xl font-bold text-white text-center mb-8">🔧 Available Upgrades</h3>
                <div className="grid md:grid-cols-4 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div className="text-3xl mb-2">⚡</div>
                    <h4 className="text-white font-bold mb-2">Turbo Engine</h4>
                    <p className="text-white/70 text-sm mb-3">+20% Speed</p>
                    <div className="text-yellow-400 font-bold">200 GORB</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div className="text-3xl mb-2">🛞</div>
                    <h4 className="text-white font-bold mb-2">Racing Tires</h4>
                    <p className="text-white/70 text-sm mb-3">+25% Handling</p>
                    <div className="text-yellow-400 font-bold">150 GORB</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div className="text-3xl mb-2">🚀</div>
                    <h4 className="text-white font-bold mb-2">Nitrous System</h4>
                    <p className="text-white/70 text-sm mb-3">+30% Acceleration</p>
                    <div className="text-yellow-400 font-bold">300 GORB</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center">
                    <div className="text-3xl mb-2">🎨</div>
                    <h4 className="text-white font-bold mb-2">Custom Paint</h4>
                    <p className="text-white/70 text-sm mb-3">Unique Colors</p>
                    <div className="text-yellow-400 font-bold">100 GORB</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="text-center py-20">
            <h2 className="text-6xl font-bold text-white mb-8">ℹ️ About</h2>
            <div className="max-w-3xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                Gorbagana Grand Prix is a high-speed racing game built on the Gorbagana blockchain.
                Race against time, collect tokens, and compete for the highest scores!
              </p>
              <p className="text-white/60">
                Built for Gorbagana Testnet • Free to Play • Open Source
              </p>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
