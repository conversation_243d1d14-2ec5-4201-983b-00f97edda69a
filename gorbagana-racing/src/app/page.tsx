'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletButton } from '@/components/WalletButton';
import { SimpleRacingGame } from '@/components/SimpleRacingGame';

export default function Home() {
  const { connected } = useWallet();
  const [currentView, setCurrentView] = useState<'home' | 'game' | 'store' | 'garage'>('home');

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600">
      {/* Navigation Header */}
      <header className="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <span className="text-3xl">🏎️</span>
                <h1 className="text-2xl font-bold text-white">Gorbagana Grand Prix</h1>
              </div>
              <nav className="hidden md:flex space-x-6">
                <button
                  onClick={() => setCurrentView('home')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'home' ? 'text-white font-semibold' : ''
                  }`}
                >
                  Races
                </button>
                <button
                  onClick={() => setCurrentView('store')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'store' ? 'text-white font-semibold' : ''
                  }`}
                >
                  Leaderboard
                </button>
                <button
                  onClick={() => setCurrentView('garage')}
                  className={`text-white/80 hover:text-white transition-colors ${
                    currentView === 'garage' ? 'text-white font-semibold' : ''
                  }`}
                >
                  About
                </button>
              </nav>
            </div>
            <WalletButton />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {currentView === 'home' && (
          <div className="text-center py-20">
            {/* Hero Section */}
            <div className="space-y-8 mb-16">
              <h2 className="text-8xl font-bold bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text text-transparent">
                GORBAGANA RACING
              </h2>
              <p className="text-2xl text-white/90 max-w-3xl mx-auto">
                High-speed racing • Collect tokens • Beat your score!
              </p>
            </div>

            {/* Main Play Button */}
            <button
              onClick={() => setCurrentView('game')}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-8 px-20 rounded-3xl text-3xl transition-all transform hover:scale-105 shadow-2xl mb-16"
            >
              🏁 START RACING
            </button>

            {/* How to Play */}
            <div className="max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-8">How to Play</h3>
              <div className="grid md:grid-cols-2 gap-6 text-lg text-white/90">
                <div className="space-y-4">
                  <div>🎮 Use arrow keys to control your car</div>
                  <div>🚗 Avoid other cars</div>
                </div>
                <div className="space-y-4">
                  <div>🪙 Collect tokens for speed boosts</div>
                  <div>🏆 Survive as long as possible</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentView === 'game' && (
          <div className="flex justify-center py-8">
            <SimpleRacingGame />
          </div>
        )}

        {currentView === 'store' && (
          <div className="text-center py-20">
            <h2 className="text-6xl font-bold text-white mb-8">🏆 Leaderboard</h2>
            <div className="max-w-2xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-white/70 text-xl mb-8">Coming Soon!</p>
              <p className="text-white/60">See top racers and their best scores</p>
            </div>
          </div>
        )}

        {currentView === 'garage' && (
          <div className="text-center py-20">
            <h2 className="text-6xl font-bold text-white mb-8">ℹ️ About</h2>
            <div className="max-w-3xl mx-auto bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                Gorbagana Grand Prix is a high-speed racing game built on the Gorbagana blockchain.
                Race against time, collect tokens, and compete for the highest scores!
              </p>
              <p className="text-white/60">
                Built for Gorbagana Testnet • Free to Play • Open Source
              </p>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
