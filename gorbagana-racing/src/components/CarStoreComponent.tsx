'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { CarStore } from '@/lib/car-store';
import { Car, CarUpgrade, StoreItem } from '@/types/enhanced-game';

interface CarStoreComponentProps {
  carStore: CarStore;
  onBack: () => void;
}

export function CarStoreComponent({ carStore, onBack }: CarStoreComponentProps) {
  const { connected, publicKey } = useWallet();
  const { balance } = useBlockchain();
  
  const [selectedTab, setSelectedTab] = useState<'cars' | 'upgrades' | 'featured'>('featured');
  const [cars, setCars] = useState<Car[]>([]);
  const [upgrades, setUpgrades] = useState<CarUpgrade[]>([]);
  const [featuredItems, setFeaturedItems] = useState<StoreItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [purchaseStatus, setPurchaseStatus] = useState<string>('');

  // Load store data
  useEffect(() => {
    setCars(carStore.getAllCars());
    setUpgrades(carStore.getAllUpgrades());
    setFeaturedItems(carStore.getFeaturedItems());
  }, [carStore]);

  // Handle car purchase
  const handlePurchaseCar = async (carId: string) => {
    if (!connected || !publicKey) return;

    setIsLoading(true);
    setPurchaseStatus('');

    try {
      const transaction = await carStore.purchaseCar(publicKey.toString(), carId, balance);
      setPurchaseStatus(`Successfully purchased car! Transaction: ${transaction.id}`);
      
      // Refresh cars list
      setCars(carStore.getAllCars());
    } catch (error) {
      setPurchaseStatus(`Purchase failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle upgrade purchase
  const handlePurchaseUpgrade = async (upgradeId: string) => {
    if (!connected || !publicKey) return;

    setIsLoading(true);
    setPurchaseStatus('');

    try {
      const transaction = await carStore.purchaseUpgrade(publicKey.toString(), upgradeId, balance);
      setPurchaseStatus(`Successfully purchased upgrade! Transaction: ${transaction.id}`);
      
      // Refresh upgrades list
      setUpgrades(carStore.getAllUpgrades());
    } catch (error) {
      setPurchaseStatus(`Purchase failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'text-gray-400 border-gray-400';
      case 'Rare': return 'text-blue-400 border-blue-400';
      case 'Epic': return 'text-purple-400 border-purple-400';
      case 'Legendary': return 'text-yellow-400 border-yellow-400';
      default: return 'text-white border-white';
    }
  };

  const getRarityBg = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'bg-gray-600/20';
      case 'Rare': return 'bg-blue-600/20';
      case 'Epic': return 'bg-purple-600/20';
      case 'Legendary': return 'bg-yellow-600/20';
      default: return 'bg-white/5';
    }
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
        <h3 className="text-lg font-semibold text-white mb-4">Car Store</h3>
        <p className="text-white/70">Connect your wallet to access the store</p>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">🏪 Car Store</h2>
        <div className="flex items-center gap-4">
          <div className="text-white">
            Balance: <span className="text-yellow-400 font-bold">{balance.toFixed(0)} GORB</span>
          </div>
          <button
            onClick={onBack}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
          >
            Back
          </button>
        </div>
      </div>

      {/* Status Message */}
      {purchaseStatus && (
        <div className={`p-3 rounded-lg ${
          purchaseStatus.includes('Successfully') ? 'bg-green-600/20 text-green-200' : 'bg-red-600/20 text-red-200'
        }`}>
          {purchaseStatus}
        </div>
      )}

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        {[
          { id: 'featured', label: 'Featured', icon: '⭐' },
          { id: 'cars', label: 'Cars', icon: '🏎️' },
          { id: 'upgrades', label: 'Upgrades', icon: '⚙️' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setSelectedTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              selectedTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Featured Items */}
      {selectedTab === 'featured' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Featured Items</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {featuredItems.map((item) => {
              const car = item.type === 'car' ? carStore.getCar(item.id) : null;
              const upgrade = item.type === 'upgrade' ? carStore.getUpgrade(item.id) : null;
              const itemData = car || upgrade;
              
              if (!itemData) return null;

              return (
                <div key={item.id} className={`${getRarityBg(item.rarity)} border ${getRarityColor(item.rarity)} rounded-lg p-4`}>
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-white font-medium">{item.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded ${getRarityColor(item.rarity)}`}>
                      {item.rarity}
                    </span>
                  </div>
                  
                  <p className="text-white/70 text-sm mb-3">{item.description}</p>
                  
                  {car && (
                    <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                      <div>Speed: {car.stats.speed}</div>
                      <div>Accel: {car.stats.acceleration}</div>
                      <div>Handle: {car.stats.handling}</div>
                      <div>Durability: {car.stats.durability}</div>
                    </div>
                  )}
                  
                  {upgrade && upgrade.statBoosts && (
                    <div className="text-xs mb-3">
                      <div className="text-green-400">
                        {Object.entries(upgrade.statBoosts).map(([stat, boost]) => (
                          <div key={stat}>+{boost} {stat}</div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <span className="text-yellow-400 font-bold">{item.price} GORB</span>
                    <button
                      onClick={() => item.type === 'car' ? handlePurchaseCar(item.id) : handlePurchaseUpgrade(item.id)}
                      disabled={isLoading || itemData.isOwned || balance < item.price}
                      className={`px-3 py-1 rounded text-sm transition-colors ${
                        itemData.isOwned
                          ? 'bg-green-600 text-green-100 cursor-not-allowed'
                          : balance < item.price
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-purple-600 hover:bg-purple-700 text-white'
                      }`}
                    >
                      {itemData.isOwned ? 'Owned' : balance < item.price ? 'Too Expensive' : 'Buy'}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Cars Tab */}
      {selectedTab === 'cars' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Cars</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {cars.map((car) => (
              <div key={car.id} className={`${getRarityBg(car.rarity)} border ${getRarityColor(car.rarity)} rounded-lg p-4`}>
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-white font-medium">{car.name}</h4>
                  <span className={`text-xs px-2 py-1 rounded ${getRarityColor(car.rarity)}`}>
                    {car.rarity}
                  </span>
                </div>
                
                <p className="text-white/70 text-sm mb-3">{car.description}</p>
                
                <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                  <div>Speed: {car.stats.speed}</div>
                  <div>Accel: {car.stats.acceleration}</div>
                  <div>Handle: {car.stats.handling}</div>
                  <div>Durability: {car.stats.durability}</div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-yellow-400 font-bold">
                    {car.price === 0 ? 'Free' : `${car.price} GORB`}
                  </span>
                  <button
                    onClick={() => handlePurchaseCar(car.id)}
                    disabled={isLoading || car.isOwned || (car.price > 0 && balance < car.price)}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      car.isOwned
                        ? 'bg-green-600 text-green-100 cursor-not-allowed'
                        : car.price > 0 && balance < car.price
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-purple-600 hover:bg-purple-700 text-white'
                    }`}
                  >
                    {car.isOwned ? 'Owned' : car.price > 0 && balance < car.price ? 'Too Expensive' : 'Buy'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upgrades Tab */}
      {selectedTab === 'upgrades' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Upgrades</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {upgrades.map((upgrade) => (
              <div key={upgrade.id} className={`${getRarityBg(upgrade.rarity)} border ${getRarityColor(upgrade.rarity)} rounded-lg p-4`}>
                <div className="flex justify-between items-start mb-2">
                  <h4 className="text-white font-medium">{upgrade.name}</h4>
                  <span className={`text-xs px-2 py-1 rounded ${getRarityColor(upgrade.rarity)}`}>
                    {upgrade.rarity}
                  </span>
                </div>
                
                <p className="text-white/70 text-sm mb-3">{upgrade.description}</p>
                
                {upgrade.statBoosts && (
                  <div className="text-xs mb-3">
                    <div className="text-green-400">
                      {Object.entries(upgrade.statBoosts).map(([stat, boost]) => (
                        <div key={stat}>+{boost} {stat}</div>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex justify-between items-center">
                  <span className="text-yellow-400 font-bold">{upgrade.price} GORB</span>
                  <button
                    onClick={() => handlePurchaseUpgrade(upgrade.id)}
                    disabled={isLoading || upgrade.isOwned || balance < upgrade.price}
                    className={`px-3 py-1 rounded text-sm transition-colors ${
                      upgrade.isOwned
                        ? 'bg-green-600 text-green-100 cursor-not-allowed'
                        : balance < upgrade.price
                        ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : 'bg-purple-600 hover:bg-purple-700 text-white'
                    }`}
                  >
                    {upgrade.isOwned ? 'Owned' : balance < upgrade.price ? 'Too Expensive' : 'Buy'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
