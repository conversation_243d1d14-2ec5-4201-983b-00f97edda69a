'use client';

import React, { useState } from 'react';
import { useWallet } from './WalletProvider';

interface CarAsset {
  id: string;
  name: string;
  emoji: string;
  price: number;
  owned: boolean;
  stats: {
    speed: number;
    handling: number;
    acceleration: number;
  };
  description: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface Upgrade {
  id: string;
  name: string;
  emoji: string;
  price: number;
  owned: boolean;
  effect: string;
  boost: {
    speed?: number;
    handling?: number;
    acceleration?: number;
  };
  description: string;
}

interface PlayerAssets {
  gorbTokens: number;
  ownedCars: string[];
  ownedUpgrades: string[];
  equippedCar: string;
  equippedUpgrades: string[];
}

export function EnhancedCarStore() {
  const { connected } = useWallet();
  const [activeTab, setActiveTab] = useState<'cars' | 'upgrades' | 'skins'>('cars');
  
  const [playerAssets, setPlayerAssets] = useState<PlayerAssets>({
    gorbTokens: 1250,
    ownedCars: ['starter'],
    ownedUpgrades: [],
    equippedCar: 'starter',
    equippedUpgrades: [],
  });

  const cars: CarAsset[] = [
    {
      id: 'starter',
      name: 'City Cruiser',
      emoji: '🚗',
      price: 0,
      owned: true,
      stats: { speed: 3, handling: 4, acceleration: 3 },
      description: 'Reliable starter car with balanced performance',
      rarity: 'common',
    },
    {
      id: 'sports',
      name: 'Lightning Bolt',
      emoji: '🏎️',
      price: 500,
      owned: false,
      stats: { speed: 5, handling: 4, acceleration: 4 },
      description: 'High-speed sports car for thrill seekers',
      rarity: 'rare',
    },
    {
      id: 'hypercar',
      name: 'Quantum Racer',
      emoji: '🏁',
      price: 1500,
      owned: false,
      stats: { speed: 5, handling: 5, acceleration: 5 },
      description: 'Ultimate performance machine',
      rarity: 'epic',
    },
    {
      id: 'legendary',
      name: 'Gorbagana GT',
      emoji: '🚀',
      price: 5000,
      owned: false,
      stats: { speed: 6, handling: 6, acceleration: 6 },
      description: 'Legendary blockchain-powered supercar',
      rarity: 'legendary',
    },
  ];

  const upgrades: Upgrade[] = [
    {
      id: 'turbo',
      name: 'Turbo Engine',
      emoji: '⚡',
      price: 200,
      owned: false,
      effect: '+20% Speed',
      boost: { speed: 1 },
      description: 'Supercharged engine for maximum velocity',
    },
    {
      id: 'tires',
      name: 'Racing Tires',
      emoji: '🛞',
      price: 150,
      owned: false,
      effect: '+25% Handling',
      boost: { handling: 1 },
      description: 'High-grip tires for better cornering',
    },
    {
      id: 'nitrous',
      name: 'Nitrous System',
      emoji: '🚀',
      price: 300,
      owned: false,
      effect: '+30% Acceleration',
      boost: { acceleration: 2 },
      description: 'Nitrous oxide injection for instant boost',
    },
    {
      id: 'suspension',
      name: 'Sport Suspension',
      emoji: '🔧',
      price: 250,
      owned: false,
      effect: '+15% All Stats',
      boost: { speed: 1, handling: 1, acceleration: 1 },
      description: 'Advanced suspension system for overall performance',
    },
  ];

  const buyCar = (carId: string) => {
    const car = cars.find(c => c.id === carId);
    if (!car || car.owned || playerAssets.gorbTokens < car.price) return;

    setPlayerAssets(prev => ({
      ...prev,
      gorbTokens: prev.gorbTokens - car.price,
      ownedCars: [...prev.ownedCars, carId],
    }));

    // Update car ownership
    car.owned = true;
  };

  const buyUpgrade = (upgradeId: string) => {
    const upgrade = upgrades.find(u => u.id === upgradeId);
    if (!upgrade || upgrade.owned || playerAssets.gorbTokens < upgrade.price) return;

    setPlayerAssets(prev => ({
      ...prev,
      gorbTokens: prev.gorbTokens - upgrade.price,
      ownedUpgrades: [...prev.ownedUpgrades, upgradeId],
    }));

    // Update upgrade ownership
    upgrade.owned = true;
  };

  const equipCar = (carId: string) => {
    if (!playerAssets.ownedCars.includes(carId)) return;
    setPlayerAssets(prev => ({ ...prev, equippedCar: carId }));
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'border-gray-500';
      case 'rare': return 'border-blue-500';
      case 'epic': return 'border-purple-500';
      case 'legendary': return 'border-yellow-500';
      default: return 'border-gray-500';
    }
  };

  const renderStars = (rating: number) => {
    return '★'.repeat(rating) + '☆'.repeat(6 - rating);
  };

  return (
    <div className="space-y-6">
      {/* Player Stats */}
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-yellow-400">🪙 {playerAssets.gorbTokens}</div>
            <div className="text-gray-400 text-sm">GORB Tokens</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-400">{playerAssets.ownedCars.length}</div>
            <div className="text-gray-400 text-sm">Cars Owned</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-400">{playerAssets.ownedUpgrades.length}</div>
            <div className="text-gray-400 text-sm">Upgrades</div>
          </div>
        </div>
      </div>

      {/* Wallet Connection Notice */}
      {!connected && (
        <div className="bg-yellow-600/20 border border-yellow-600 rounded-lg p-4">
          <div className="text-yellow-400 font-bold mb-2">⚠️ Wallet Not Connected</div>
          <div className="text-yellow-200 text-sm">
            Connect your wallet to store assets on-chain and trade with other players.
          </div>
        </div>
      )}

      {/* Store Tabs */}
      <div className="flex bg-gray-800 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('cars')}
          className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
            activeTab === 'cars' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
          }`}
        >
          🏎️ Cars
        </button>
        <button
          onClick={() => setActiveTab('upgrades')}
          className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
            activeTab === 'upgrades' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
          }`}
        >
          ⚙️ Upgrades
        </button>
        <button
          onClick={() => setActiveTab('skins')}
          className={`flex-1 py-3 px-4 rounded-md font-medium transition-colors ${
            activeTab === 'skins' ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
          }`}
        >
          🎨 Skins
        </button>
      </div>

      {/* Cars Tab */}
      {activeTab === 'cars' && (
        <div className="grid md:grid-cols-2 gap-4">
          {cars.map(car => (
            <div
              key={car.id}
              className={`bg-gray-800 rounded-lg p-4 border-2 ${getRarityColor(car.rarity)} ${
                playerAssets.equippedCar === car.id ? 'ring-2 ring-green-500' : ''
              }`}
            >
              <div className="text-center space-y-3">
                <div className="text-4xl">{car.emoji}</div>
                <h3 className="text-xl font-bold text-white">{car.name}</h3>
                <p className="text-gray-400 text-sm">{car.description}</p>
                
                {/* Stats */}
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between text-gray-300">
                    <span>Speed:</span>
                    <span className="text-yellow-400">{renderStars(car.stats.speed)}</span>
                  </div>
                  <div className="flex justify-between text-gray-300">
                    <span>Handling:</span>
                    <span className="text-yellow-400">{renderStars(car.stats.handling)}</span>
                  </div>
                  <div className="flex justify-between text-gray-300">
                    <span>Acceleration:</span>
                    <span className="text-yellow-400">{renderStars(car.stats.acceleration)}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  {car.owned ? (
                    <div className="space-y-2">
                      <div className="bg-green-600 text-white px-4 py-2 rounded font-bold">
                        OWNED
                      </div>
                      {playerAssets.equippedCar === car.id ? (
                        <div className="bg-blue-600 text-white px-4 py-2 rounded font-bold">
                          EQUIPPED
                        </div>
                      ) : (
                        <button
                          onClick={() => equipCar(car.id)}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded font-bold transition-colors"
                        >
                          Equip
                        </button>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="text-2xl font-bold text-yellow-400">
                        {car.price > 0 ? `${car.price} GORB` : 'FREE'}
                      </div>
                      <button
                        onClick={() => buyCar(car.id)}
                        disabled={playerAssets.gorbTokens < car.price}
                        className={`w-full px-4 py-2 rounded font-bold transition-colors ${
                          playerAssets.gorbTokens >= car.price
                            ? 'bg-green-600 hover:bg-green-700 text-white'
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        {playerAssets.gorbTokens >= car.price ? 'Buy Now' : 'Insufficient Funds'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upgrades Tab */}
      {activeTab === 'upgrades' && (
        <div className="grid md:grid-cols-2 gap-4">
          {upgrades.map(upgrade => (
            <div key={upgrade.id} className="bg-gray-800 rounded-lg p-4">
              <div className="text-center space-y-3">
                <div className="text-3xl">{upgrade.emoji}</div>
                <h3 className="text-lg font-bold text-white">{upgrade.name}</h3>
                <p className="text-gray-400 text-sm">{upgrade.description}</p>
                <div className="text-green-400 font-bold">{upgrade.effect}</div>
                
                {upgrade.owned ? (
                  <div className="bg-green-600 text-white px-4 py-2 rounded font-bold">
                    OWNED
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="text-xl font-bold text-yellow-400">{upgrade.price} GORB</div>
                    <button
                      onClick={() => buyUpgrade(upgrade.id)}
                      disabled={playerAssets.gorbTokens < upgrade.price}
                      className={`w-full px-4 py-2 rounded font-bold transition-colors ${
                        playerAssets.gorbTokens >= upgrade.price
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {playerAssets.gorbTokens >= upgrade.price ? 'Buy' : 'Insufficient Funds'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Skins Tab */}
      {activeTab === 'skins' && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">🎨</div>
          <h3 className="text-xl font-bold text-white mb-4">Custom Skins</h3>
          <p className="text-gray-400 mb-6">
            Customize your car's appearance with unique skins and visual effects.
          </p>
          <div className="bg-gray-800 rounded-lg p-6">
            <p className="text-gray-300">Coming Soon!</p>
            <p className="text-gray-500 text-sm mt-2">
              Custom paint jobs, decals, and special effects for your cars.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
