'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useEnhancedMultiplayer } from '@/hooks/useEnhancedMultiplayer';
import { RaceStatus } from '@/types/game';

export function EnhancedMultiplayerLobby() {
  const { connected: walletConnected } = useWallet();
  const {
    connected,
    connecting,
    error,
    currentSession,
    availableSessions,
    currentPlayer,
    allPlayers,
    raceStatus,
    raceProgress,
    leaderboard,
    latency,
    fps,
    connect,
    createSession,
    joinSession,
    leaveSession,
    setReady,
    refreshSessions,
  } = useEnhancedMultiplayer();

  const [showCreateSession, setShowCreateSession] = useState(false);
  const [sessionName, setSessionName] = useState('');

  const handleCreateSession = () => {
    if (sessionName.trim()) {
      createSession({
        name: sessionName.trim(),
        maxPlayers: 8,
        entryFee: 50,
      });
      setSessionName('');
      setShowCreateSession(false);
    }
  };

  const handleJoinSession = (sessionId?: string) => {
    joinSession(sessionId);
  };

  if (!walletConnected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Enhanced Multiplayer Racing</h3>
        <p className="text-white/70">Connect your wallet to join multiplayer races</p>
      </div>
    );
  }

  if (connecting) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Enhanced Multiplayer Racing</h3>
        <div className="flex items-center gap-2">
          <div className="animate-spin w-4 h-4 border-2 border-white/30 border-t-white rounded-full"></div>
          <p className="text-white/70">Connecting to enhanced multiplayer server...</p>
        </div>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Enhanced Multiplayer Racing</h3>
        <div className="space-y-4">
          <p className="text-white/70">Connection failed. Click to retry.</p>
          <button
            onClick={connect}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
          >
            Reconnect
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Enhanced Multiplayer Racing</h3>
        <div className="flex items-center gap-4">
          {/* Performance Indicators */}
          <div className="flex items-center gap-2 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-white/70">{fps} FPS</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span className="text-white/70">{latency}ms</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-white/70">Connected</span>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      {/* Current Session */}
      {currentSession && (
        <div className="bg-white/5 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium">Current Session</h4>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              raceStatus === RaceStatus.WAITING ? 'bg-yellow-600 text-yellow-100' :
              raceStatus === RaceStatus.STARTING ? 'bg-orange-600 text-orange-100' :
              raceStatus === RaceStatus.ACTIVE ? 'bg-green-600 text-green-100' :
              'bg-blue-600 text-blue-100'
            }`}>
              {raceStatus}
            </span>
          </div>

          {/* Session Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white/70">Session ID:</span>
              <span className="text-white ml-2">{currentSession.id.split('-')[1]}</span>
            </div>
            <div>
              <span className="text-white/70">Players:</span>
              <span className="text-white ml-2">{allPlayers.length}/8</span>
            </div>
          </div>

          {/* Race Progress */}
          {raceStatus === RaceStatus.ACTIVE && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Race Progress</span>
                <span className="text-white text-sm">{raceProgress.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(raceProgress, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Players List */}
          <div className="space-y-2">
            <h5 className="text-white font-medium text-sm">Players:</h5>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {allPlayers.map((player, index) => (
                <div key={player.id} className="flex items-center justify-between bg-white/5 rounded p-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🏎️</span>
                    <span className="text-white text-sm">{player.name}</span>
                    {player.id === currentPlayer?.id && (
                      <span className="text-yellow-400 text-xs">(You)</span>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {raceStatus === RaceStatus.ACTIVE && (
                      <span className="text-xs text-white/70">
                        #{index + 1} • {player.position.toFixed(0)}m
                      </span>
                    )}
                    {player.isReady && raceStatus === RaceStatus.WAITING && (
                      <span className="text-xs bg-green-600 text-green-100 px-2 py-1 rounded">
                        Ready
                      </span>
                    )}
                    <div className={`w-2 h-2 rounded-full ${
                      player.isConnected ? 'bg-green-400' : 'bg-red-400'
                    }`}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            {raceStatus === RaceStatus.WAITING && !currentPlayer?.isReady && (
              <button
                onClick={() => setReady(true)}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
              >
                Ready to Race
              </button>
            )}
            
            {raceStatus === RaceStatus.WAITING && currentPlayer?.isReady && (
              <button
                onClick={() => setReady(false)}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded transition-colors"
              >
                Not Ready
              </button>
            )}
            
            <button
              onClick={leaveSession}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
            >
              Leave
            </button>
          </div>
        </div>
      )}

      {/* Available Sessions */}
      {!currentSession && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium">Available Sessions</h4>
            <div className="flex gap-2">
              <button
                onClick={() => setShowCreateSession(!showCreateSession)}
                className="text-sm bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded transition-colors"
              >
                Create
              </button>
              <button
                onClick={refreshSessions}
                className="text-white/70 hover:text-white transition-colors"
              >
                🔄
              </button>
            </div>
          </div>

          {/* Create Session Form */}
          {showCreateSession && (
            <div className="bg-white/5 border border-white/20 rounded-lg p-4">
              <div className="space-y-3">
                <input
                  type="text"
                  value={sessionName}
                  onChange={(e) => setSessionName(e.target.value)}
                  placeholder="Session name"
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded text-white placeholder-white/50 focus:outline-none focus:border-purple-400"
                  maxLength={30}
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleCreateSession}
                    disabled={!sessionName.trim()}
                    className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
                  >
                    Create Session
                  </button>
                  <button
                    onClick={() => setShowCreateSession(false)}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Sessions List */}
          {availableSessions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-white/70 mb-4">No sessions available</p>
              <button
                onClick={() => handleJoinSession()}
                className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
              >
                Create & Join New Session
              </button>
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {availableSessions.map((session) => (
                <div key={session.id} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="text-white font-medium">Session {session.id.split('-')[1]}</h5>
                      <p className="text-sm text-white/70">
                        {session.players.size}/8 players • Status: {session.status}
                      </p>
                    </div>
                    <button
                      onClick={() => handleJoinSession(session.id)}
                      disabled={session.players.size >= 8 || session.status !== RaceStatus.WAITING}
                      className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
                        session.players.size >= 8 || session.status !== RaceStatus.WAITING
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-purple-600 hover:bg-purple-700 text-white'
                      }`}
                    >
                      {session.players.size >= 8 ? 'Full' : 
                       session.status !== RaceStatus.WAITING ? 'In Progress' : 'Join'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="text-xs text-white/50 border-t border-white/10 pt-3">
        <p>Enhanced multiplayer system with real-time synchronization and performance monitoring</p>
      </div>
    </div>
  );
}
