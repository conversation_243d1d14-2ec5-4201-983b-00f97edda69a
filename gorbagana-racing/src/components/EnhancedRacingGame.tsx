'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { EnhancedGameEngine } from '@/lib/enhanced-game-engine';
import { CarStore } from '@/lib/car-store';
import { GameMode, GameSession, GamePlayer, Car, CarUpgrade } from '@/types/enhanced-game';

interface EnhancedRacingGameProps {
  mode: 'menu' | 'endless' | 'pvp' | 'store' | 'garage';
  onModeChange: (mode: string) => void;
}

export function EnhancedRacingGame({ mode, onModeChange }: EnhancedRacingGameProps) {
  const { connected, publicKey } = useWallet();
  const { balance } = useBlockchain();
  
  const [gameEngine] = useState(() => new EnhancedGameEngine());
  const [carStore] = useState(() => new CarStore());
  const [currentSession, setCurrentSession] = useState<GameSession | null>(null);
  const [playerCar, setPlayerCar] = useState<Car | null>(null);
  const [playerUpgrades, setPlayerUpgrades] = useState<CarUpgrade[]>([]);
  const [gameState, setGameState] = useState<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const keysPressed = useRef<Set<string>>(new Set());

  // Initialize player's car and upgrades
  useEffect(() => {
    if (connected && publicKey) {
      const inventory = carStore.getPlayerInventory(publicKey.toString());
      const equippedCar = inventory.cars.find(car => car.isEquipped);
      const equippedUpgrades = inventory.upgrades.filter(upgrade => upgrade.isEquipped);
      
      setPlayerCar(equippedCar || null);
      setPlayerUpgrades(equippedUpgrades);
    }
  }, [connected, publicKey, carStore]);

  // Set up game engine event listeners
  useEffect(() => {
    const handleGameUpdate = (data: any) => {
      setGameState(data.session.gameState);
    };

    const handleGameEnded = (data: any) => {
      setIsPlaying(false);
      setCurrentSession(null);
      // Handle rewards, show results, etc.
    };

    gameEngine.on('gameUpdate', handleGameUpdate);
    gameEngine.on('gameEnded', handleGameEnded);

    return () => {
      gameEngine.off('gameUpdate', handleGameUpdate);
      gameEngine.off('gameEnded', handleGameEnded);
    };
  }, [gameEngine]);

  // Keyboard controls
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysPressed.current.add(e.key.toLowerCase());
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysPressed.current.delete(e.key.toLowerCase());
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Game input processing
  useEffect(() => {
    if (!isPlaying || !currentSession || !publicKey) return;

    const processInput = () => {
      if (keysPressed.current.has('a') || keysPressed.current.has('arrowleft')) {
        gameEngine.movePlayer(publicKey.toString(), 'left');
      }
      if (keysPressed.current.has('d') || keysPressed.current.has('arrowright')) {
        gameEngine.movePlayer(publicKey.toString(), 'right');
      }
      if (keysPressed.current.has('w') || keysPressed.current.has('arrowup')) {
        gameEngine.movePlayer(publicKey.toString(), 'up');
      }
      if (keysPressed.current.has('s') || keysPressed.current.has('arrowdown')) {
        gameEngine.movePlayer(publicKey.toString(), 'down');
      }
    };

    const inputInterval = setInterval(processInput, 16); // ~60 FPS
    return () => clearInterval(inputInterval);
  }, [isPlaying, currentSession, publicKey, gameEngine]);

  // Start endless mode
  const startEndlessMode = () => {
    if (!connected || !publicKey || !playerCar) return;

    const gameMode: GameMode = {
      id: 'endless',
      name: 'Endless Highway',
      description: 'Survive as long as possible on the endless highway',
      type: 'endless',
      rewards: {
        baseTokens: 10,
        distanceMultiplier: 0.1,
        timeMultiplier: 0.05,
        collectibleBonus: 5,
        streakBonus: 0,
      },
      settings: {
        difficulty: 'Medium',
        trackType: 'highway',
        weather: 'clear',
        timeOfDay: 'day',
        trafficDensity: 50,
        tokenSpawnRate: 0.2,
        obstacleFrequency: 30,
      },
    };

    const player: GamePlayer = {
      id: publicKey.toString(),
      name: `Player ${publicKey.toString().slice(0, 8)}`,
      car: playerCar,
      upgrades: playerUpgrades,
      position: { x: 150, y: 0, z: 0, speed: 100, direction: 0, lane: 1 },
      stats: {
        distance: 0,
        tokensCollected: 0,
        obstaclesHit: 0,
        carsOvertaken: 0,
        timeAlive: 0,
        maxSpeed: 0,
        score: 0,
      },
      isReady: true,
      isConnected: true,
    };

    const session = gameEngine.initializeSession(gameMode, [player]);
    setCurrentSession(session);
    setIsPlaying(true);
    gameEngine.startGame();
  };

  // Start PvP mode
  const startPvPMode = () => {
    // This would typically involve matchmaking
    // For now, we'll create a simple AI opponent
    if (!connected || !publicKey || !playerCar) return;

    const gameMode: GameMode = {
      id: 'pvp',
      name: 'Head-to-Head Race',
      description: 'Race against another player',
      type: 'pvp',
      entryFee: 50,
      rewards: {
        baseTokens: 0,
        winnerTokens: 150,
        distanceMultiplier: 0,
        timeMultiplier: 0,
        collectibleBonus: 2,
        streakBonus: 10,
      },
      settings: {
        duration: 120, // 2 minutes
        maxPlayers: 2,
        minPlayers: 2,
        difficulty: 'Hard',
        trackType: 'city',
        weather: 'clear',
        timeOfDay: 'day',
        trafficDensity: 30,
        tokenSpawnRate: 0.1,
        obstacleFrequency: 20,
      },
    };

    const player: GamePlayer = {
      id: publicKey.toString(),
      name: `Player ${publicKey.toString().slice(0, 8)}`,
      car: playerCar,
      upgrades: playerUpgrades,
      position: { x: 100, y: 0, z: 0, speed: 100, direction: 0, lane: 0 },
      stats: {
        distance: 0,
        tokensCollected: 0,
        obstaclesHit: 0,
        carsOvertaken: 0,
        timeAlive: 0,
        maxSpeed: 0,
        score: 0,
      },
      isReady: true,
      isConnected: true,
    };

    // Create AI opponent
    const aiCar = carStore.getCar('sports_coupe') || playerCar;
    const aiPlayer: GamePlayer = {
      id: 'ai_opponent',
      name: 'Speed Demon AI',
      car: aiCar,
      upgrades: [],
      position: { x: 200, y: 0, z: 0, speed: 100, direction: 0, lane: 2 },
      stats: {
        distance: 0,
        tokensCollected: 0,
        obstaclesHit: 0,
        carsOvertaken: 0,
        timeAlive: 0,
        maxSpeed: 0,
        score: 0,
      },
      isReady: true,
      isConnected: true,
    };

    const session = gameEngine.initializeSession(gameMode, [player, aiPlayer]);
    setCurrentSession(session);
    setIsPlaying(true);
    gameEngine.startGame();
  };

  // Render game canvas
  const renderGame = () => {
    const canvas = canvasRef.current;
    if (!canvas || !gameState || !currentSession) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.fillStyle = '#2a5934'; // Dark green road
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw road lanes
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.setLineDash([20, 20]);
    for (let i = 1; i < 3; i++) {
      ctx.beginPath();
      ctx.moveTo(i * 100, 0);
      ctx.lineTo(i * 100, canvas.height);
      ctx.stroke();
    }

    // Draw traffic cars
    ctx.fillStyle = '#ff4444';
    gameState.traffic.forEach((car: any) => {
      ctx.fillRect(car.x - 20, car.y - 30, 40, 60);
    });

    // Draw obstacles
    ctx.fillStyle = '#8b4513';
    gameState.obstacles.forEach((obstacle: any) => {
      ctx.fillRect(obstacle.x - obstacle.size/2, obstacle.y - obstacle.size/2, obstacle.size, obstacle.size);
    });

    // Draw collectibles
    ctx.fillStyle = '#ffd700';
    gameState.collectibles.forEach((collectible: any) => {
      ctx.beginPath();
      ctx.arc(collectible.x, collectible.y, 15, 0, 2 * Math.PI);
      ctx.fill();
    });

    // Draw players
    currentSession.players.forEach((player, index) => {
      ctx.fillStyle = index === 0 ? '#4444ff' : '#ff44ff';
      ctx.fillRect(player.position.x - 20, player.position.y - 30, 40, 60);
      
      // Draw player name
      ctx.fillStyle = '#ffffff';
      ctx.font = '12px Arial';
      ctx.fillText(player.name, player.position.x - 30, player.position.y - 40);
    });
  };

  // Render game every frame
  useEffect(() => {
    if (isPlaying) {
      const renderLoop = () => {
        renderGame();
        requestAnimationFrame(renderLoop);
      };
      renderLoop();
    }
  }, [isPlaying, gameState, currentSession]);

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
        <h3 className="text-lg font-semibold text-white mb-4">Enhanced Racing Game</h3>
        <p className="text-white/70">Connect your wallet to start racing!</p>
      </div>
    );
  }

  if (mode === 'menu') {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
        <h2 className="text-2xl font-bold text-white text-center">🏎️ Gorbagana Grand Prix</h2>
        
        <div className="grid md:grid-cols-2 gap-4">
          {/* Endless Mode */}
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">🛣️ Endless Highway</h3>
            <p className="text-white/70 text-sm mb-4">
              Survive as long as possible on the endless highway. Collect tokens, avoid traffic, and beat your high score!
            </p>
            <button
              onClick={startEndlessMode}
              disabled={!playerCar}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
            >
              Start Endless Mode
            </button>
          </div>

          {/* PvP Mode */}
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">🏁 PvP Racing</h3>
            <p className="text-white/70 text-sm mb-4">
              Race against other players. Entry fee: 50 GORB. Winner takes 150 GORB!
            </p>
            <button
              onClick={startPvPMode}
              disabled={!playerCar || balance < 50}
              className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
            >
              {balance < 50 ? 'Insufficient Balance' : 'Start PvP Race'}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex gap-4 justify-center">
          <button
            onClick={() => onModeChange('store')}
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-6 rounded transition-colors"
          >
            🏪 Store
          </button>
          <button
            onClick={() => onModeChange('garage')}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded transition-colors"
          >
            🏠 Garage
          </button>
        </div>

        {/* Player Info */}
        <div className="bg-white/5 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-white">Balance: {balance.toFixed(0)} GORB</p>
              <p className="text-white/70">Car: {playerCar?.name || 'No car equipped'}</p>
            </div>
            <div className="text-right">
              <p className="text-white">Upgrades: {playerUpgrades.length}</p>
              <p className="text-white/70">Total Cars: {carStore.getPlayerInventory(publicKey?.toString() || '').cars.length}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isPlaying && currentSession) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">{currentSession.mode.name}</h3>
          <button
            onClick={() => {
              setIsPlaying(false);
              setCurrentSession(null);
              gameEngine.destroy();
            }}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
          >
            Exit Race
          </button>
        </div>

        {/* Game Canvas */}
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={300}
            height={600}
            className="border border-white/20 rounded-lg mx-auto block"
            style={{ background: '#1a4a2a' }}
          />
          
          {/* Game UI Overlay */}
          <div className="absolute top-4 left-4 text-white">
            <p>Distance: {currentSession.players[0]?.stats.distance.toFixed(0)}m</p>
            <p>Tokens: {currentSession.players[0]?.stats.tokensCollected}</p>
            <p>Score: {currentSession.players[0]?.stats.score}</p>
          </div>
        </div>

        {/* Controls */}
        <div className="mt-4 text-center text-white/70 text-sm">
          <p>Use WASD or Arrow Keys to control your car</p>
          <p>Collect golden tokens • Avoid red cars and obstacles</p>
        </div>
      </div>
    );
  }

  // Placeholder for other modes
  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
      <h3 className="text-lg font-semibold text-white mb-4">Game Mode: {mode}</h3>
      <p className="text-white/70">This mode is under development</p>
      <button
        onClick={() => onModeChange('menu')}
        className="mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
      >
        Back to Menu
      </button>
    </div>
  );
}
