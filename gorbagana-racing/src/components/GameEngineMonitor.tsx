'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useMultiplayer } from '@/hooks/useMultiplayer';

interface PerformanceMetrics {
  fps: number;
  latency: number;
  playersConnected: number;
  raceTime: number;
  averageSpeed: number;
  collisions: number;
  powerUpsUsed: number;
}

export function GameEngineMonitor() {
  const { publicKey } = useWallet();
  const { currentRace, players, isInRace, raceStatus } = useMultiplayer();
  
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    latency: 0,
    playersConnected: 0,
    raceTime: 0,
    averageSpeed: 0,
    collisions: 0,
    powerUpsUsed: 0,
  });

  const [showDetails, setShowDetails] = useState(false);
  const [frameCount, setFrameCount] = useState(0);
  const [lastFrameTime, setLastFrameTime] = useState(Date.now());

  // Calculate FPS and update metrics
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const deltaTime = now - lastFrameTime;
      const fps = Math.round(1000 / deltaTime);
      
      setFrameCount(prev => prev + 1);
      setLastFrameTime(now);

      if (currentRace && isInRace) {
        const raceStartTime = currentRace.startTime?.getTime() || now;
        const raceTime = Math.max(0, now - raceStartTime);
        
        const currentPlayer = players.find(p => p.id === publicKey?.toString());
        const averageSpeed = currentPlayer ? currentPlayer.speed : 0;
        
        setMetrics(prev => ({
          ...prev,
          fps: Math.min(fps, 60), // Cap at 60 FPS
          playersConnected: players.length,
          raceTime: raceTime / 1000, // Convert to seconds
          averageSpeed,
          latency: Math.random() * 50 + 10, // Simulated latency
        }));
      }
    }, 100);

    return () => clearInterval(interval);
  }, [currentRace, isInRace, players, publicKey, lastFrameTime]);

  if (!isInRace) {
    return (
      <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4">
        <div className="flex items-center justify-between">
          <h4 className="text-white font-medium text-sm">Game Engine</h4>
          <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
        </div>
        <p className="text-white/60 text-xs mt-2">Not in race</p>
      </div>
    );
  }

  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-white font-medium text-sm">Game Engine Monitor</h4>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-white/60 hover:text-white text-xs"
          >
            {showDetails ? 'Hide' : 'Show'} Details
          </button>
        </div>
      </div>

      {/* Basic Metrics */}
      <div className="grid grid-cols-3 gap-3 text-center">
        <div>
          <div className="text-lg font-bold text-green-400">{metrics.fps}</div>
          <div className="text-xs text-white/60">FPS</div>
        </div>
        <div>
          <div className="text-lg font-bold text-blue-400">{metrics.latency.toFixed(0)}ms</div>
          <div className="text-xs text-white/60">Latency</div>
        </div>
        <div>
          <div className="text-lg font-bold text-yellow-400">{metrics.playersConnected}</div>
          <div className="text-xs text-white/60">Players</div>
        </div>
      </div>

      {/* Detailed Metrics */}
      {showDetails && (
        <div className="space-y-2 border-t border-white/10 pt-3">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-white/60">Race Time:</span>
              <span className="text-white">{metrics.raceTime.toFixed(1)}s</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Avg Speed:</span>
              <span className="text-white">{metrics.averageSpeed.toFixed(1)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Collisions:</span>
              <span className="text-white">{metrics.collisions}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-white/60">Power-ups:</span>
              <span className="text-white">{metrics.powerUpsUsed}</span>
            </div>
          </div>

          {/* Physics State */}
          {currentRace && (
            <div className="bg-white/5 rounded p-2">
              <div className="text-xs text-white/60 mb-1">Physics Engine:</div>
              <div className="grid grid-cols-2 gap-1 text-xs">
                <div>✓ Collision Detection</div>
                <div>✓ Real-time Physics</div>
                <div>✓ Power-up Effects</div>
                <div>✓ Track Features</div>
              </div>
            </div>
          )}

          {/* Performance Indicators */}
          <div className="flex items-center justify-between text-xs">
            <span className="text-white/60">Engine Status:</span>
            <div className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${
                metrics.fps > 45 ? 'bg-green-400' : 
                metrics.fps > 30 ? 'bg-yellow-400' : 'bg-red-400'
              }`}></div>
              <span className="text-white">
                {metrics.fps > 45 ? 'Optimal' : 
                 metrics.fps > 30 ? 'Good' : 'Poor'}
              </span>
            </div>
          </div>

          {/* Track Information */}
          {currentRace?.track && (
            <div className="bg-white/5 rounded p-2">
              <div className="text-xs text-white/60 mb-1">Track Info:</div>
              <div className="grid grid-cols-2 gap-1 text-xs">
                <div>Length: {currentRace.track.length}m</div>
                <div>Obstacles: {currentRace.track.obstacles?.length || 0}</div>
                <div>Shortcuts: {currentRace.track.shortcuts?.length || 0}</div>
                <div>Difficulty: {currentRace.track.difficulty}/5</div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Status Indicator */}
      <div className="flex items-center justify-between text-xs border-t border-white/10 pt-2">
        <span className="text-white/60">Status:</span>
        <span className={`font-medium ${
          raceStatus === 'racing' ? 'text-green-400' :
          raceStatus === 'starting' ? 'text-yellow-400' :
          'text-blue-400'
        }`}>
          {raceStatus?.toUpperCase()}
        </span>
      </div>
    </div>
  );
}
