'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { EnhancedRacingGame } from '@/components/EnhancedRacingGame';
import { CarStoreComponent } from '@/components/CarStoreComponent';
import { GarageComponent } from '@/components/GarageComponent';
import { CarStore } from '@/lib/car-store';

export function GameHub() {
  const { connected } = useWallet();
  const [currentMode, setCurrentMode] = useState<'menu' | 'endless' | 'pvp' | 'store' | 'garage'>('menu');
  const [carStore] = useState(() => new CarStore());

  const handleModeChange = (mode: string) => {
    setCurrentMode(mode as any);
  };

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center max-w-md">
          <h1 className="text-3xl font-bold text-white mb-4">🏎️ Gorbagana Grand Prix</h1>
          <p className="text-white/70 mb-6">
            The ultimate blockchain racing experience with NFT cars, upgrades, and token rewards!
          </p>
          <div className="space-y-4">
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">🛣️ Endless Highway Mode</h3>
              <p className="text-white/60 text-sm">
                Survive the endless highway, collect tokens, and avoid traffic!
              </p>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">🏁 PvP Racing</h3>
              <p className="text-white/60 text-sm">
                Race against other players with token stakes and prizes!
              </p>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-medium mb-2">🏪 Car Store & Garage</h3>
              <p className="text-white/60 text-sm">
                Buy cars and upgrades with GORB tokens, customize your ride!
              </p>
            </div>
          </div>
          <p className="text-white/70 mt-6">Connect your wallet to start racing!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Navigation Bar */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">🏎️ Gorbagana Grand Prix</h1>
            
            {/* Navigation Buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => handleModeChange('menu')}
                className={`px-4 py-2 rounded transition-colors ${
                  currentMode === 'menu'
                    ? 'bg-purple-600 text-white'
                    : 'bg-white/10 text-white/70 hover:text-white hover:bg-white/20'
                }`}
              >
                🏠 Home
              </button>
              <button
                onClick={() => handleModeChange('store')}
                className={`px-4 py-2 rounded transition-colors ${
                  currentMode === 'store'
                    ? 'bg-purple-600 text-white'
                    : 'bg-white/10 text-white/70 hover:text-white hover:bg-white/20'
                }`}
              >
                🏪 Store
              </button>
              <button
                onClick={() => handleModeChange('garage')}
                className={`px-4 py-2 rounded transition-colors ${
                  currentMode === 'garage'
                    ? 'bg-purple-600 text-white'
                    : 'bg-white/10 text-white/70 hover:text-white hover:bg-white/20'
                }`}
              >
                🏠 Garage
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        {currentMode === 'store' && (
          <CarStoreComponent 
            carStore={carStore} 
            onBack={() => handleModeChange('menu')} 
          />
        )}
        
        {currentMode === 'garage' && (
          <GarageComponent 
            carStore={carStore} 
            onBack={() => handleModeChange('menu')} 
          />
        )}
        
        {(currentMode === 'menu' || currentMode === 'endless' || currentMode === 'pvp') && (
          <EnhancedRacingGame 
            mode={currentMode} 
            onModeChange={handleModeChange} 
          />
        )}

        {/* Footer */}
        <div className="mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-4">
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <h3 className="text-white font-medium mb-2">🎮 Game Modes</h3>
              <p className="text-white/60 text-sm">
                Endless highway survival and competitive PvP racing with real token stakes
              </p>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">🏎️ NFT Cars & Upgrades</h3>
              <p className="text-white/60 text-sm">
                Collect and upgrade cars with blockchain-verified ownership and stats
              </p>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">💰 Token Economy</h3>
              <p className="text-white/60 text-sm">
                Earn GORB tokens through racing, spend them on cars and upgrades
              </p>
            </div>
          </div>
          
          <div className="mt-6 pt-4 border-t border-white/20 text-center">
            <p className="text-white/50 text-sm">
              Built for Gorbagana Testnet • Powered by Solana • Racing reimagined on blockchain
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
