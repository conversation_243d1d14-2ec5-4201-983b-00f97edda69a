'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { CarStore } from '@/lib/car-store';
import { Car, CarUpgrade, PlayerInventory } from '@/types/enhanced-game';

interface GarageComponentProps {
  carStore: CarStore;
  onBack: () => void;
}

export function GarageComponent({ carStore, onBack }: GarageComponentProps) {
  const { connected, publicKey } = useWallet();
  
  const [selectedTab, setSelectedTab] = useState<'cars' | 'upgrades'>('cars');
  const [inventory, setInventory] = useState<PlayerInventory | null>(null);
  const [selectedCar, setSelectedCar] = useState<Car | null>(null);

  // Load player inventory
  useEffect(() => {
    if (connected && publicKey) {
      const playerInventory = carStore.getPlayerInventory(publicKey.toString());
      setInventory(playerInventory);
      
      const equippedCar = playerInventory.cars.find(car => car.isEquipped);
      setSelectedCar(equippedCar || null);
    }
  }, [connected, publicKey, carStore]);

  // Handle car equipment
  const handleEquipCar = (carId: string) => {
    if (!connected || !publicKey) return;

    const success = carStore.equipCar(publicKey.toString(), carId);
    if (success) {
      // Refresh inventory
      const updatedInventory = carStore.getPlayerInventory(publicKey.toString());
      setInventory(updatedInventory);
      
      const newEquippedCar = updatedInventory.cars.find(car => car.isEquipped);
      setSelectedCar(newEquippedCar || null);
    }
  };

  // Handle upgrade equipment
  const handleToggleUpgrade = (upgradeId: string, isEquipped: boolean) => {
    if (!connected || !publicKey) return;

    const success = isEquipped 
      ? carStore.unequipUpgrade(publicKey.toString(), upgradeId)
      : carStore.equipUpgrade(publicKey.toString(), upgradeId);
    
    if (success) {
      // Refresh inventory
      const updatedInventory = carStore.getPlayerInventory(publicKey.toString());
      setInventory(updatedInventory);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'text-gray-400 border-gray-400';
      case 'Rare': return 'text-blue-400 border-blue-400';
      case 'Epic': return 'text-purple-400 border-purple-400';
      case 'Legendary': return 'text-yellow-400 border-yellow-400';
      default: return 'text-white border-white';
    }
  };

  const getRarityBg = (rarity: string) => {
    switch (rarity) {
      case 'Common': return 'bg-gray-600/20';
      case 'Rare': return 'bg-blue-600/20';
      case 'Epic': return 'bg-purple-600/20';
      case 'Legendary': return 'bg-yellow-600/20';
      default: return 'bg-white/5';
    }
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
        <h3 className="text-lg font-semibold text-white mb-4">Garage</h3>
        <p className="text-white/70">Connect your wallet to access your garage</p>
      </div>
    );
  }

  if (!inventory) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
        <h3 className="text-lg font-semibold text-white mb-4">Loading Garage...</h3>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">🏠 Garage</h2>
        <button
          onClick={onBack}
          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
        >
          Back
        </button>
      </div>

      {/* Current Setup */}
      <div className="bg-white/5 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-3">Current Setup</h3>
        
        {selectedCar ? (
          <div className="grid md:grid-cols-2 gap-6">
            {/* Equipped Car */}
            <div className={`${getRarityBg(selectedCar.rarity)} border ${getRarityColor(selectedCar.rarity)} rounded-lg p-4`}>
              <h4 className="text-white font-medium mb-2">{selectedCar.name}</h4>
              <p className="text-white/70 text-sm mb-3">{selectedCar.description}</p>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Speed: {selectedCar.stats.speed}</div>
                <div>Acceleration: {selectedCar.stats.acceleration}</div>
                <div>Handling: {selectedCar.stats.handling}</div>
                <div>Durability: {selectedCar.stats.durability}</div>
              </div>
            </div>

            {/* Equipped Upgrades */}
            <div>
              <h4 className="text-white font-medium mb-3">Equipped Upgrades</h4>
              {inventory.equippedUpgrades.length === 0 ? (
                <p className="text-white/70 text-sm">No upgrades equipped</p>
              ) : (
                <div className="space-y-2">
                  {inventory.equippedUpgrades.map(upgradeId => {
                    const upgrade = inventory.upgrades.find(u => u.id === upgradeId);
                    if (!upgrade) return null;
                    
                    return (
                      <div key={upgradeId} className="bg-white/10 rounded p-2">
                        <div className="flex justify-between items-center">
                          <span className="text-white text-sm">{upgrade.name}</span>
                          <span className={`text-xs px-2 py-1 rounded ${getRarityColor(upgrade.rarity)}`}>
                            {upgrade.rarity}
                          </span>
                        </div>
                        {upgrade.statBoosts && (
                          <div className="text-xs text-green-400 mt-1">
                            {Object.entries(upgrade.statBoosts).map(([stat, boost]) => (
                              <span key={stat} className="mr-2">+{boost} {stat}</span>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        ) : (
          <p className="text-white/70">No car equipped</p>
        )}

        {/* Total Stats */}
        {selectedCar && (
          <div className="mt-4 pt-4 border-t border-white/20">
            <h4 className="text-white font-medium mb-2">Total Stats (with upgrades)</h4>
            <div className="grid grid-cols-5 gap-4">
              {(() => {
                const totalStats = carStore.calculateTotalStats(selectedCar.id, inventory.equippedUpgrades);
                return totalStats ? Object.entries(totalStats).map(([stat, value]) => (
                  <div key={stat} className="text-center">
                    <div className="text-white text-sm capitalize">{stat}</div>
                    <div className="text-yellow-400 font-bold">{value}</div>
                  </div>
                )) : null;
              })()}
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        {[
          { id: 'cars', label: 'My Cars', icon: '🏎️' },
          { id: 'upgrades', label: 'My Upgrades', icon: '⚙️' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setSelectedTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              selectedTab === tab.id
                ? 'bg-blue-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Cars Tab */}
      {selectedTab === 'cars' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-white">My Cars ({inventory.cars.length})</h3>
          </div>
          
          {inventory.cars.length === 0 ? (
            <p className="text-white/70 text-center py-8">No cars owned</p>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {inventory.cars.map((car) => (
                <div key={car.id} className={`${getRarityBg(car.rarity)} border ${getRarityColor(car.rarity)} rounded-lg p-4 ${car.isEquipped ? 'ring-2 ring-yellow-400' : ''}`}>
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-white font-medium">{car.name}</h4>
                    <div className="flex flex-col items-end gap-1">
                      <span className={`text-xs px-2 py-1 rounded ${getRarityColor(car.rarity)}`}>
                        {car.rarity}
                      </span>
                      {car.isEquipped && (
                        <span className="text-xs px-2 py-1 rounded bg-yellow-600 text-yellow-100">
                          Equipped
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-white/70 text-sm mb-3">{car.description}</p>
                  
                  <div className="grid grid-cols-2 gap-2 text-xs mb-3">
                    <div>Speed: {car.stats.speed}</div>
                    <div>Accel: {car.stats.acceleration}</div>
                    <div>Handle: {car.stats.handling}</div>
                    <div>Durability: {car.stats.durability}</div>
                  </div>
                  
                  <button
                    onClick={() => handleEquipCar(car.id)}
                    disabled={car.isEquipped}
                    className={`w-full py-2 px-4 rounded text-sm transition-colors ${
                      car.isEquipped
                        ? 'bg-yellow-600 text-yellow-100 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {car.isEquipped ? 'Equipped' : 'Equip'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Upgrades Tab */}
      {selectedTab === 'upgrades' && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-white">My Upgrades ({inventory.upgrades.length})</h3>
            <div className="text-sm text-white/70">
              Equipped: {inventory.equippedUpgrades.length}
            </div>
          </div>
          
          {inventory.upgrades.length === 0 ? (
            <p className="text-white/70 text-center py-8">No upgrades owned</p>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {inventory.upgrades.map((upgrade) => (
                <div key={upgrade.id} className={`${getRarityBg(upgrade.rarity)} border ${getRarityColor(upgrade.rarity)} rounded-lg p-4 ${upgrade.isEquipped ? 'ring-2 ring-green-400' : ''}`}>
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="text-white font-medium">{upgrade.name}</h4>
                    <div className="flex flex-col items-end gap-1">
                      <span className={`text-xs px-2 py-1 rounded ${getRarityColor(upgrade.rarity)}`}>
                        {upgrade.rarity}
                      </span>
                      {upgrade.isEquipped && (
                        <span className="text-xs px-2 py-1 rounded bg-green-600 text-green-100">
                          Equipped
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-white/70 text-sm mb-3">{upgrade.description}</p>
                  
                  {upgrade.statBoosts && (
                    <div className="text-xs mb-3">
                      <div className="text-green-400">
                        {Object.entries(upgrade.statBoosts).map(([stat, boost]) => (
                          <div key={stat}>+{boost} {stat}</div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <button
                    onClick={() => handleToggleUpgrade(upgrade.id, upgrade.isEquipped)}
                    className={`w-full py-2 px-4 rounded text-sm transition-colors ${
                      upgrade.isEquipped
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-green-600 hover:bg-green-700 text-white'
                    }`}
                  >
                    {upgrade.isEquipped ? 'Unequip' : 'Equip'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
