'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface Car {
  id: string;
  x: number;
  y: number;
  color: string;
  speed: number;
  progress: number;
}

interface PvPGameState {
  gameStarted: boolean;
  gameFinished: boolean;
  raceDistance: number;
  playerCar: Car;
  aiCar: Car;
  winner: string | null;
  countdown: number;
  raceTime: number;
  stakeAmount: number;
  prizePool: number;
}

export function PvPRacingGame() {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const keysRef = useRef<Set<string>>(new Set());
  
  const [gameState, setGameState] = useState<PvPGameState>({
    gameStarted: false,
    gameFinished: false,
    raceDistance: 1000, // meters
    playerCar: { 
      id: 'player', 
      x: 30, 
      y: 70, 
      color: '#ff6b35', 
      speed: 0, 
      progress: 0 
    },
    aiCar: { 
      id: 'ai', 
      x: 70, 
      y: 70, 
      color: '#e74c3c', 
      speed: 0, 
      progress: 0 
    },
    winner: null,
    countdown: 0,
    raceTime: 0,
    stakeAmount: 0,
    prizePool: 0,
  });

  const GAME_WIDTH = 400;
  const GAME_HEIGHT = 600;
  const CAR_WIDTH = 40;
  const CAR_HEIGHT = 60;
  const FINISH_LINE = 90; // percentage

  // Game loop for PvP racing
  const gameLoop = useCallback(() => {
    setGameState(prevState => {
      if (!prevState.gameStarted || prevState.gameFinished) return prevState;

      let newState = { ...prevState };
      newState.raceTime += 1/60; // 60fps

      // Player car movement
      const acceleration = 0.3;
      const maxSpeed = 8;
      const friction = 0.95;

      if (keysRef.current.has('ArrowUp')) {
        newState.playerCar.speed = Math.min(maxSpeed, newState.playerCar.speed + acceleration);
      } else {
        newState.playerCar.speed *= friction;
      }

      if (keysRef.current.has('ArrowLeft') && newState.playerCar.x > 10) {
        newState.playerCar.x -= 2;
      }
      if (keysRef.current.has('ArrowRight') && newState.playerCar.x < 50) {
        newState.playerCar.x += 2;
      }

      // Update player progress
      newState.playerCar.progress += newState.playerCar.speed * 0.1;

      // AI car logic (simple but competitive)
      const aiTargetSpeed = 6 + Math.sin(newState.raceTime * 0.5) * 2; // Variable speed
      if (newState.aiCar.speed < aiTargetSpeed) {
        newState.aiCar.speed = Math.min(aiTargetSpeed, newState.aiCar.speed + acceleration * 0.8);
      } else {
        newState.aiCar.speed *= 0.98;
      }

      // AI steering (simple lane changes)
      if (Math.random() < 0.02) {
        newState.aiCar.x = 50 + Math.random() * 40;
      }

      // Update AI progress
      newState.aiCar.progress += newState.aiCar.speed * 0.1;

      // Check for finish
      if (newState.playerCar.progress >= newState.raceDistance) {
        newState.gameFinished = true;
        newState.winner = 'player';
      } else if (newState.aiCar.progress >= newState.raceDistance) {
        newState.gameFinished = true;
        newState.winner = 'ai';
      }

      return newState;
    });

    if (gameState.gameStarted && !gameState.gameFinished) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameStarted, gameState.gameFinished]);

  // Start race with countdown
  const startRace = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      countdown: 3,
      gameStarted: false,
      gameFinished: false,
      playerCar: { ...prev.playerCar, progress: 0, speed: 0 },
      aiCar: { ...prev.aiCar, progress: 0, speed: 0 },
      winner: null,
      raceTime: 0,
    }));

    // Countdown timer
    let count = 3;
    const countdownInterval = setInterval(() => {
      count--;
      setGameState(prev => ({ ...prev, countdown: count }));
      
      if (count <= 0) {
        clearInterval(countdownInterval);
        setGameState(prev => ({ ...prev, gameStarted: true, countdown: 0 }));
        animationRef.current = requestAnimationFrame(gameLoop);
      }
    }, 1000);
  }, [gameLoop]);

  // Keyboard handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.code);
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.code);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const playerProgressPercent = (gameState.playerCar.progress / gameState.raceDistance) * 100;
  const aiProgressPercent = (gameState.aiCar.progress / gameState.raceDistance) * 100;

  return (
    <div className="space-y-4">
      {/* Race Stats */}
      <div className="grid grid-cols-3 gap-3 text-center">
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{gameState.raceTime.toFixed(1)}s</div>
          <div className="text-xs text-gray-400">Time</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{Math.floor(playerProgressPercent)}%</div>
          <div className="text-xs text-gray-400">Progress</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{gameState.playerCar.speed.toFixed(1)}</div>
          <div className="text-xs text-gray-400">Speed</div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-white text-sm w-16">You:</span>
          <div className="flex-1 bg-gray-700 rounded-full h-4">
            <div 
              className="bg-blue-500 h-4 rounded-full transition-all duration-100"
              style={{ width: `${Math.min(100, playerProgressPercent)}%` }}
            />
          </div>
          <span className="text-white text-sm w-12">{Math.floor(playerProgressPercent)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-white text-sm w-16">AI:</span>
          <div className="flex-1 bg-gray-700 rounded-full h-4">
            <div 
              className="bg-red-500 h-4 rounded-full transition-all duration-100"
              style={{ width: `${Math.min(100, aiProgressPercent)}%` }}
            />
          </div>
          <span className="text-white text-sm w-12">{Math.floor(aiProgressPercent)}%</span>
        </div>
      </div>

      {/* Game Container */}
      <div 
        ref={gameRef}
        className="relative bg-gray-900 rounded-lg overflow-hidden border-2 border-gray-700"
        style={{ width: GAME_WIDTH, height: GAME_HEIGHT }}
      >
        {/* Road */}
        <div className="absolute inset-0 bg-gray-800">
          {/* Road lines */}
          <div className="absolute bg-white opacity-60" style={{ left: '48%', width: '4%', height: '100%' }} />
          
          {/* Finish line */}
          <div 
            className="absolute bg-yellow-400 opacity-80"
            style={{ 
              top: `${FINISH_LINE}%`, 
              left: '0%', 
              width: '100%', 
              height: '2px',
              boxShadow: '0 0 10px yellow'
            }}
          />
        </div>

        {/* Player Car */}
        <div
          className="absolute transition-all duration-100"
          style={{
            left: `${gameState.playerCar.x}%`,
            top: `${gameState.playerCar.y}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: gameState.playerCar.color,
            borderRadius: '8px',
            transform: 'translate(-50%, -50%)',
            border: '2px solid white',
          }}
        />

        {/* AI Car */}
        <div
          className="absolute transition-all duration-100"
          style={{
            left: `${gameState.aiCar.x}%`,
            top: `${gameState.aiCar.y}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: gameState.aiCar.color,
            borderRadius: '8px',
            transform: 'translate(-50%, -50%)',
            border: '2px solid white',
          }}
        />

        {/* Countdown */}
        {gameState.countdown > 0 && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-6xl font-bold text-white animate-pulse">
              {gameState.countdown}
            </div>
          </div>
        )}

        {/* Race Finished */}
        {gameState.gameFinished && (
          <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-white">
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <div className="text-4xl mb-4">
                {gameState.winner === 'player' ? '🏆' : '😞'}
              </div>
              <h2 className="text-2xl font-bold mb-4">
                {gameState.winner === 'player' ? 'You Win!' : 'AI Wins!'}
              </h2>
              <div className="space-y-2 mb-6">
                <div>Race Time: {gameState.raceTime.toFixed(2)}s</div>
                <div>Your Progress: {Math.floor(playerProgressPercent)}%</div>
                <div>AI Progress: {Math.floor(aiProgressPercent)}%</div>
              </div>
              <button
                onClick={startRace}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded transition-colors"
              >
                🔄 Race Again
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Start Button */}
      {!gameState.gameStarted && !gameState.gameFinished && gameState.countdown === 0 && (
        <button
          onClick={startRace}
          className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded transition-colors"
        >
          🏁 Start Race
        </button>
      )}
    </div>
  );
}
