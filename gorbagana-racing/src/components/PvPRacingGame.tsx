'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface Car {
  id: string;
  x: number;
  y: number;
  color: string;
  speed: number;
  progress: number;
}

interface PvPGameState {
  gameStarted: boolean;
  gameFinished: boolean;
  raceDistance: number;
  playerCar: Car;
  aiCar: Car;
  winner: string | null;
  countdown: number;
  raceTime: number;
  stakeAmount: number;
  prizePool: number;
}

export function PvPRacingGame() {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const keysRef = useRef<Set<string>>(new Set());
  
  const [gameState, setGameState] = useState<PvPGameState>({
    gameStarted: false,
    gameFinished: false,
    raceDistance: 1000, // meters
    playerCar: { 
      id: 'player', 
      x: 30, 
      y: 70, 
      color: '#ff6b35', 
      speed: 0, 
      progress: 0 
    },
    aiCar: { 
      id: 'ai', 
      x: 70, 
      y: 70, 
      color: '#e74c3c', 
      speed: 0, 
      progress: 0 
    },
    winner: null,
    countdown: 0,
    raceTime: 0,
    stakeAmount: 0,
    prizePool: 0,
  });

  const GAME_WIDTH = 500;
  const GAME_HEIGHT = 400;
  const CAR_WIDTH = 40;
  const CAR_HEIGHT = 60;
  const FINISH_LINE = 90; // percentage

  // Game loop for PvP racing
  const gameLoop = useCallback(() => {
    setGameState(prevState => {
      if (!prevState.gameStarted || prevState.gameFinished) return prevState;

      const newState = { ...prevState };
      newState.raceTime += 1/60; // 60fps

      // Player car movement
      const acceleration = 0.4;
      const maxSpeed = 6;
      const friction = 0.92;

      // Player controls
      if (keysRef.current.has('ArrowUp') || keysRef.current.has('KeyW')) {
        newState.playerCar.speed = Math.min(maxSpeed, newState.playerCar.speed + acceleration);
      } else {
        newState.playerCar.speed *= friction;
      }

      if ((keysRef.current.has('ArrowLeft') || keysRef.current.has('KeyA')) && newState.playerCar.x > 5) {
        newState.playerCar.x -= 3;
      }
      if ((keysRef.current.has('ArrowRight') || keysRef.current.has('KeyD')) && newState.playerCar.x < 45) {
        newState.playerCar.x += 3;
      }

      // Update player progress (move forward on track)
      newState.playerCar.progress += newState.playerCar.speed * 0.8;

      // Smart AI logic - tries to win but not too easy
      const playerProgress = newState.playerCar.progress;
      const aiProgress = newState.aiCar.progress;

      // AI speed strategy: be competitive but give player a chance
      let aiTargetSpeed = 4.5; // Base speed

      if (aiProgress < playerProgress - 10) {
        // AI is behind, speed up
        aiTargetSpeed = 6.5;
      } else if (aiProgress > playerProgress + 15) {
        // AI is too far ahead, slow down a bit
        aiTargetSpeed = 3.5;
      } else {
        // Close race, vary speed for excitement
        aiTargetSpeed = 4.5 + Math.sin(newState.raceTime * 0.3) * 1.5;
      }

      // AI acceleration
      if (newState.aiCar.speed < aiTargetSpeed) {
        newState.aiCar.speed = Math.min(aiTargetSpeed, newState.aiCar.speed + acceleration * 0.9);
      } else {
        newState.aiCar.speed *= 0.95;
      }

      // AI steering - simple lane changes to avoid being too predictable
      if (Math.random() < 0.015) {
        const targetX = 55 + Math.random() * 35; // Stay in right lane mostly
        newState.aiCar.x = Math.max(50, Math.min(90, targetX));
      }

      // Update AI progress
      newState.aiCar.progress += newState.aiCar.speed * 0.8;

      // Check for finish (first to reach 100% wins)
      if (newState.playerCar.progress >= 100) {
        newState.gameFinished = true;
        newState.winner = 'player';
      } else if (newState.aiCar.progress >= 100) {
        newState.gameFinished = true;
        newState.winner = 'ai';
      }

      return newState;
    });

    if (prevState.gameStarted && !prevState.gameFinished) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, []);

  // Start race with countdown
  const startRace = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      countdown: 3,
      gameStarted: false,
      gameFinished: false,
      playerCar: { ...prev.playerCar, progress: 0, speed: 0 },
      aiCar: { ...prev.aiCar, progress: 0, speed: 0 },
      winner: null,
      raceTime: 0,
    }));

    // Countdown timer
    let count = 3;
    const countdownInterval = setInterval(() => {
      count--;
      setGameState(prev => ({ ...prev, countdown: count }));
      
      if (count <= 0) {
        clearInterval(countdownInterval);
        setGameState(prev => ({ ...prev, gameStarted: true, countdown: 0 }));
        animationRef.current = requestAnimationFrame(gameLoop);
      }
    }, 1000);
  }, [gameLoop]);

  // Keyboard handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.code);
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.code);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);

  // Cleanup
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const playerProgressPercent = Math.min(100, gameState.playerCar.progress);
  const aiProgressPercent = Math.min(100, gameState.aiCar.progress);

  return (
    <div className="space-y-4">
      {/* Race Stats */}
      <div className="grid grid-cols-3 gap-3 text-center">
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{gameState.raceTime.toFixed(1)}s</div>
          <div className="text-xs text-gray-400">Time</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{Math.floor(playerProgressPercent)}%</div>
          <div className="text-xs text-gray-400">Progress</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{gameState.playerCar.speed.toFixed(1)}</div>
          <div className="text-xs text-gray-400">Speed</div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className="text-white text-sm w-16">You:</span>
          <div className="flex-1 bg-gray-700 rounded-full h-4">
            <div 
              className="bg-blue-500 h-4 rounded-full transition-all duration-100"
              style={{ width: `${Math.min(100, playerProgressPercent)}%` }}
            />
          </div>
          <span className="text-white text-sm w-12">{Math.floor(playerProgressPercent)}%</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-white text-sm w-16">AI:</span>
          <div className="flex-1 bg-gray-700 rounded-full h-4">
            <div 
              className="bg-red-500 h-4 rounded-full transition-all duration-100"
              style={{ width: `${Math.min(100, aiProgressPercent)}%` }}
            />
          </div>
          <span className="text-white text-sm w-12">{Math.floor(aiProgressPercent)}%</span>
        </div>
      </div>

      {/* Game Container */}
      <div 
        ref={gameRef}
        className="relative bg-gray-900 rounded-lg overflow-hidden border-2 border-gray-700"
        style={{ width: GAME_WIDTH, height: GAME_HEIGHT }}
      >
        {/* Road */}
        <div className="absolute inset-0 bg-gray-800">
          {/* Road lines */}
          <div className="absolute bg-white opacity-60" style={{ left: '48%', width: '4%', height: '100%' }} />
          
          {/* Finish line */}
          <div 
            className="absolute bg-yellow-400 opacity-80"
            style={{ 
              top: `${FINISH_LINE}%`, 
              left: '0%', 
              width: '100%', 
              height: '2px',
              boxShadow: '0 0 10px yellow'
            }}
          />
        </div>

        {/* Player Car */}
        <div
          className="absolute transition-all duration-100"
          style={{
            left: `${gameState.playerCar.x}%`,
            top: `${95 - (gameState.playerCar.progress) * 0.85}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: gameState.playerCar.color,
            borderRadius: '8px',
            transform: 'translate(-50%, -50%)',
            border: '2px solid white',
            boxShadow: '0 0 15px rgba(59, 130, 246, 0.7)',
          }}
        >
          <div className="absolute inset-1 bg-gradient-to-b from-blue-300 to-blue-600 rounded-md">
            <div className="absolute top-1 left-1 w-2 h-2 bg-white rounded-full opacity-80"></div>
            <div className="absolute top-1 right-1 w-2 h-2 bg-white rounded-full opacity-80"></div>
          </div>
        </div>

        {/* AI Car */}
        <div
          className="absolute transition-all duration-100"
          style={{
            left: `${gameState.aiCar.x}%`,
            top: `${95 - (gameState.aiCar.progress) * 0.85}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: gameState.aiCar.color,
            borderRadius: '8px',
            transform: 'translate(-50%, -50%)',
            border: '2px solid white',
            boxShadow: '0 0 15px rgba(239, 68, 68, 0.7)',
          }}
        >
          <div className="absolute inset-1 bg-gradient-to-b from-red-300 to-red-600 rounded-md">
            <div className="absolute top-1 left-1 w-2 h-2 bg-white rounded-full opacity-80"></div>
            <div className="absolute top-1 right-1 w-2 h-2 bg-white rounded-full opacity-80"></div>
          </div>
        </div>

        {/* Start Screen */}
        {!gameState.gameStarted && gameState.countdown === 0 && (
          <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="text-6xl mb-4">🏁</div>
              <h3 className="text-2xl font-bold mb-2">PvP Racing</h3>
              <p className="text-gray-300 mb-4">Use arrow keys to control your car</p>
              <p className="text-sm text-gray-400">Click "Start Race" below to begin!</p>
            </div>
          </div>
        )}

        {/* Countdown */}
        {gameState.countdown > 0 && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-6xl font-bold text-white animate-pulse">
              {gameState.countdown}
            </div>
          </div>
        )}

        {/* Race Finished */}
        {gameState.gameFinished && (
          <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-white">
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <div className="text-4xl mb-4">
                {gameState.winner === 'player' ? '🏆' : '😞'}
              </div>
              <h2 className="text-2xl font-bold mb-4">
                {gameState.winner === 'player' ? 'You Win!' : 'AI Wins!'}
              </h2>
              <div className="space-y-2 mb-6">
                <div>Race Time: {gameState.raceTime.toFixed(2)}s</div>
                <div>Your Progress: {Math.floor(playerProgressPercent)}%</div>
                <div>AI Progress: {Math.floor(aiProgressPercent)}%</div>
              </div>
              <div className="space-y-2">
                <button
                  onClick={startRace}
                  className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 shadow-lg"
                >
                  🔄 Race Again
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-2 px-6 rounded-xl transition-all"
                >
                  🏠 Main Menu
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Auto-start when not started */}
      {!gameState.gameStarted && !gameState.gameFinished && gameState.countdown === 0 && (
        <div className="text-center">
          <button
            onClick={startRace}
            className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 shadow-lg"
          >
            🏁 Start Race
          </button>
        </div>
      )}
    </div>
  );
}
