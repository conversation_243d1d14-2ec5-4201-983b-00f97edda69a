'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { LobbyBrowser } from '@/components/LobbyBrowser';
import { LobbyChat } from '@/components/LobbyChat';
import { LobbyManager, LobbyInstance, LobbyConfig, MatchmakingCriteria, ChatMessage } from '@/lib/lobby-manager';

export function RaceLobbySystem() {
  const { connected, publicKey } = useWallet();
  const { balance, payEntryFee } = useBlockchain();
  
  const [lobbyManager] = useState(() => new LobbyManager());
  const [currentLobby, setCurrentLobby] = useState<LobbyInstance | null>(null);
  const [availableLobbies, setAvailableLobbies] = useState<LobbyInstance[]>([]);
  const [lobbyConfigs, setLobbyConfigs] = useState<LobbyConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  // Initialize lobby system
  useEffect(() => {
    if (!connected || !publicKey) return;

    // Set up lobby manager event listeners
    const handleLobbyCreated = (data: { lobbyId: string; lobby: LobbyInstance }) => {
      setAvailableLobbies(prev => [...prev, data.lobby]);
    };

    const handlePlayerJoinedLobby = (data: { lobbyId: string; player: any }) => {
      if (data.player.id === publicKey.toString()) {
        const lobby = lobbyManager.getLobby(data.lobbyId);
        if (lobby) {
          setCurrentLobby(lobby);
          setChatMessages(lobby.chatMessages);
        }
      }
      refreshLobbies();
    };

    const handlePlayerLeftLobby = (data: { lobbyId: string; playerId: string }) => {
      if (data.playerId === publicKey.toString()) {
        setCurrentLobby(null);
        setChatMessages([]);
      }
      refreshLobbies();
    };

    const handleChatMessage = (data: { lobbyId: string; message: ChatMessage }) => {
      if (currentLobby?.config.id === data.lobbyId) {
        setChatMessages(prev => [...prev, data.message]);
      }
    };

    const handleLobbyStarting = (data: { lobbyId: string; race: any }) => {
      if (currentLobby?.config.id === data.lobbyId) {
        // Race is starting, transition to race view
        console.log('Race starting!', data.race);
      }
    };

    const handleLobbyClosed = (data: { lobbyId: string }) => {
      if (currentLobby?.config.id === data.lobbyId) {
        setCurrentLobby(null);
        setChatMessages([]);
      }
      refreshLobbies();
    };

    lobbyManager.on('lobbyCreated', handleLobbyCreated);
    lobbyManager.on('playerJoinedLobby', handlePlayerJoinedLobby);
    lobbyManager.on('playerLeftLobby', handlePlayerLeftLobby);
    lobbyManager.on('chatMessage', handleChatMessage);
    lobbyManager.on('lobbyStarting', handleLobbyStarting);
    lobbyManager.on('lobbyClosed', handleLobbyClosed);

    // Load initial data
    refreshLobbies();
    setLobbyConfigs(lobbyManager.getLobbyConfigs());

    return () => {
      lobbyManager.removeAllListeners();
    };
  }, [connected, publicKey, lobbyManager, currentLobby]);

  const refreshLobbies = () => {
    setAvailableLobbies(lobbyManager.getAvailableLobbies());
  };

  const handleJoinLobby = async (lobbyId: string, password?: string) => {
    if (!publicKey) return;

    setIsLoading(true);
    setError(null);

    try {
      const lobby = lobbyManager.getLobby(lobbyId);
      if (!lobby) {
        throw new Error('Lobby not found');
      }

      // Check if player can afford entry fee
      if (balance < lobby.config.entryFee) {
        throw new Error('Insufficient balance for entry fee');
      }

      // Pay entry fee
      const paymentResult = await payEntryFee();
      if (!paymentResult.success) {
        throw new Error(paymentResult.error || 'Failed to pay entry fee');
      }

      // Join lobby
      const player = {
        id: publicKey.toString(),
        publicKey: publicKey,
        name: `Player ${publicKey.toString().slice(0, 8)}`,
        position: 0,
        speed: 1,
        tokenBalance: balance,
        powerUps: [],
        isReady: false,
        avatar: '🏎️',
      };

      const success = lobbyManager.joinLobby(lobbyId, player, password);
      if (!success) {
        throw new Error('Failed to join lobby');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join lobby');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveLobby = () => {
    if (!currentLobby || !publicKey) return;

    const success = lobbyManager.leaveLobby(currentLobby.config.id, publicKey.toString());
    if (success) {
      setCurrentLobby(null);
      setChatMessages([]);
    }
  };

  const handleQuickMatch = async (criteria: MatchmakingCriteria) => {
    if (!publicKey) return;

    setIsLoading(true);
    setError(null);

    try {
      const lobbyId = lobbyManager.findMatch(publicKey.toString(), criteria);
      
      if (lobbyId) {
        await handleJoinLobby(lobbyId);
      } else {
        // Create new lobby if no match found
        const newLobbyId = lobbyManager.createLobby('beginner-circuit');
        if (newLobbyId) {
          await handleJoinLobby(newLobbyId);
        } else {
          throw new Error('Failed to create new lobby');
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Quick match failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCustomLobby = async (config: Partial<LobbyConfig>) => {
    if (!publicKey) return;

    setIsLoading(true);
    setError(null);

    try {
      // Create temporary config
      const tempConfig: LobbyConfig = {
        id: `custom-${Date.now()}`,
        name: config.name || 'Custom Lobby',
        description: config.description || 'Custom race lobby',
        maxPlayers: config.maxPlayers || 6,
        minPlayers: config.minPlayers || 2,
        entryFee: config.entryFee || 50,
        prizeMultiplier: config.prizeMultiplier || 3.0,
        difficulty: config.difficulty || 'Intermediate',
        autoStart: config.autoStart ?? true,
        autoStartDelay: config.autoStartDelay || 30000,
        skillMatchmaking: config.skillMatchmaking ?? false,
        tokenRequirement: config.tokenRequirement || (config.entryFee || 50) * 2,
        track: config.track || {
          id: 'track-custom',
          name: 'Custom Track',
          length: 1000,
          difficulty: 2,
          obstacles: [],
          shortcuts: [],
        },
      };

      // Add to lobby configs temporarily
      lobbyManager['lobbyConfigs'].set(tempConfig.id, tempConfig);

      const lobbyId = lobbyManager.createLobby(tempConfig.id);
      if (lobbyId) {
        await handleJoinLobby(lobbyId);
      } else {
        throw new Error('Failed to create custom lobby');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create lobby');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetReady = (ready: boolean) => {
    if (!currentLobby || !publicKey) return;

    lobbyManager.setPlayerReady(currentLobby.config.id, publicKey.toString(), ready);
  };

  const handleSendChatMessage = (message: string) => {
    if (!currentLobby || !publicKey) return;

    lobbyManager.addChatMessage(currentLobby.config.id, {
      playerId: publicKey.toString(),
      playerName: `Player ${publicKey.toString().slice(0, 8)}`,
      message,
      type: 'chat',
    });
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Race Lobby System</h3>
        <p className="text-white/70">Connect your wallet to access race lobbies</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
          <p className="text-red-200 text-sm">{error}</p>
          <button
            onClick={() => setError(null)}
            className="text-red-300 hover:text-red-100 text-xs mt-1"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Current Lobby */}
      {currentLobby ? (
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Lobby Info */}
          <div className="lg:col-span-2 bg-white/10 backdrop-blur-sm rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{currentLobby.config.name}</h3>
              <button
                onClick={handleLeaveLobby}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
              >
                Leave Lobby
              </button>
            </div>

            <p className="text-white/70 mb-4">{currentLobby.config.description}</p>

            {/* Lobby Stats */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-white">{currentLobby.players.size}</div>
                <div className="text-xs text-white/70">Players</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-yellow-400">{currentLobby.config.entryFee}</div>
                <div className="text-xs text-white/70">Entry Fee</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-green-400">
                  {(currentLobby.config.entryFee * currentLobby.players.size * currentLobby.config.prizeMultiplier).toFixed(0)}
                </div>
                <div className="text-xs text-white/70">Prize Pool</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-2xl font-bold text-blue-400">{currentLobby.readyPlayers.size}</div>
                <div className="text-xs text-white/70">Ready</div>
              </div>
            </div>

            {/* Players List */}
            <div className="space-y-2 mb-6">
              <h4 className="text-white font-medium">Players ({currentLobby.players.size}/{currentLobby.config.maxPlayers})</h4>
              {Array.from(currentLobby.players.values()).map((player) => (
                <div key={player.id} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{player.avatar}</span>
                    <div>
                      <div className="text-white font-medium">
                        {player.name}
                        {player.id === publicKey?.toString() && (
                          <span className="text-yellow-400 ml-2">(You)</span>
                        )}
                      </div>
                      <div className="text-white/60 text-sm">{player.tokenBalance} GORB</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {currentLobby.readyPlayers.has(player.id) && (
                      <span className="bg-green-600 text-green-100 px-2 py-1 rounded text-xs">Ready</span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Ready Button */}
            {publicKey && currentLobby.players.has(publicKey.toString()) && (
              <div className="flex gap-2">
                <button
                  onClick={() => handleSetReady(!currentLobby.readyPlayers.has(publicKey.toString()))}
                  className={`flex-1 py-3 px-6 rounded-lg font-medium transition-colors ${
                    currentLobby.readyPlayers.has(publicKey.toString())
                      ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                >
                  {currentLobby.readyPlayers.has(publicKey.toString()) ? 'Not Ready' : 'Ready to Race!'}
                </button>
              </div>
            )}
          </div>

          {/* Chat */}
          <div>
            <LobbyChat
              messages={chatMessages}
              onSendMessage={handleSendChatMessage}
              isEnabled={currentLobby.settings.chatEnabled}
              playerCount={currentLobby.players.size}
            />
          </div>
        </div>
      ) : (
        /* Lobby Browser */
        <LobbyBrowser
          onJoinLobby={handleJoinLobby}
          onQuickMatch={handleQuickMatch}
          onCreateCustomLobby={handleCreateCustomLobby}
          availableLobbies={availableLobbies}
          lobbyConfigs={lobbyConfigs}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
