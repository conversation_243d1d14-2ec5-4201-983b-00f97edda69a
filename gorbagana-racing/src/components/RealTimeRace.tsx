'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useMultiplayer } from '@/hooks/useMultiplayer';
import { useBlockchain } from '@/hooks/useBlockchain';
import { PowerUpType } from '@/types/game';
import { GAME_CONFIG } from '@/lib/blockchain';

export function RealTimeRace() {
  const { publicKey } = useWallet();
  const { buyPowerUp } = useBlockchain();
  const {
    currentRace,
    players,
    isInRace,
    raceStatus,
    playerPosition,
    usePowerUp: useMultiplayerPowerUp,
  } = useMultiplayer();

  const [activePowerUps, setActivePowerUps] = useState<PowerUpType[]>([]);
  const [powerUpCooldowns, setPowerUpCooldowns] = useState<Record<string, number>>({});

  // Handle power-up usage
  const handleUsePowerUp = async (powerUpType: PowerUpType) => {
    if (powerUpCooldowns[powerUpType] > 0) return;

    // Purchase power-up with blockchain transaction
    const result = await buyPowerUp(powerUpType);
    
    if (result.success) {
      // Use power-up in multiplayer game
      useMultiplayerPowerUp(powerUpType);
      
      // Add to active power-ups
      setActivePowerUps(prev => [...prev, powerUpType]);
      
      // Set cooldown
      setPowerUpCooldowns(prev => ({
        ...prev,
        [powerUpType]: 5000 // 5 second cooldown
      }));

      // Remove from active after duration
      setTimeout(() => {
        setActivePowerUps(prev => prev.filter(p => p !== powerUpType));
      }, getPowerUpDuration(powerUpType));
    }
  };

  // Get power-up duration
  const getPowerUpDuration = (powerUpType: PowerUpType): number => {
    switch (powerUpType) {
      case PowerUpType.SPEED_BOOST:
        return 5000;
      case PowerUpType.SHIELD:
        return 8000;
      case PowerUpType.SHORTCUT:
        return 1000;
      case PowerUpType.DOUBLE_TOKENS:
        return 10000;
      default:
        return 5000;
    }
  };

  // Update cooldowns
  useEffect(() => {
    const interval = setInterval(() => {
      setPowerUpCooldowns(prev => {
        const updated = { ...prev };
        Object.keys(updated).forEach(key => {
          if (updated[key] > 0) {
            updated[key] = Math.max(0, updated[key] - 100);
          }
        });
        return updated;
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  if (!isInRace || !currentRace) {
    return null;
  }

  // Sort players by position for leaderboard
  const sortedPlayers = [...players].sort((a, b) => b.position - a.position);
  const currentPlayer = players.find(p => p.id === publicKey?.toString());

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">🏁 Live Race</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          raceStatus === 'starting' ? 'bg-orange-600 text-orange-100' :
          raceStatus === 'racing' ? 'bg-green-600 text-green-100' :
          'bg-blue-600 text-blue-100'
        }`}>
          {raceStatus === 'starting' ? 'Get Ready!' : 
           raceStatus === 'racing' ? 'Racing!' : 
           'Finished!'}
        </div>
      </div>

      {/* Race Track */}
      <div className="bg-white/5 rounded-lg p-4">
        <div className="space-y-3">
          {sortedPlayers.map((player, index) => (
            <div key={player.id} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-500 text-yellow-900' :
                    index === 1 ? 'bg-gray-400 text-gray-900' :
                    index === 2 ? 'bg-orange-600 text-orange-100' :
                    'bg-blue-600 text-blue-100'
                  }`}>
                    {index + 1}
                  </span>
                  <span className="text-white">{player.name}</span>
                  {player.id === publicKey?.toString() && (
                    <span className="text-yellow-400 text-xs">(You)</span>
                  )}
                </div>
                <span className="text-white">{player.position.toFixed(1)}%</span>
              </div>
              
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    player.id === publicKey?.toString()
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500'
                      : 'bg-gradient-to-r from-blue-400 to-purple-500'
                  }`}
                  style={{ width: `${Math.min(player.position, 100)}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Your Stats */}
      {currentPlayer && (
        <div className="bg-white/5 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Your Performance</h4>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-yellow-400">
                {sortedPlayers.findIndex(p => p.id === currentPlayer.id) + 1}
              </div>
              <div className="text-xs text-white/70">Position</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-400">
                {currentPlayer.position.toFixed(1)}%
              </div>
              <div className="text-xs text-white/70">Progress</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-400">
                {currentPlayer.speed.toFixed(1)}x
              </div>
              <div className="text-xs text-white/70">Speed</div>
            </div>
          </div>
        </div>
      )}

      {/* Power-ups */}
      {raceStatus === 'racing' && (
        <div className="space-y-3">
          <h4 className="text-white font-medium">Power-ups</h4>
          
          {/* Active Power-ups */}
          {activePowerUps.length > 0 && (
            <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3">
              <div className="text-green-200 text-sm font-medium mb-2">Active:</div>
              <div className="flex flex-wrap gap-2">
                {activePowerUps.map((powerUp, index) => (
                  <span 
                    key={`${powerUp}-${index}`}
                    className="bg-green-600 text-green-100 px-2 py-1 rounded text-xs"
                  >
                    {powerUp.replace('_', ' ')}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Power-up Buttons */}
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(GAME_CONFIG.POWER_UP_COSTS).map(([powerUp, cost]) => {
              const cooldown = powerUpCooldowns[powerUp] || 0;
              const isOnCooldown = cooldown > 0;
              
              return (
                <button
                  key={powerUp}
                  onClick={() => handleUsePowerUp(powerUp as PowerUpType)}
                  disabled={isOnCooldown}
                  className={`p-3 rounded-lg text-sm font-medium transition-all ${
                    isOnCooldown
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-purple-600 hover:bg-purple-700 text-white hover:scale-105'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span>{powerUp.replace('_', ' ')}</span>
                    <span className="text-xs">{cost} GORB</span>
                  </div>
                  {isOnCooldown && (
                    <div className="text-xs mt-1">
                      {(cooldown / 1000).toFixed(1)}s
                    </div>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Race Results */}
      {raceStatus === 'finished' && currentRace.winner && (
        <div className="bg-white/5 rounded-lg p-4 text-center">
          <h4 className="text-xl font-bold text-white mb-2">🏆 Race Finished!</h4>
          <p className="text-lg text-yellow-400 mb-2">
            Winner: {currentRace.winner.name}
          </p>
          <p className="text-white/70">
            Prize Pool: {currentRace.prizePool} GORB
          </p>
        </div>
      )}

      <div className="text-xs text-white/50 border-t border-white/10 pt-3">
        <p>Real-time multiplayer racing powered by Socket.io</p>
      </div>
    </div>
  );
}
