'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface Car {
  id: string;
  x: number;
  y: number;
  color: string;
}

interface Token {
  id: string;
  x: number;
  y: number;
}

interface GameState {
  score: number;
  gameOver: boolean;
  baseSpeed: number;
  currentSpeed: number;
  playerCar: Car;
  enemyCars: Car[];
  roadLines: { id: string; y: number }[];
  tokens: Token[];
  tokensCollected: number;
  level: number;
  lives: number;
  speedBoost: number;
  showReplay: boolean;
}

export function SimpleRacingGame() {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const keysRef = useRef<Set<string>>(new Set());
  
  const [gameState, setGameState] = useState<GameState>({
    score: 0,
    gameOver: false,
    baseSpeed: 2,
    currentSpeed: 2,
    playerCar: { id: 'player', x: 60, y: 85, color: '#ff6b35' },
    enemyCars: [
      { id: 'enemy1', x: 60, y: -20, color: '#e74c3c' },
      { id: 'enemy2', x: 40, y: -40, color: '#3498db' },
      { id: 'enemy3', x: 50, y: -70, color: '#9b59b6' },
    ],
    roadLines: [
      { id: 'line1', y: -30 },
      { id: 'line2', y: 30 },
      { id: 'line3', y: 90 },
    ],
    tokens: [],
    tokensCollected: 0,
    level: 1,
    lives: 3,
    speedBoost: 0,
    showReplay: false,
  });

  const GAME_WIDTH = 300;
  const GAME_HEIGHT = 500;
  const CAR_WIDTH = 40;
  const CAR_HEIGHT = 60;

  // Collision detection
  const checkCollision = useCallback((car1: Car, car2: Car) => {
    const car1Left = (car1.x / 100) * GAME_WIDTH;
    const car1Right = car1Left + CAR_WIDTH;
    const car1Top = (car1.y / 100) * GAME_HEIGHT;
    const car1Bottom = car1Top + CAR_HEIGHT;

    const car2Left = (car2.x / 100) * GAME_WIDTH;
    const car2Right = car2Left + CAR_WIDTH;
    const car2Top = (car2.y / 100) * GAME_HEIGHT;
    const car2Bottom = car2Top + CAR_HEIGHT;

    return !(car1Right < car2Left || car1Left > car2Right || car1Bottom < car2Top || car1Top > car2Bottom);
  }, []);

  // Game loop
  const gameLoop = useCallback(() => {
    setGameState(prevState => {
      if (prevState.gameOver) return prevState;

      let newState = { ...prevState };

      // Calculate current speed based on tokens collected
      const tokenSpeedBonus = Math.floor(newState.tokensCollected / 5) * 0.5;
      newState.currentSpeed = newState.baseSpeed + tokenSpeedBonus + newState.speedBoost;

      // Move player car based on keys (faster movement)
      const moveSpeed = 3;
      if (keysRef.current.has('ArrowLeft') && newState.playerCar.x > 5) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x - moveSpeed };
      }
      if (keysRef.current.has('ArrowRight') && newState.playerCar.x < 85) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x + moveSpeed };
      }
      if (keysRef.current.has('ArrowUp') && newState.playerCar.y > 5) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y - 2 };
      }
      if (keysRef.current.has('ArrowDown') && newState.playerCar.y < 90) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y + 2 };
      }

      // Move enemy cars (speed varies by car)
      newState.enemyCars = newState.enemyCars.map((car, index) => {
        const carSpeed = newState.currentSpeed + (index * 0.3); // Different speeds
        let newY = car.y + carSpeed;
        let newX = car.x;

        if (newY > 110) {
          newY = -20 - (Math.random() * 50); // Stagger spawning
          newX = 15 + Math.random() * 70; // Random lane
          // Random car colors for variety
          const colors = ['#e74c3c', '#3498db', '#9b59b6', '#f39c12', '#2ecc71'];
          car.color = colors[Math.floor(Math.random() * colors.length)];
        }

        return { ...car, x: newX, y: newY };
      });

      // Move road lines (faster for better visual effect)
      newState.roadLines = newState.roadLines.map(line => {
        let newY = line.y + newState.currentSpeed * 3;
        if (newY > 110) {
          newY = -30;
        }
        return { ...line, y: newY };
      });

      // Spawn tokens more frequently and strategically
      if (Math.random() < 0.015) { // 1.5% chance per frame
        newState.tokens.push({
          id: `token-${Date.now()}-${Math.random()}`,
          x: 25 + Math.random() * 50, // Keep tokens in safer zones
          y: -10,
        });
      }

      // Move tokens
      newState.tokens = newState.tokens.filter(token => {
        const newY = token.y + newState.currentSpeed;
        if (newY > 110) return false;

        token.y = newY;

        // Check token collection (larger collection area)
        const tokenLeft = (token.x / 100) * GAME_WIDTH;
        const tokenRight = tokenLeft + 25;
        const tokenTop = (token.y / 100) * GAME_HEIGHT;
        const tokenBottom = tokenTop + 25;

        const playerLeft = (newState.playerCar.x / 100) * GAME_WIDTH;
        const playerRight = playerLeft + CAR_WIDTH;
        const playerTop = (newState.playerCar.y / 100) * GAME_HEIGHT;
        const playerBottom = playerTop + CAR_HEIGHT;

        if (!(tokenRight < playerLeft || tokenLeft > playerRight || tokenBottom < playerTop || tokenTop > playerBottom)) {
          newState.tokensCollected += 1;
          newState.score += 50; // Bonus points for tokens

          // Temporary speed boost when collecting tokens
          newState.speedBoost = Math.min(newState.speedBoost + 0.1, 2);

          return false; // Remove collected token
        }

        return true;
      });

      // Gradually reduce speed boost
      if (newState.speedBoost > 0) {
        newState.speedBoost = Math.max(0, newState.speedBoost - 0.01);
      }

      // Check collisions
      for (const enemyCar of newState.enemyCars) {
        if (checkCollision(newState.playerCar, enemyCar)) {
          newState.lives -= 1;
          if (newState.lives <= 0) {
            newState.gameOver = true;
            newState.showReplay = true;
          } else {
            // Give temporary invincibility and reset position
            newState.playerCar.y = 85;
            newState.speedBoost = 0; // Reset speed boost on collision
          }
          break;
        }
      }

      // Update score and level
      newState.score += 2;
      const newLevel = Math.floor(newState.score / 1000) + 1;
      if (newLevel > newState.level) {
        newState.level = newLevel;
        newState.baseSpeed += 0.3; // Increase base speed each level
      }

      return newState;
    });

    if (!gameState.gameOver) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, checkCollision]);

  // Start game function
  const startGame = useCallback(() => {
    setGameState({
      score: 0,
      gameOver: false,
      baseSpeed: 2,
      currentSpeed: 2,
      playerCar: { id: 'player', x: 60, y: 85, color: '#ff6b35' },
      enemyCars: [
        { id: 'enemy1', x: 60, y: -20, color: '#e74c3c' },
        { id: 'enemy2', x: 40, y: -40, color: '#3498db' },
        { id: 'enemy3', x: 50, y: -70, color: '#9b59b6' },
      ],
      roadLines: [
        { id: 'line1', y: -30 },
        { id: 'line2', y: 30 },
        { id: 'line3', y: 90 },
      ],
      tokens: [],
      tokensCollected: 0,
      level: 1,
      lives: 3,
      speedBoost: 0,
      showReplay: false,
    });

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop]);

  // Keyboard handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.code);

      // Restart game on Enter when game over
      if (e.code === 'Enter' && gameState.gameOver) {
        startGame();
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.code);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [gameState.gameOver, startGame]);



  // Cleanup
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Auto-start game loop when not game over
  useEffect(() => {
    if (!gameState.gameOver && !animationRef.current) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, gameLoop]);

  return (
    <div className="flex flex-col items-center space-y-6 p-4">
      {/* Enhanced Stats Dashboard */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-3 w-full max-w-2xl">
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="text-2xl font-bold">{Math.floor(gameState.score)}</div>
          <div className="text-xs opacity-90">SCORE</div>
        </div>
        <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="text-2xl font-bold">🪙 {gameState.tokensCollected}</div>
          <div className="text-xs opacity-90">TOKENS</div>
        </div>
        <div className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="text-2xl font-bold">{gameState.currentSpeed.toFixed(1)}x</div>
          <div className="text-xs opacity-90">SPEED</div>
        </div>
        <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="text-2xl font-bold">L{gameState.level}</div>
          <div className="text-xs opacity-90">LEVEL</div>
        </div>
        <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-3 rounded-xl shadow-lg text-center">
          <div className="text-2xl font-bold">{'❤️'.repeat(gameState.lives)}</div>
          <div className="text-xs opacity-90">LIVES</div>
        </div>
      </div>

      {/* Speed Boost Indicator */}
      {gameState.speedBoost > 0 && (
        <div className="bg-gradient-to-r from-yellow-400 to-red-500 text-white px-6 py-2 rounded-full shadow-lg animate-pulse">
          <span className="font-bold">🚀 SPEED BOOST ACTIVE! +{gameState.speedBoost.toFixed(1)}</span>
        </div>
      )}

      {/* Game Container */}
      <div
        ref={gameRef}
        className="relative bg-gradient-to-b from-gray-700 via-gray-800 to-gray-900 rounded-2xl overflow-hidden shadow-2xl border-4 border-white/20"
        style={{ width: GAME_WIDTH, height: GAME_HEIGHT }}
      >
        {/* Road Lines */}
        {gameState.roadLines.map(line => (
          <div
            key={line.id}
            className="absolute bg-gradient-to-b from-yellow-400 to-white rounded-full opacity-80"
            style={{
              left: '48%',
              width: '4%',
              height: '120px',
              top: `${line.y}%`,
              boxShadow: '0 0 10px rgba(255, 255, 255, 0.5)',
            }}
          />
        ))}

        {/* Player Car */}
        <div
          className={`absolute transition-all duration-75 ease-linear ${gameState.speedBoost > 0 ? 'animate-pulse' : ''}`}
          style={{
            left: `${gameState.playerCar.x}%`,
            top: `${gameState.playerCar.y}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: `linear-gradient(45deg, ${gameState.playerCar.color}, #ff8c42)`,
            borderRadius: '8px',
            boxShadow: gameState.speedBoost > 0
              ? '0px 0px 20px 5px rgba(255, 107, 53, 0.8)'
              : '0px 2px 15px 2px rgba(0, 0, 0, 0.7)',
            transform: 'translate(-50%, -50%)',
            border: '2px solid rgba(255, 255, 255, 0.3)',
          }}
        >
          {/* Car Details */}
          <div className="absolute bg-gray-800 rounded-b border border-gray-600" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-gray-800 rounded-t border border-gray-600" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-yellow-300 rounded-t border border-yellow-500" style={{ top: '-6%', left: '10%', width: '20%', height: '12%' }} />
          <div className="absolute bg-yellow-300 rounded-t border border-yellow-500" style={{ top: '-6%', right: '10%', width: '20%', height: '12%' }} />
          {/* Speed boost flames */}
          {gameState.speedBoost > 0 && (
            <>
              <div className="absolute bg-gradient-to-t from-red-500 to-yellow-400 rounded-full opacity-80 animate-pulse"
                   style={{ bottom: '-10%', left: '20%', width: '15%', height: '20%' }} />
              <div className="absolute bg-gradient-to-t from-red-500 to-yellow-400 rounded-full opacity-80 animate-pulse"
                   style={{ bottom: '-10%', right: '20%', width: '15%', height: '20%' }} />
            </>
          )}
        </div>

        {/* Enemy Cars */}
        {gameState.enemyCars.map(car => (
          <div
            key={car.id}
            className="absolute"
            style={{
              left: `${car.x}%`,
              top: `${car.y}%`,
              width: CAR_WIDTH,
              height: CAR_HEIGHT,
              background: `linear-gradient(135deg, ${car.color}, ${car.color}dd)`,
              borderRadius: '6px',
              boxShadow: '0px 2px 12px 1px rgba(0, 0, 0, 0.6)',
              transform: 'translate(-50%, -50%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
          >
            {/* Car Details */}
            <div className="absolute bg-gray-700 rounded-b border border-gray-500" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-gray-700 rounded-t border border-gray-500" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-red-400 rounded-t" style={{ bottom: '-6%', left: '10%', width: '20%', height: '12%' }} />
            <div className="absolute bg-red-400 rounded-t" style={{ bottom: '-6%', right: '10%', width: '20%', height: '12%' }} />
          </div>
        ))}

        {/* Tokens */}
        {gameState.tokens.map(token => (
          <div
            key={token.id}
            className="absolute animate-spin"
            style={{
              left: `${token.x}%`,
              top: `${token.y}%`,
              width: '25px',
              height: '25px',
              transform: 'translate(-50%, -50%)',
            }}
          >
            <div
              className="w-full h-full rounded-full border-3 border-yellow-300 animate-pulse"
              style={{
                background: 'radial-gradient(circle, #ffd700, #ffed4e, #ffd700)',
                boxShadow: '0 0 20px 5px rgba(255, 215, 0, 0.8), inset 0 0 10px rgba(255, 255, 255, 0.5)',
              }}
            >
              <div className="absolute inset-1 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full">
                <div className="absolute top-1 left-1 w-2 h-2 bg-white rounded-full opacity-80"></div>
              </div>
            </div>
          </div>
        ))}

        {/* Game Over Screen */}
        {gameState.gameOver && gameState.showReplay && (
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/95 via-pink-900/95 to-red-900/95 flex flex-col items-center justify-center text-white backdrop-blur-sm">
            <div className="bg-white/10 rounded-2xl p-8 border border-white/20 shadow-2xl text-center max-w-sm">
              <div className="text-6xl mb-4">💥</div>
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-red-500 bg-clip-text text-transparent">
                GAME OVER!
              </h2>

              <div className="space-y-4 mb-8">
                <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-xl p-4">
                  <div className="text-3xl font-bold text-yellow-400">{Math.floor(gameState.score)}</div>
                  <div className="text-sm opacity-80">FINAL SCORE</div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white/10 rounded-lg p-3">
                    <div className="text-xl font-bold text-yellow-400">🪙 {gameState.tokensCollected}</div>
                    <div className="text-xs opacity-80">Tokens</div>
                  </div>
                  <div className="bg-white/10 rounded-lg p-3">
                    <div className="text-xl font-bold text-blue-400">L{gameState.level}</div>
                    <div className="text-xs opacity-80">Level</div>
                  </div>
                </div>

                <div className="text-sm text-gray-300">
                  Max Speed: {gameState.currentSpeed.toFixed(1)}x
                </div>
              </div>

              <button
                onClick={startGame}
                className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-4 px-6 rounded-xl transition-all transform hover:scale-105 shadow-lg mb-4"
              >
                🚀 RACE AGAIN
              </button>

              <p className="text-xs text-gray-400">Press Enter to restart</p>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Controls & Tips */}
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 max-w-2xl">
        <h3 className="text-white font-bold text-lg mb-4 text-center">🎮 How to Play</h3>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-white font-semibold mb-2">Controls:</h4>
            <div className="space-y-2 text-sm text-white/80">
              <div className="flex items-center gap-2">
                <span className="bg-white/20 px-2 py-1 rounded">←→</span>
                <span>Steer left/right</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-white/20 px-2 py-1 rounded">↑↓</span>
                <span>Speed up/slow down</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="bg-white/20 px-2 py-1 rounded">Enter</span>
                <span>Restart when crashed</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-white font-semibold mb-2">Pro Tips:</h4>
            <div className="space-y-1 text-sm text-white/80">
              <div>🪙 <span className="text-yellow-400">Tokens boost your speed!</span></div>
              <div>❤️ <span className="text-red-400">You have 3 lives</span></div>
              <div>🚀 <span className="text-blue-400">Speed increases each level</span></div>
              <div>⚡ <span className="text-purple-400">Collect 5 tokens = speed boost</span></div>
            </div>
          </div>
        </div>
      </div>

      {/* Start Button */}
      {!animationRef.current && !gameState.gameOver && (
        <button
          onClick={startGame}
          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-4 px-8 rounded-2xl transition-all transform hover:scale-105 shadow-lg text-xl"
        >
          🏁 START YOUR ENGINES!
        </button>
      )}
    </div>
  );
}
