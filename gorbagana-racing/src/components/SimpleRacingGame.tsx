'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface Car {
  id: string;
  x: number;
  y: number;
  color: string;
}

interface Token {
  id: string;
  x: number;
  y: number;
}

interface Obstacle {
  id: string;
  x: number;
  y: number;
  type: 'pothole' | 'debris' | 'oil' | 'barrier';
  size: number;
}

interface GameState {
  score: number;
  gameOver: boolean;
  baseSpeed: number;
  currentSpeed: number;
  playerCar: Car;
  enemyCars: Car[];
  roadLines: { id: string; y: number }[];
  tokens: Token[];
  obstacles: Obstacle[];
  tokensCollected: number;
  level: number;
  lives: number;
  speedBoost: number;
  showReplay: boolean;
  distance: number;
  mode: 'endless' | 'pvp';
}

export function SimpleRacingGame() {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const keysRef = useRef<Set<string>>(new Set());
  
  const [gameState, setGameState] = useState<GameState>({
    score: 0,
    gameOver: false,
    baseSpeed: 2,
    currentSpeed: 2,
    playerCar: { id: 'player', x: 60, y: 85, color: '#ff6b35' },
    enemyCars: [
      { id: 'enemy1', x: 60, y: -20, color: '#e74c3c' },
      { id: 'enemy2', x: 40, y: -40, color: '#3498db' },
      { id: 'enemy3', x: 50, y: -70, color: '#9b59b6' },
    ],
    roadLines: [
      { id: 'line1', y: -30 },
      { id: 'line2', y: 30 },
      { id: 'line3', y: 90 },
    ],
    tokens: [],
    obstacles: [],
    tokensCollected: 0,
    level: 1,
    lives: 3,
    speedBoost: 0,
    showReplay: false,
    distance: 0,
    mode: 'endless',
  });

  const GAME_WIDTH = 600;
  const GAME_HEIGHT = 800;
  const CAR_WIDTH = 50;
  const CAR_HEIGHT = 75;

  // Collision detection
  const checkCollision = useCallback((car1: Car, car2: Car) => {
    const car1Left = (car1.x / 100) * GAME_WIDTH;
    const car1Right = car1Left + CAR_WIDTH;
    const car1Top = (car1.y / 100) * GAME_HEIGHT;
    const car1Bottom = car1Top + CAR_HEIGHT;

    const car2Left = (car2.x / 100) * GAME_WIDTH;
    const car2Right = car2Left + CAR_WIDTH;
    const car2Top = (car2.y / 100) * GAME_HEIGHT;
    const car2Bottom = car2Top + CAR_HEIGHT;

    return !(car1Right < car2Left || car1Left > car2Right || car1Bottom < car2Top || car1Top > car2Bottom);
  }, []);

  // Game loop
  const gameLoop = useCallback(() => {
    setGameState(prevState => {
      if (prevState.gameOver) return prevState;

      const newState = { ...prevState };
      
      // Calculate current speed based on tokens collected
      const tokenSpeedBonus = Math.floor(newState.tokensCollected / 5) * 0.5;
      newState.currentSpeed = newState.baseSpeed + tokenSpeedBonus + newState.speedBoost;
      
      // Move player car based on keys
      const moveSpeed = 3;
      if (keysRef.current.has('ArrowLeft') && newState.playerCar.x > 5) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x - moveSpeed };
      }
      if (keysRef.current.has('ArrowRight') && newState.playerCar.x < 85) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x + moveSpeed };
      }
      if (keysRef.current.has('ArrowUp') && newState.playerCar.y > 5) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y - 2 };
      }
      if (keysRef.current.has('ArrowDown') && newState.playerCar.y < 90) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y + 2 };
      }

      // Move enemy cars
      newState.enemyCars = newState.enemyCars.map((car, index) => {
        const carSpeed = newState.currentSpeed + (index * 0.3);
        let newY = car.y + carSpeed;
        let newX = car.x;
        
        if (newY > 110) {
          newY = -20 - (Math.random() * 50);
          newX = 15 + Math.random() * 70;
          const colors = ['#e74c3c', '#3498db', '#9b59b6', '#f39c12', '#2ecc71'];
          car.color = colors[Math.floor(Math.random() * colors.length)];
        }
        
        return { ...car, x: newX, y: newY };
      });

      // Move road lines
      newState.roadLines = newState.roadLines.map(line => {
        let newY = line.y + newState.currentSpeed * 3;
        if (newY > 110) {
          newY = -30;
        }
        return { ...line, y: newY };
      });

      // Spawn tokens
      if (Math.random() < 0.015) {
        newState.tokens.push({
          id: `token-${Date.now()}-${Math.random()}`,
          x: 25 + Math.random() * 50,
          y: -10,
        });
      }

      // Spawn obstacles
      if (Math.random() < 0.008) {
        const obstacleTypes: Obstacle['type'][] = ['pothole', 'debris', 'oil', 'barrier'];
        newState.obstacles.push({
          id: `obstacle-${Date.now()}-${Math.random()}`,
          x: 20 + Math.random() * 60,
          y: -15,
          type: obstacleTypes[Math.floor(Math.random() * obstacleTypes.length)],
          size: 15 + Math.random() * 10,
        });
      }

      // Move tokens
      newState.tokens = newState.tokens.filter(token => {
        const newY = token.y + newState.currentSpeed;
        if (newY > 110) return false;

        token.y = newY;

        // Check token collection with better collision detection
        const tokenLeft = (token.x / 100) * GAME_WIDTH;
        const tokenRight = tokenLeft + 30;
        const tokenTop = (token.y / 100) * GAME_HEIGHT;
        const tokenBottom = tokenTop + 30;

        const playerLeft = (newState.playerCar.x / 100) * GAME_WIDTH;
        const playerRight = playerLeft + CAR_WIDTH;
        const playerTop = (newState.playerCar.y / 100) * GAME_HEIGHT;
        const playerBottom = playerTop + CAR_HEIGHT;

        if (!(tokenRight < playerLeft || tokenLeft > playerRight || tokenBottom < playerTop || tokenTop > playerBottom)) {
          newState.tokensCollected += 1;
          newState.score += 100;
          newState.speedBoost = Math.min(newState.speedBoost + 0.2, 3);
          return false;
        }

        return true;
      });

      // Move obstacles
      newState.obstacles = newState.obstacles.filter(obstacle => {
        const newY = obstacle.y + newState.currentSpeed;
        if (newY > 110) return false;
        
        obstacle.y = newY;
        
        // Check obstacle collision
        const obstacleLeft = (obstacle.x / 100) * GAME_WIDTH;
        const obstacleRight = obstacleLeft + obstacle.size;
        const obstacleTop = (obstacle.y / 100) * GAME_HEIGHT;
        const obstacleBottom = obstacleTop + obstacle.size;

        const playerLeft = (newState.playerCar.x / 100) * GAME_WIDTH;
        const playerRight = playerLeft + CAR_WIDTH;
        const playerTop = (newState.playerCar.y / 100) * GAME_HEIGHT;
        const playerBottom = playerTop + CAR_HEIGHT;

        if (!(obstacleRight < playerLeft || obstacleLeft > playerRight || obstacleBottom < playerTop || obstacleTop > playerBottom)) {
          newState.lives -= 1;
          newState.currentSpeed = Math.max(1, newState.currentSpeed * 0.7);
          newState.speedBoost = 0;
          
          if (newState.lives <= 0) {
            newState.gameOver = true;
            newState.showReplay = true;
          }
          
          return false;
        }
        
        return true;
      });

      // Gradually reduce speed boost
      if (newState.speedBoost > 0) {
        newState.speedBoost = Math.max(0, newState.speedBoost - 0.01);
      }

      // Check car collisions
      for (const enemyCar of newState.enemyCars) {
        if (checkCollision(newState.playerCar, enemyCar)) {
          newState.lives -= 1;
          if (newState.lives <= 0) {
            newState.gameOver = true;
            newState.showReplay = true;
          } else {
            newState.playerCar.y = 85;
            newState.speedBoost = 0;
          }
          break;
        }
      }

      // Update distance and score
      newState.distance += newState.currentSpeed * 0.1;
      newState.score += Math.floor(newState.currentSpeed);
      
      const newLevel = Math.floor(newState.distance / 500) + 1;
      if (newLevel > newState.level) {
        newState.level = newLevel;
        newState.baseSpeed += 0.3;
      }

      return newState;
    });

    if (!gameState.gameOver) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, checkCollision]);

  // Start game function
  const startGame = useCallback(() => {
    setGameState({
      score: 0,
      gameOver: false,
      baseSpeed: 2,
      currentSpeed: 2,
      playerCar: { id: 'player', x: 60, y: 85, color: '#ff6b35' },
      enemyCars: [
        { id: 'enemy1', x: 60, y: -20, color: '#e74c3c' },
        { id: 'enemy2', x: 40, y: -40, color: '#3498db' },
        { id: 'enemy3', x: 50, y: -70, color: '#9b59b6' },
      ],
      roadLines: [
        { id: 'line1', y: -30 },
        { id: 'line2', y: 30 },
        { id: 'line3', y: 90 },
      ],
      tokens: [],
      obstacles: [],
      tokensCollected: 0,
      level: 1,
      lives: 3,
      speedBoost: 0,
      showReplay: false,
      distance: 0,
      mode: 'endless',
    });
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop]);

  // Keyboard handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.code);
      
      if (e.code === 'Enter' && gameState.gameOver) {
        startGame();
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.code);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [gameState.gameOver, startGame]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Auto-start game loop when not game over
  useEffect(() => {
    if (!gameState.gameOver && !animationRef.current) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, gameLoop]);

  return (
    <div className="space-y-4">
      {/* Simple Stats */}
      <div className="grid grid-cols-3 gap-3 text-center">
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{Math.floor(gameState.score)}</div>
          <div className="text-xs text-gray-400">Score</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">{Math.floor(gameState.distance)}m</div>
          <div className="text-xs text-gray-400">Distance</div>
        </div>
        <div className="bg-gray-800 text-white px-3 py-2 rounded">
          <div className="text-lg font-bold">🪙 {gameState.tokensCollected}</div>
          <div className="text-xs text-gray-400">Tokens</div>
        </div>
      </div>

      {/* Speed Boost Indicator */}
      {gameState.speedBoost > 0 && (
        <div className="bg-yellow-600 text-white px-4 py-2 rounded text-center animate-pulse">
          <span className="font-bold">🚀 BOOST!</span>
        </div>
      )}

      {/* Game Container */}
      <div 
        ref={gameRef}
        className="relative bg-gray-900 rounded-lg overflow-hidden border-2 border-gray-700"
        style={{ width: GAME_WIDTH, height: GAME_HEIGHT }}
      >
        {/* Road Lines */}
        {gameState.roadLines.map(line => (
          <div
            key={line.id}
            className="absolute bg-white rounded"
            style={{
              left: '48%',
              width: '4%',
              height: '100px',
              top: `${line.y}%`,
              opacity: 0.6,
            }}
          />
        ))}

        {/* Player Car */}
        <div
          className={`absolute transition-all duration-75 ease-linear ${gameState.speedBoost > 0 ? 'animate-pulse' : ''}`}
          style={{
            left: `${gameState.playerCar.x}%`,
            top: `${gameState.playerCar.y}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            background: `linear-gradient(45deg, ${gameState.playerCar.color}, #ff8c42)`,
            borderRadius: '8px',
            boxShadow: gameState.speedBoost > 0 
              ? '0px 0px 20px 5px rgba(255, 107, 53, 0.8)' 
              : '0px 2px 15px 2px rgba(0, 0, 0, 0.7)',
            transform: 'translate(-50%, -50%)',
            border: '2px solid rgba(255, 255, 255, 0.3)',
          }}
        >
          <div className="absolute bg-gray-800 rounded-b border border-gray-600" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-gray-800 rounded-t border border-gray-600" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-yellow-300 rounded-t border border-yellow-500" style={{ top: '-6%', left: '10%', width: '20%', height: '12%' }} />
          <div className="absolute bg-yellow-300 rounded-t border border-yellow-500" style={{ top: '-6%', right: '10%', width: '20%', height: '12%' }} />
        </div>

        {/* Enemy Cars */}
        {gameState.enemyCars.map(car => (
          <div
            key={car.id}
            className="absolute"
            style={{
              left: `${car.x}%`,
              top: `${car.y}%`,
              width: CAR_WIDTH,
              height: CAR_HEIGHT,
              background: `linear-gradient(135deg, ${car.color}, ${car.color}dd)`,
              borderRadius: '6px',
              boxShadow: '0px 2px 12px 1px rgba(0, 0, 0, 0.6)',
              transform: 'translate(-50%, -50%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
          >
            <div className="absolute bg-gray-700 rounded-b border border-gray-500" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-gray-700 rounded-t border border-gray-500" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-red-400 rounded-t" style={{ bottom: '-6%', left: '10%', width: '20%', height: '12%' }} />
            <div className="absolute bg-red-400 rounded-t" style={{ bottom: '-6%', right: '10%', width: '20%', height: '12%' }} />
          </div>
        ))}

        {/* Tokens */}
        {gameState.tokens.map(token => (
          <div
            key={token.id}
            className="absolute animate-spin"
            style={{
              left: `${token.x}%`,
              top: `${token.y}%`,
              width: '35px',
              height: '35px',
              transform: 'translate(-50%, -50%)',
            }}
          >
            <div
              className="w-full h-full rounded-full border-3 border-yellow-300 animate-pulse"
              style={{
                background: 'radial-gradient(circle, #ffd700, #ffed4e, #ffd700)',
                boxShadow: '0 0 25px 8px rgba(255, 215, 0, 0.9)',
              }}
            >
              <div className="absolute inset-2 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full">
                <div className="absolute top-1 left-1 w-3 h-3 bg-white rounded-full opacity-80"></div>
              </div>
            </div>
          </div>
        ))}

        {/* Obstacles */}
        {gameState.obstacles.map(obstacle => {
          const getObstacleStyle = (type: Obstacle['type']) => {
            switch (type) {
              case 'pothole':
                return { background: 'radial-gradient(circle, #2c1810, #1a0f08)', borderRadius: '50%' };
              case 'debris':
                return { background: 'linear-gradient(45deg, #666, #999)', borderRadius: '20%' };
              case 'oil':
                return { background: 'radial-gradient(ellipse, #1a1a1a, #000)', borderRadius: '60%', opacity: 0.8 };
              case 'barrier':
                return { background: 'linear-gradient(90deg, #ff4444, #ff6666)', borderRadius: '10%' };
              default:
                return {};
            }
          };

          return (
            <div
              key={obstacle.id}
              className="absolute"
              style={{
                left: `${obstacle.x}%`,
                top: `${obstacle.y}%`,
                width: `${obstacle.size}px`,
                height: `${obstacle.size}px`,
                transform: 'translate(-50%, -50%)',
                ...getObstacleStyle(obstacle.type),
              }}
            />
          );
        })}

        {/* Game Over Screen */}
        {gameState.gameOver && gameState.showReplay && (
          <div className="absolute inset-0 bg-black/80 flex flex-col items-center justify-center text-white">
            <div className="bg-gray-800 rounded-lg p-6 text-center max-w-sm">
              <div className="text-4xl mb-4">💥</div>
              <h2 className="text-2xl font-bold mb-4 text-white">Game Over!</h2>
              
              <div className="space-y-3 mb-6">
                <div className="bg-gray-700 rounded p-3">
                  <div className="text-xl font-bold text-yellow-400">{Math.floor(gameState.score)}</div>
                  <div className="text-sm text-gray-400">Final Score</div>
                </div>
                
                <div className="grid grid-cols-3 gap-2">
                  <div className="bg-gray-700 rounded p-2">
                    <div className="text-sm font-bold">{Math.floor(gameState.distance)}m</div>
                    <div className="text-xs text-gray-400">Distance</div>
                  </div>
                  <div className="bg-gray-700 rounded p-2">
                    <div className="text-sm font-bold">🪙 {gameState.tokensCollected}</div>
                    <div className="text-xs text-gray-400">Tokens</div>
                  </div>
                  <div className="bg-gray-700 rounded p-2">
                    <div className="text-sm font-bold">L{gameState.level}</div>
                    <div className="text-xs text-gray-400">Level</div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={startGame}
                  className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all transform hover:scale-105 shadow-lg"
                >
                  🔄 Play Again
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-2 px-6 rounded-xl transition-all"
                >
                  🏠 Main Menu
                </button>
              </div>
              
              <p className="text-xs text-gray-400">Press Enter to restart</p>
            </div>
          </div>
        )}
      </div>

      {/* Start Button */}
      {!animationRef.current && !gameState.gameOver && (
        <button
          onClick={startGame}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded transition-colors"
        >
          🏁 Start Game
        </button>
      )}
    </div>
  );
}
