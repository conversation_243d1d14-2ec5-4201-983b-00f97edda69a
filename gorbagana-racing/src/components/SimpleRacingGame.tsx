'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';

interface Car {
  id: string;
  x: number;
  y: number;
  color: string;
}

interface Token {
  id: string;
  x: number;
  y: number;
}

interface GameState {
  score: number;
  gameOver: boolean;
  speed: number;
  playerCar: Car;
  enemyCars: Car[];
  roadLines: { id: string; y: number }[];
  tokens: Token[];
  tokensCollected: number;
}

export function SimpleRacingGame() {
  const gameRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const keysRef = useRef<Set<string>>(new Set());
  
  const [gameState, setGameState] = useState<GameState>({
    score: 0,
    gameOver: false,
    speed: 2,
    playerCar: { id: 'player', x: 60, y: 85, color: '#ffdf5a' },
    enemyCars: [
      { id: 'enemy1', x: 60, y: -20, color: '#26c5ff' },
      { id: 'enemy2', x: 40, y: -40, color: '#26c5ff' },
      { id: 'enemy3', x: 50, y: -70, color: '#26c5ff' },
    ],
    roadLines: [
      { id: 'line1', y: -30 },
      { id: 'line2', y: 30 },
      { id: 'line3', y: 90 },
    ],
    tokens: [],
    tokensCollected: 0,
  });

  const GAME_WIDTH = 300;
  const GAME_HEIGHT = 500;
  const CAR_WIDTH = 40;
  const CAR_HEIGHT = 60;

  // Collision detection
  const checkCollision = useCallback((car1: Car, car2: Car) => {
    const car1Left = (car1.x / 100) * GAME_WIDTH;
    const car1Right = car1Left + CAR_WIDTH;
    const car1Top = (car1.y / 100) * GAME_HEIGHT;
    const car1Bottom = car1Top + CAR_HEIGHT;

    const car2Left = (car2.x / 100) * GAME_WIDTH;
    const car2Right = car2Left + CAR_WIDTH;
    const car2Top = (car2.y / 100) * GAME_HEIGHT;
    const car2Bottom = car2Top + CAR_HEIGHT;

    return !(car1Right < car2Left || car1Left > car2Right || car1Bottom < car2Top || car1Top > car2Bottom);
  }, []);

  // Game loop
  const gameLoop = useCallback(() => {
    setGameState(prevState => {
      if (prevState.gameOver) return prevState;

      let newState = { ...prevState };
      
      // Move player car based on keys
      if (keysRef.current.has('ArrowLeft') && newState.playerCar.x > 5) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x - 2 };
      }
      if (keysRef.current.has('ArrowRight') && newState.playerCar.x < 85) {
        newState.playerCar = { ...newState.playerCar, x: newState.playerCar.x + 2 };
      }
      if (keysRef.current.has('ArrowUp') && newState.playerCar.y > 5) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y - 1 };
      }
      if (keysRef.current.has('ArrowDown') && newState.playerCar.y < 90) {
        newState.playerCar = { ...newState.playerCar, y: newState.playerCar.y + 1 };
      }

      // Move enemy cars
      newState.enemyCars = newState.enemyCars.map(car => {
        let newY = car.y + newState.speed;
        let newX = car.x;
        
        if (newY > 110) {
          newY = -20;
          newX = 20 + Math.random() * 60; // Random lane
        }
        
        return { ...car, x: newX, y: newY };
      });

      // Move road lines
      newState.roadLines = newState.roadLines.map(line => {
        let newY = line.y + newState.speed * 2;
        if (newY > 110) {
          newY = -30;
        }
        return { ...line, y: newY };
      });

      // Spawn tokens randomly
      if (Math.random() < 0.01) { // 1% chance per frame
        newState.tokens.push({
          id: `token-${Date.now()}-${Math.random()}`,
          x: 20 + Math.random() * 60,
          y: -10,
        });
      }

      // Move tokens
      newState.tokens = newState.tokens.filter(token => {
        const newY = token.y + newState.speed;
        if (newY > 110) return false;

        token.y = newY;

        // Check token collection
        const tokenLeft = (token.x / 100) * GAME_WIDTH;
        const tokenRight = tokenLeft + 20;
        const tokenTop = (token.y / 100) * GAME_HEIGHT;
        const tokenBottom = tokenTop + 20;

        const playerLeft = (newState.playerCar.x / 100) * GAME_WIDTH;
        const playerRight = playerLeft + CAR_WIDTH;
        const playerTop = (newState.playerCar.y / 100) * GAME_HEIGHT;
        const playerBottom = playerTop + CAR_HEIGHT;

        if (!(tokenRight < playerLeft || tokenLeft > playerRight || tokenBottom < playerTop || tokenTop > playerBottom)) {
          newState.tokensCollected += 1;
          return false; // Remove collected token
        }

        return true;
      });

      // Check collisions
      for (const enemyCar of newState.enemyCars) {
        if (checkCollision(newState.playerCar, enemyCar)) {
          newState.gameOver = true;
          break;
        }
      }

      // Update score and speed
      newState.score += 1;
      if (newState.score % 500 === 0) {
        newState.speed += 0.5;
      }

      return newState;
    });

    if (!gameState.gameOver) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, checkCollision]);

  // Keyboard handlers
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.code);

      // Restart game on Enter when game over
      if (e.code === 'Enter' && gameState.gameOver) {
        startGame();
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.code);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [gameState.gameOver, startGame]);

  // Start game
  const startGame = useCallback(() => {
    setGameState({
      score: 0,
      gameOver: false,
      speed: 2,
      playerCar: { id: 'player', x: 60, y: 85, color: '#ffdf5a' },
      enemyCars: [
        { id: 'enemy1', x: 60, y: -20, color: '#26c5ff' },
        { id: 'enemy2', x: 40, y: -40, color: '#26c5ff' },
        { id: 'enemy3', x: 50, y: -70, color: '#26c5ff' },
      ],
      roadLines: [
        { id: 'line1', y: -30 },
        { id: 'line2', y: 30 },
        { id: 'line3', y: 90 },
      ],
      tokens: [],
      tokensCollected: 0,
    });

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameLoop]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // Auto-start game loop when not game over
  useEffect(() => {
    if (!gameState.gameOver && !animationRef.current) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }
  }, [gameState.gameOver, gameLoop]);

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Score and Stats */}
      <div className="flex gap-4">
        <div className="bg-white text-gray-800 px-4 py-2 rounded-lg shadow-lg font-bold text-lg">
          Score: {Math.floor(gameState.score / 20)}
        </div>
        <div className="bg-yellow-500 text-black px-4 py-2 rounded-lg shadow-lg font-bold text-lg">
          🪙 {gameState.tokensCollected}
        </div>
        <div className="bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg font-bold text-lg">
          Speed: {Math.floor(gameState.speed)}x
        </div>
      </div>

      {/* Game Container */}
      <div 
        ref={gameRef}
        className="relative bg-gray-800 rounded-lg overflow-hidden shadow-2xl"
        style={{ width: GAME_WIDTH, height: GAME_HEIGHT }}
      >
        {/* Road Lines */}
        {gameState.roadLines.map(line => (
          <div
            key={line.id}
            className="absolute bg-white/50 rounded"
            style={{
              left: '48%',
              width: '4%',
              height: '150px',
              top: `${line.y}%`,
            }}
          />
        ))}

        {/* Player Car */}
        <div
          className="absolute transition-all duration-75 ease-linear"
          style={{
            left: `${gameState.playerCar.x}%`,
            top: `${gameState.playerCar.y}%`,
            width: CAR_WIDTH,
            height: CAR_HEIGHT,
            backgroundColor: gameState.playerCar.color,
            borderRadius: '5px',
            boxShadow: '0px 1px 8px 0px black',
            transform: 'translate(-50%, -50%)',
          }}
        >
          {/* Car Details */}
          <div className="absolute bg-gray-600 rounded-b" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-gray-600 rounded-t" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
          <div className="absolute bg-gray-300 rounded-t" style={{ top: '-6%', left: '10%', width: '20%', height: '10%' }} />
          <div className="absolute bg-gray-300 rounded-t" style={{ top: '-6%', right: '10%', width: '20%', height: '10%' }} />
        </div>

        {/* Enemy Cars */}
        {gameState.enemyCars.map(car => (
          <div
            key={car.id}
            className="absolute"
            style={{
              left: `${car.x}%`,
              top: `${car.y}%`,
              width: CAR_WIDTH,
              height: CAR_HEIGHT,
              backgroundColor: car.color,
              borderRadius: '5px',
              boxShadow: '0px 1px 8px 0px black',
              transform: 'translate(-50%, -50%)',
            }}
          >
            {/* Car Details */}
            <div className="absolute bg-gray-600 rounded-b" style={{ top: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-gray-600 rounded-t" style={{ bottom: '15%', left: '20%', width: '60%', height: '20%' }} />
            <div className="absolute bg-gray-300 rounded-t" style={{ top: '-6%', left: '10%', width: '20%', height: '10%' }} />
            <div className="absolute bg-gray-300 rounded-t" style={{ top: '-6%', right: '10%', width: '20%', height: '10%' }} />
          </div>
        ))}

        {/* Tokens */}
        {gameState.tokens.map(token => (
          <div
            key={token.id}
            className="absolute bg-yellow-400 rounded-full border-2 border-yellow-600 animate-pulse"
            style={{
              left: `${token.x}%`,
              top: `${token.y}%`,
              width: '20px',
              height: '20px',
              transform: 'translate(-50%, -50%)',
              boxShadow: '0px 0px 10px 2px rgba(255, 215, 0, 0.6)',
            }}
          >
            <div className="absolute inset-1 bg-yellow-300 rounded-full"></div>
          </div>
        ))}

        {/* Game Over Screen */}
        {gameState.gameOver && (
          <div className="absolute inset-0 bg-gray-900/90 flex flex-col items-center justify-center text-white">
            <h2 className="text-3xl font-bold mb-4">🏁 Game Over!</h2>
            <div className="text-center mb-6 space-y-2">
              <p className="text-xl">Final Score: <span className="text-yellow-400 font-bold">{Math.floor(gameState.score / 20)}</span></p>
              <p className="text-lg">Tokens Collected: <span className="text-yellow-400 font-bold">🪙 {gameState.tokensCollected}</span></p>
              <p className="text-sm text-gray-300">Speed Level: {Math.floor(gameState.speed)}x</p>
            </div>
            <button
              onClick={startGame}
              className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors transform hover:scale-105"
            >
              🔄 Play Again
            </button>
            <p className="text-xs mt-4 text-gray-400">Press Enter to restart</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="text-center text-white/70 text-sm">
        <p className="mb-2">🎮 Use Arrow Keys to Control Your Car</p>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>← → Move Left/Right</div>
          <div>↑ ↓ Move Up/Down</div>
        </div>
        <p className="text-xs mt-2 text-yellow-400">🪙 Collect golden tokens for bonus points!</p>
      </div>

      {/* Start Button */}
      {!animationRef.current && !gameState.gameOver && (
        <button
          onClick={startGame}
          className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
        >
          Start Racing!
        </button>
      )}
    </div>
  );
}
