'use client';

import React from 'react';
import { useMultiplayer } from '@/hooks/useMultiplayer';
import { useWallet } from '@solana/wallet-adapter-react';

export function TrackVisualization() {
  const { publicKey } = useWallet();
  const { currentRace, players, isInRace } = useMultiplayer();

  if (!isInRace || !currentRace) {
    return null;
  }

  const track = currentRace.track;
  const trackLength = track.length;
  
  // Sort players by position for better visualization
  const sortedPlayers = [...players].sort((a, b) => b.position - a.position);
  const currentPlayer = players.find(p => p.id === publicKey?.toString());

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">🏁 Track Overview</h3>
        <div className="text-sm text-white/70">
          {track.name} • {trackLength}m
        </div>
      </div>

      {/* Track Visualization */}
      <div className="relative bg-white/5 rounded-lg p-4 overflow-hidden">
        {/* Track Background */}
        <div className="relative w-full h-32 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-lg overflow-hidden">
          
          {/* Start Line */}
          <div className="absolute left-0 top-0 w-1 h-full bg-green-400 z-10">
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-green-400 font-bold">
              START
            </div>
          </div>

          {/* Finish Line */}
          <div className="absolute right-0 top-0 w-1 h-full bg-yellow-400 z-10">
            <div className="absolute -top-6 right-1/2 transform translate-x-1/2 text-xs text-yellow-400 font-bold">
              FINISH
            </div>
          </div>

          {/* Obstacles */}
          {track.obstacles?.map((obstacle, index) => {
            const position = (obstacle.position / trackLength) * 100;
            return (
              <div
                key={obstacle.id}
                className={`absolute top-1/2 transform -translate-y-1/2 w-2 h-8 rounded ${
                  obstacle.type === 'HARD' ? 'bg-red-500' :
                  obstacle.type === 'MEDIUM' ? 'bg-orange-500' :
                  'bg-yellow-500'
                } z-20`}
                style={{ left: `${position}%` }}
                title={`${obstacle.type} Obstacle`}
              >
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs">
                  ⚠️
                </div>
              </div>
            );
          })}

          {/* Shortcuts */}
          {track.shortcuts?.map((shortcut, index) => {
            const startPos = (shortcut.startPosition / trackLength) * 100;
            const endPos = (shortcut.endPosition / trackLength) * 100;
            const width = endPos - startPos;
            
            return (
              <div
                key={shortcut.id}
                className="absolute top-0 h-full bg-blue-400/30 border-t-2 border-b-2 border-blue-400 z-10"
                style={{ 
                  left: `${startPos}%`, 
                  width: `${width}%` 
                }}
                title={`Shortcut (${shortcut.cost} GORB)`}
              >
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-blue-400">
                  🚀
                </div>
              </div>
            );
          })}

          {/* Player Positions */}
          {sortedPlayers.map((player, index) => {
            const position = Math.min((player.position / trackLength) * 100, 100);
            const isCurrentPlayer = player.id === publicKey?.toString();
            
            return (
              <div
                key={player.id}
                className={`absolute top-0 w-3 h-full z-30 transition-all duration-300 ${
                  isCurrentPlayer ? 'bg-yellow-400' : 'bg-blue-400'
                }`}
                style={{ 
                  left: `${position}%`,
                  transform: `translateY(${index * 4}px)` // Stagger players vertically
                }}
                title={`${player.name} - ${player.position.toFixed(1)}m`}
              >
                {/* Player Icon */}
                <div className={`absolute -top-8 left-1/2 transform -translate-x-1/2 text-lg ${
                  isCurrentPlayer ? 'animate-bounce' : ''
                }`}>
                  {player.avatar}
                </div>
                
                {/* Position Indicator */}
                <div className={`absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs font-bold ${
                  isCurrentPlayer ? 'text-yellow-400' : 'text-blue-400'
                }`}>
                  #{index + 1}
                </div>
              </div>
            );
          })}

          {/* Progress Markers */}
          {[25, 50, 75].map(percent => (
            <div
              key={percent}
              className="absolute top-0 w-px h-full bg-white/20"
              style={{ left: `${percent}%` }}
            >
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-white/50">
                {percent}%
              </div>
            </div>
          ))}
        </div>

        {/* Track Legend */}
        <div className="flex items-center justify-center gap-6 mt-4 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-yellow-500 rounded"></div>
            <span className="text-white/70">Light Obstacle</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-orange-500 rounded"></div>
            <span className="text-white/70">Medium Obstacle</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-red-500 rounded"></div>
            <span className="text-white/70">Hard Obstacle</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-400 rounded"></div>
            <span className="text-white/70">Shortcut</span>
          </div>
        </div>
      </div>

      {/* Current Player Stats */}
      {currentPlayer && (
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-white font-medium text-sm">Your Performance</h4>
            <span className="text-yellow-400 text-sm">
              {((currentPlayer.position / trackLength) * 100).toFixed(1)}% Complete
            </span>
          </div>
          
          <div className="grid grid-cols-4 gap-3 text-center text-xs">
            <div>
              <div className="text-white font-bold">{currentPlayer.position.toFixed(0)}m</div>
              <div className="text-white/60">Distance</div>
            </div>
            <div>
              <div className="text-white font-bold">{currentPlayer.speed.toFixed(1)}</div>
              <div className="text-white/60">Speed</div>
            </div>
            <div>
              <div className="text-white font-bold">
                #{sortedPlayers.findIndex(p => p.id === currentPlayer.id) + 1}
              </div>
              <div className="text-white/60">Position</div>
            </div>
            <div>
              <div className="text-white font-bold">{currentPlayer.powerUps.length}</div>
              <div className="text-white/60">Power-ups</div>
            </div>
          </div>
        </div>
      )}

      {/* Track Features Summary */}
      <div className="grid grid-cols-3 gap-3 text-center text-xs">
        <div className="bg-white/5 rounded p-2">
          <div className="text-lg font-bold text-white">{track.obstacles?.length || 0}</div>
          <div className="text-white/60">Obstacles</div>
        </div>
        <div className="bg-white/5 rounded p-2">
          <div className="text-lg font-bold text-white">{track.shortcuts?.length || 0}</div>
          <div className="text-white/60">Shortcuts</div>
        </div>
        <div className="bg-white/5 rounded p-2">
          <div className="text-lg font-bold text-white">{track.difficulty}/5</div>
          <div className="text-white/60">Difficulty</div>
        </div>
      </div>
    </div>
  );
}
