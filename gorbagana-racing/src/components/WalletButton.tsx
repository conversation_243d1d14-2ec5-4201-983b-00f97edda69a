'use client';

import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';

export function WalletButton() {
  const { connected, publicKey } = useWallet();

  return (
    <div className="flex items-center gap-4">
      <WalletMultiButton className="!bg-purple-600 hover:!bg-purple-700 !rounded-lg !px-4 !py-2 !text-white !font-medium !transition-colors" />
      {connected && publicKey && (
        <div className="text-sm text-gray-600">
          Connected: {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}
        </div>
      )}
    </div>
  );
}
