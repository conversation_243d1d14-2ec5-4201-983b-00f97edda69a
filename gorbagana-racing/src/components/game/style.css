body {
  height: 100%;
  width: 100%;
  margin: 0;
  background-color: #45d678;
  font-family: sans-serif;
}

#container{
  position: relative;
  height: 90vh;
  width: 25%;
  left: 35%;
  margin-top: 20px;;
  background-color: #292929;
  overflow: hidden;
}
.line{
  position: absolute;
  height: 150px;
  width: 4%;
  margin-left: 48%;
  background-color: rgba(255,255,255,0.5);
}
#line_1{
  top: -150px;
}
#line_2{
  top: 150px;
}
#line_3{
  top: 450px;
}
.car{
  position: absolute;
  height: 60px;
  width: 40px;
  border-radius: 5px;
  box-shadow: 0px 1px 8px 0px black;
}
#car{
  bottom: 8%;
  left: 60%;
  background-color: #ffdf5a;
}
.f_glass{
  position: absolute;
  height: 20%;
  width: 60%;
  margin-left: 20%;
  top: 15%;
  border-radius: 0px 0px 5px 5px;
  background-color: #484848;
}
.b_glass{
  position: absolute;
  height: 20%;
  width: 60%;
  margin-left: 20%;
  bottom: 15%;
  border-radius: 5px 5px 0px 0px;
  background-color: #484848;
}
.f_light_l{
  position: absolute;
  height: 10%;
  width: 20%;
  margin-left: 10%;
  top: -6%;
  border-radius: 5px 5px 0px 0px;
  background-color: #efefef;
}
.f_light_r{
  position: absolute;
  height: 10%;
  width: 20%;
  margin-left: 70%;
  top: -6%;
  border-radius: 5px 5px 0px 0px;
  background-color: #efefef;
}
.f_tyre_l{
  position: absolute;
  height: 20%;
  width: 10%;
  background-color: grey;
  top: 20%;
  left: -10%;
  border-radius: 5px 0px 0px 5px;
}
.f_tyre_r{
  position: absolute;
  height: 20%;
  width: 10%;
  background-color: grey;
  top: 20%;
  left: 100%;
  border-radius: 0px 5px 5px 0px;
}
.b_tyre_l{
  position: absolute;
  height: 20%;
  width: 10%;
  background-color: grey;
  top: 70%;
  left: -10%;
  border-radius: 5px 0px 0px 5px;
}
.b_tyre_r{
  position: absolute;
  height: 20%;
  width: 10%;
  background-color: grey;
  top: 70%;
  left: 100%;
  border-radius: 0px 5px 5px 0px;
}
#car_1{
  top: -100px;
  left: 60%;
  background-color: #26c5ff;
}
#car_2{
  top: -200px;
  left: 40%;
  background-color: #26c5ff;
}
#car_3{
  top: -350px;
  left: 50%;
  background-color: #26c5ff;
}
#restart_div {
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: #292929;
  color: white;
  font-family: sans-serif;
  font-size: 40px;
  text-align: center;
  display: none;
}

#restart {
  border: none;
  padding: 25px;
  color: white;
  background-color: #7760b9;
  font-size: 30px;
  margin-top: 30%;
}
.small_text{
  font-size: 15px;
}
#score_div{
  position: absolute;
  margin-top: 20%;
  margin-left: 10%;
  font-size: 35px;
  background-color: white;
  color: #454545;
  padding: 10px;
  box-shadow: 4px 4px 0px 1px #808080;
}
#help{
text-align: center;
}
#donate{
  position: absolute;
  right: 10px;
  top: 25px;
  background: rgba(255,255,255,0.8);
  padding: 12px;
}


$(document).ready(function() {

  var anim_id;

  //saving dom objects to variables
  var container = $('#container');
  var car = $('#car');
  var car_1 = $('#car_1');
  var car_2 = $('#car_2');
  var car_3 = $('#car_3');
  var line_1 = $('#line_1');
  var line_2 = $('#line_2');
  var line_3 = $('#line_3');
  var restart_div = $('#restart_div');
  var restart_btn = $('#restart');
  var score = $('#score');

  //saving some initial setup
  var container_left = parseInt(container.css('left'));
  var container_width = parseInt(container.width());
  var container_height = parseInt(container.height());
  var car_width = parseInt(car.width());
  var car_height = parseInt(car.height());

  //some other declarations
  var game_over = false;

  var score_counter = 1;

  var speed = 2;
  var line_speed = 5;

  var move_right = false;
  var move_left = false;
  var move_up = false;
  var move_down = false;

  /* ------------------------------GAME CODE STARTS HERE------------------------------------------- */
  /* Move the cars */
  $(document).on('keydown', function(e) {
      if (game_over === false) {
          var key = e.keyCode;
          if (key === 37 && move_left === false) {
              move_left = requestAnimationFrame(left);
          } else if (key === 39 && move_right === false) {
              move_right = requestAnimationFrame(right);
          } else if (key === 38 && move_up === false) {
              move_up = requestAnimationFrame(up);
          } else if (key === 40 && move_down === false) {
              move_down = requestAnimationFrame(down);
          }
      }
  });

  $(document).on('keyup', function(e) {
      if (game_over === false) {
          var key = e.keyCode;
          if (key === 37) {
              cancelAnimationFrame(move_left);
              move_left = false;
          } else if (key === 39) {
              cancelAnimationFrame(move_right);
              move_right = false;
          } else if (key === 38) {
              cancelAnimationFrame(move_up);
              move_up = false;
          } else if (key === 40) {
              cancelAnimationFrame(move_down);
              move_down = false;
          }
      }
  });

  function left() {
      if (game_over === false && parseInt(car.css('left')) > 0) {
          car.css('left', parseInt(car.css('left')) - 5);
          move_left = requestAnimationFrame(left);
      }
  }

  function right() {
      if (game_over === false && parseInt(car.css('left')) < container_width - car_width) {
          car.css('left', parseInt(car.css('left')) + 5);
          move_right = requestAnimationFrame(right);
      }
  }

  function up() {
      if (game_over === false && parseInt(car.css('top')) > 0) {
          car.css('top', parseInt(car.css('top')) - 3);
          move_up = requestAnimationFrame(up);
      }
  }

  function down() {
      if (game_over === false && parseInt(car.css('top')) < container_height - car_height) {
          car.css('top', parseInt(car.css('top')) + 3);
          move_down = requestAnimationFrame(down);
      }
  }

  /* Move the cars and lines */
  anim_id = requestAnimationFrame(repeat);

  function repeat() {
      if (collision(car, car_1) || collision(car, car_2) || collision(car, car_3)) {
          stop_the_game();
          return;
      }

      score_counter++;

      if (score_counter % 20 == 0) {
          score.text(parseInt(score.text()) + 1);
      }
      if (score_counter % 500 == 0) {
          speed++;
          line_speed++;
      }

      car_down(car_1);
      car_down(car_2);
      car_down(car_3);

      line_down(line_1);
      line_down(line_2);
      line_down(line_3);

      anim_id = requestAnimationFrame(repeat);
  }

  function car_down(car) {
      var car_current_top = parseInt(car.css('top'));
      if (car_current_top > container_height) {
          car_current_top = -200;
          var car_left = parseInt(Math.random() * (container_width - car_width));
          car.css('left', car_left);
      }
      car.css('top', car_current_top + speed);
  }

  function line_down(line) {
      var line_current_top = parseInt(line.css('top'));
      if (line_current_top > container_height) {
          line_current_top = -300;
      }
      line.css('top', line_current_top + line_speed);
  }

  restart_btn.click(function() {
      location.reload();
  });

  function stop_the_game() {
      game_over = true;
      cancelAnimationFrame(anim_id);
      cancelAnimationFrame(move_right);
      cancelAnimationFrame(move_left);
      cancelAnimationFrame(move_up);
      cancelAnimationFrame(move_down);
      restart_div.slideDown();
      restart_btn.focus();
  }

  /* ------------------------------GAME CODE ENDS HERE------------------------------------------- */


  function collision($div1, $div2) {
      var x1 = $div1.offset().left;
      var y1 = $div1.offset().top;
      var h1 = $div1.outerHeight(true);
      var w1 = $div1.outerWidth(true);
      var b1 = y1 + h1;
      var r1 = x1 + w1;
      var x2 = $div2.offset().left;
      var y2 = $div2.offset().top;
      var h2 = $div2.outerHeight(true);
      var w2 = $div2.outerWidth(true);
      var b2 = y2 + h2;
      var r2 = x2 + w2;

      if (b1 < y2 || y1 > b2 || r1 < x2 || x1 > r2) return false;
      return true;
  }



});


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Racing Game</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="script.js"></script>
</head>
<body>
    <div id="help">
        <p>Controls: Right, Left, Up & Down arrow keys.</p>
    </div>
    <div id="score_div">
        Score: <span id="score">0</span>
    </div>
    <div id="container">
        <div id="line_1" class="line"></div>
        <div id="line_2" class="line"></div>
        <div id="line_3" class="line"></div>
        <div id="car" class="car">
            <div class="f_glass"></div>
            <div class="b_glass"></div>
            <div class="f_light_l"></div>
            <div class="f_light_r"></div>
            <div class="f_tyre_l"></div>
            <div class="f_tyre_r"></div>
            <div class="b_tyre_l"></div>
            <div class="b_tyre_r"></div>
        </div>
        <div id="car_1" class="car">
            <div class="f_glass"></div>
            <div class="b_glass"></div>
            <div class="f_light_l"></div>
            <div class="f_light_r"></div>
            <div class="f_tyre_l"></div>
            <div class="f_tyre_r"></div>
            <div class="b_tyre_l"></div>
            <div class="b_tyre_r"></div>
        </div>
        <div id="car_2" class="car">
            <div class="f_glass"></div>
            <div class="b_glass"></div>
            <div class="f_light_l"></div>
            <div class="f_light_r"></div>
            <div class="f_tyre_l"></div>
            <div class="f_tyre_r"></div>
            <div class="b_tyre_l"></div>
            <div class="b_tyre_r"></div>
        </div>
        <div id="car_3" class="car">
            <div class="f_glass"></div>
            <div class="b_glass"></div>
            <div class="f_light_l"></div>
            <div class="f_light_r"></div>
            <div class="f_tyre_l"></div>
            <div class="f_tyre_r"></div>
            <div class="b_tyre_l"></div>
            <div class="b_tyre_r"></div>
        </div>
        <div id="restart_div">
            <button id="restart">
                Restart<br>
                <small class="small_text">(press Enter)</small>
            </button>
        </div>
    </div>

</body>
</html>


