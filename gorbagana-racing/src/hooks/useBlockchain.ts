import { useState, useEffect, useCallback } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { blockchainService, GAME_CONFIG } from '@/lib/blockchain';

export interface BlockchainState {
  balance: number;
  loading: boolean;
  error: string | null;
  canAffordEntryFee: boolean;
  gameWalletBalance: number;
}

export interface BlockchainActions {
  refreshBalance: () => Promise<void>;
  payEntryFee: () => Promise<{ success: boolean; signature?: string; error?: string }>;
  buyPowerUp: (powerUpType: string) => Promise<{ success: boolean; signature?: string; error?: string }>;
  airdropTokens: (amount?: number) => Promise<boolean>;
  checkPowerUpAffordability: (powerUpType: string) => Promise<boolean>;
}

export function useBlockchain(): BlockchainState & BlockchainActions {
  const { publicKey, signTransaction, connected } = useWallet();
  
  const [state, setState] = useState<BlockchainState>({
    balance: 0,
    loading: false,
    error: null,
    canAffordEntryFee: false,
    gameWalletBalance: 0,
  });

  // Refresh player balance
  const refreshBalance = useCallback(async () => {
    if (!publicKey || !connected) {
      setState(prev => ({ ...prev, balance: 0, canAffordEntryFee: false }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const [balance, gameBalance] = await Promise.all([
        blockchainService.getPlayerBalance(publicKey),
        blockchainService.getGameWalletBalance(),
      ]);
      
      setState(prev => ({
        ...prev,
        balance,
        gameWalletBalance: gameBalance,
        canAffordEntryFee: balance >= GAME_CONFIG.ENTRY_FEE,
        loading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch balance',
        loading: false,
      }));
    }
  }, [publicKey, connected]);

  // Pay entry fee for race
  const payEntryFee = useCallback(async () => {
    if (!publicKey || !signTransaction || !connected) {
      return { success: false, error: 'Wallet not connected' };
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await blockchainService.processEntryFee(publicKey, signTransaction);
      
      if (result.success) {
        // Refresh balance after successful payment
        await refreshBalance();
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Payment failed' }));
      }
      
      setState(prev => ({ ...prev, loading: false }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
      return { success: false, error: errorMessage };
    }
  }, [publicKey, signTransaction, connected, refreshBalance]);

  // Buy power-up
  const buyPowerUp = useCallback(async (powerUpType: string) => {
    if (!publicKey || !signTransaction || !connected) {
      return { success: false, error: 'Wallet not connected' };
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await blockchainService.processPowerUpPurchase(
        publicKey, 
        powerUpType, 
        signTransaction
      );
      
      if (result.success) {
        // Refresh balance after successful purchase
        await refreshBalance();
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Purchase failed' }));
      }
      
      setState(prev => ({ ...prev, loading: false }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
      return { success: false, error: errorMessage };
    }
  }, [publicKey, signTransaction, connected, refreshBalance]);

  // Airdrop tokens for testing
  const airdropTokens = useCallback(async (amount: number = 1000) => {
    if (!publicKey || !connected) {
      return false;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const success = await blockchainService.airdropTokens(publicKey, amount);
      
      if (success) {
        // Refresh balance after airdrop
        setTimeout(() => refreshBalance(), 2000); // Wait a bit for confirmation
      } else {
        setState(prev => ({ ...prev, error: 'Airdrop failed' }));
      }
      
      setState(prev => ({ ...prev, loading: false }));
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Airdrop failed';
      setState(prev => ({ ...prev, error: errorMessage, loading: false }));
      return false;
    }
  }, [publicKey, connected, refreshBalance]);

  // Check if player can afford a specific power-up
  const checkPowerUpAffordability = useCallback(async (powerUpType: string) => {
    if (!publicKey || !connected) {
      return false;
    }

    try {
      return await blockchainService.canAffordPowerUp(publicKey, powerUpType);
    } catch (error) {
      console.error('Error checking power-up affordability:', error);
      return false;
    }
  }, [publicKey, connected]);

  // Auto-refresh balance when wallet connects/disconnects
  useEffect(() => {
    if (connected && publicKey) {
      refreshBalance();
    } else {
      setState(prev => ({ 
        ...prev, 
        balance: 0, 
        canAffordEntryFee: false,
        gameWalletBalance: 0,
        error: null 
      }));
    }
  }, [connected, publicKey, refreshBalance]);

  // Periodic balance refresh (every 30 seconds when connected)
  useEffect(() => {
    if (!connected || !publicKey) return;

    const interval = setInterval(() => {
      refreshBalance();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [connected, publicKey, refreshBalance]);

  return {
    ...state,
    refreshBalance,
    payEntryFee,
    buyPowerUp,
    airdropTokens,
    checkPowerUpAffordability,
  };
}
