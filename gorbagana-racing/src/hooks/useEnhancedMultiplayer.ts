import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useWallet } from '@solana/wallet-adapter-react';
import { Player, Race, PowerUpType, RaceStatus } from '@/types/game';
import { ServerToClientEvents, ClientToServerEvents } from '@/lib/socket-server';
import { PlayerState, RaceSession } from '@/lib/multiplayer-manager';

export interface EnhancedMultiplayerState {
  // Connection
  connected: boolean;
  connecting: boolean;
  error: string | null;
  
  // Session
  currentSession: RaceSession | null;
  availableSessions: RaceSession[];
  
  // Player
  currentPlayer: PlayerState | null;
  allPlayers: PlayerState[];
  
  // Race
  raceStatus: RaceStatus;
  raceProgress: number;
  leaderboard: PlayerState[];
  
  // Performance
  latency: number;
  fps: number;
  lastHeartbeat: number;
}

export interface EnhancedMultiplayerActions {
  // Connection
  connect: () => void;
  disconnect: () => void;
  
  // Session management
  createSession: (raceData?: Partial<Race>) => void;
  joinSession: (sessionId?: string) => void;
  leaveSession: () => void;
  
  // Player actions
  setReady: (ready: boolean) => void;
  usePowerUp: (powerUpType: PowerUpType) => void;
  
  // System
  refreshSessions: () => void;
  sendHeartbeat: () => void;
}

export function useEnhancedMultiplayer(): EnhancedMultiplayerState & EnhancedMultiplayerActions {
  const { publicKey, connected: walletConnected } = useWallet();
  const socketRef = useRef<Socket<ServerToClientEvents, ClientToServerEvents> | null>(null);
  
  const [state, setState] = useState<EnhancedMultiplayerState>({
    connected: false,
    connecting: false,
    error: null,
    currentSession: null,
    availableSessions: [],
    currentPlayer: null,
    allPlayers: [],
    raceStatus: RaceStatus.WAITING,
    raceProgress: 0,
    leaderboard: [],
    latency: 0,
    fps: 0,
    lastHeartbeat: 0,
  });

  // Performance monitoring
  const [frameCount, setFrameCount] = useState(0);
  const [lastFrameTime, setLastFrameTime] = useState(Date.now());

  // Connect to server
  const connect = useCallback(() => {
    if (socketRef.current?.connected || !walletConnected || !publicKey) return;

    setState(prev => ({ ...prev, connecting: true, error: null }));

    const socket = io(process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true,
    });

    socketRef.current = socket;

    // Connection events
    socket.on('connect', () => {
      setState(prev => ({ ...prev, connected: true, connecting: false, error: null }));
      console.log('Connected to enhanced multiplayer server');
      refreshSessions();
    });

    socket.on('disconnect', (reason) => {
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        connecting: false,
        currentSession: null,
        currentPlayer: null,
        allPlayers: [],
        error: `Disconnected: ${reason}`
      }));
      console.log('Disconnected from server:', reason);
    });

    socket.on('connect_error', (error) => {
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        connecting: false, 
        error: `Connection error: ${error.message}` 
      }));
    });

    // Session events
    socket.on('sessionCreated', (session) => {
      setState(prev => ({
        ...prev,
        availableSessions: [...prev.availableSessions, session]
      }));
    });

    socket.on('sessionDestroyed', (sessionId) => {
      setState(prev => ({
        ...prev,
        availableSessions: prev.availableSessions.filter(s => s.id !== sessionId),
        currentSession: prev.currentSession?.id === sessionId ? null : prev.currentSession
      }));
    });

    // Player events
    socket.on('playerJoined', (data) => {
      setState(prev => {
        if (prev.currentSession?.id !== data.sessionId) return prev;
        
        const updatedPlayers = [...prev.allPlayers, data.player];
        return {
          ...prev,
          allPlayers: updatedPlayers,
          currentSession: prev.currentSession ? {
            ...prev.currentSession,
            players: new Map([...prev.currentSession.players, [data.player.id, data.player]])
          } : null
        };
      });
    });

    socket.on('playerLeft', (data) => {
      setState(prev => {
        if (prev.currentSession?.id !== data.sessionId) return prev;
        
        const updatedPlayers = prev.allPlayers.filter(p => p.id !== data.playerId);
        return {
          ...prev,
          allPlayers: updatedPlayers,
          currentSession: prev.currentSession ? {
            ...prev.currentSession,
            players: new Map(Array.from(prev.currentSession.players).filter(([id]) => id !== data.playerId))
          } : null
        };
      });
    });

    socket.on('playerReadyChanged', (data) => {
      setState(prev => {
        if (prev.currentSession?.id !== data.sessionId) return prev;
        
        const updatedPlayers = prev.allPlayers.map(p => 
          p.id === data.playerId ? { ...p, isReady: data.ready } : p
        );
        
        return { ...prev, allPlayers: updatedPlayers };
      });
    });

    // Race events
    socket.on('raceCountdownStarted', (data) => {
      setState(prev => ({ ...prev, raceStatus: RaceStatus.STARTING }));
    });

    socket.on('raceStarted', (data) => {
      setState(prev => ({ ...prev, raceStatus: RaceStatus.ACTIVE }));
    });

    socket.on('raceFinished', (data) => {
      setState(prev => ({ ...prev, raceStatus: RaceStatus.FINISHED }));
    });

    // Game events
    socket.on('positionUpdate', (data) => {
      setState(prev => {
        if (prev.currentSession?.id !== data.sessionId) return prev;
        
        const updatedPlayers = prev.allPlayers.map(p => 
          p.id === data.playerId 
            ? { ...p, position: data.position, velocity: data.velocity }
            : p
        );
        
        // Update current player
        const currentPlayer = data.playerId === publicKey?.toString() 
          ? updatedPlayers.find(p => p.id === data.playerId) || prev.currentPlayer
          : prev.currentPlayer;
        
        // Update leaderboard
        const leaderboard = [...updatedPlayers].sort((a, b) => b.position - a.position);
        
        return {
          ...prev,
          allPlayers: updatedPlayers,
          currentPlayer,
          leaderboard,
          raceProgress: currentPlayer ? (currentPlayer.position / 1000) * 100 : 0
        };
      });
    });

    socket.on('powerUpUsed', (data) => {
      console.log(`Player ${data.playerId} used power-up: ${data.powerUpType}`);
    });

    socket.on('collision', (data) => {
      console.log(`Player ${data.playerId} collided with obstacle`);
    });

    // System events
    socket.on('heartbeat', () => {
      setState(prev => ({ ...prev, lastHeartbeat: Date.now() }));
    });

    socket.on('error', (message) => {
      setState(prev => ({ ...prev, error: message }));
    });

    // Legacy lobby support
    socket.on('lobbyUpdate', (lobbies) => {
      const sessions = lobbies.map(lobby => ({
        id: lobby.id,
        players: new Map(lobby.players.map(p => [p.id, {
          id: p.id,
          name: p.name,
          position: p.position,
          velocity: p.speed,
          isReady: p.isReady,
          isConnected: true,
          lastHeartbeat: Date.now(),
          powerUpsActive: [],
          tokenBalance: p.tokenBalance,
          raceStats: {
            bestLap: 0,
            totalDistance: 0,
            collisions: 0,
            powerUpsUsed: 0,
          }
        }])),
        gameEngine: null as any,
        status: lobby.status === 'waiting' ? RaceStatus.WAITING :
                lobby.status === 'starting' ? RaceStatus.STARTING :
                lobby.status === 'racing' ? RaceStatus.ACTIVE : RaceStatus.FINISHED,
        startTime: 0,
        leaderboard: [],
      }));
      
      setState(prev => ({ ...prev, availableSessions: sessions }));
    });

  }, [walletConnected, publicKey]);

  // Disconnect from server
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
  }, []);

  // Create new session
  const createSession = useCallback((raceData?: Partial<Race>) => {
    if (!socketRef.current?.connected) return;

    const defaultRace: Race = {
      id: `race-${Date.now()}`,
      name: 'Custom Race',
      players: [],
      maxPlayers: 8,
      entryFee: 50,
      prizePool: 0,
      status: RaceStatus.WAITING,
      startTime: new Date(),
      duration: 300000,
      track: {
        id: 'track-1',
        name: 'Gorbagana Circuit',
        length: 1000,
        difficulty: 2,
        obstacles: [],
        shortcuts: [],
      },
      ...raceData,
    };

    socketRef.current.emit('createSession', defaultRace);
  }, []);

  // Join session
  const joinSession = useCallback((sessionId?: string) => {
    if (!socketRef.current?.connected || !publicKey) return;

    socketRef.current.emit('joinSession', {
      sessionId,
      playerData: {
        publicKey: publicKey.toString(),
        name: `Player ${publicKey.toString().slice(0, 8)}`,
        tokenBalance: 1000,
      }
    });
  }, [publicKey]);

  // Leave session
  const leaveSession = useCallback(() => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit('leaveSession');
    setState(prev => ({
      ...prev,
      currentSession: null,
      currentPlayer: null,
      allPlayers: [],
      raceStatus: RaceStatus.WAITING,
    }));
  }, []);

  // Set ready status
  const setReady = useCallback((ready: boolean) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit('setReady', ready);
  }, []);

  // Use power-up
  const usePowerUp = useCallback((powerUpType: PowerUpType) => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit('usePowerUp', powerUpType);
  }, []);

  // Refresh sessions
  const refreshSessions = useCallback(() => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit('requestSessions');
    socketRef.current.emit('requestLobbies'); // Legacy support
  }, []);

  // Send heartbeat
  const sendHeartbeat = useCallback(() => {
    if (!socketRef.current?.connected) return;

    socketRef.current.emit('heartbeat');
  }, []);

  // Auto-connect when wallet connects
  useEffect(() => {
    if (walletConnected && publicKey) {
      connect();
    } else {
      disconnect();
    }

    return () => disconnect();
  }, [walletConnected, publicKey, connect, disconnect]);

  // Heartbeat interval
  useEffect(() => {
    if (!state.connected) return;

    const interval = setInterval(sendHeartbeat, 5000);
    return () => clearInterval(interval);
  }, [state.connected, sendHeartbeat]);

  // Performance monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const deltaTime = now - lastFrameTime;
      const fps = Math.round(1000 / deltaTime);
      
      setFrameCount(prev => prev + 1);
      setLastFrameTime(now);
      
      setState(prev => ({ ...prev, fps: Math.min(fps, 60) }));
    }, 100);

    return () => clearInterval(interval);
  }, [lastFrameTime]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        setState(prev => ({ ...prev, error: null }));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [state.error]);

  return {
    ...state,
    connect,
    disconnect,
    createSession,
    joinSession,
    leaveSession,
    setReady,
    usePowerUp,
    refreshSessions,
    sendHeartbeat,
  };
}
