import { Player, Race, RaceStatus, PowerUp, PowerUpType, RaceEvent, Track, Obstacle } from '@/types/game';

export interface PhysicsState {
  velocity: number;
  acceleration: number;
  friction: number;
  maxSpeed: number;
  momentum: number;
}

export interface CollisionResult {
  hasCollision: boolean;
  obstacle?: Obstacle;
  speedPenalty: number;
  positionAdjustment: number;
}

export class GameEngine {
  private race: Race | null = null;
  private gameLoop: NodeJS.Timeout | null = null;
  private eventCallbacks: ((event: RaceEvent) => void)[] = [];
  private physicsStates: Map<string, PhysicsState> = new Map();
  private lastUpdateTime: number = 0;
  private raceStartTime: number = 0;

  // Physics constants
  private readonly PHYSICS_CONFIG = {
    BASE_ACCELERATION: 0.02,
    MAX_SPEED: 2.5,
    FRICTION: 0.98,
    POWER_UP_MULTIPLIERS: {
      [PowerUpType.SPEED_BOOST]: 1.8,
      [PowerUpType.SHIELD]: 1.0,
      [PowerUpType.SHORTCUT]: 1.0,
      [PowerUpType.DOUBLE_TOKENS]: 1.0,
    },
    OBSTACLE_PENALTIES: {
      SLOW: 0.7,
      MEDIUM: 0.5,
      HARD: 0.3,
    },
    COLLISION_RECOVERY_TIME: 2000, // 2 seconds
  };

  constructor() {
    this.race = null;
    this.lastUpdateTime = Date.now();
  }

  // Initialize a new race
  initializeRace(race: Race): void {
    this.race = race;
    this.race.status = RaceStatus.WAITING;
    this.physicsStates.clear();
    this.generateTrackFeatures();
  }

  // Generate dynamic track features
  private generateTrackFeatures(): void {
    if (!this.race?.track) return;

    // Generate obstacles
    this.race.track.obstacles = [];
    const numObstacles = Math.floor(this.race.track.length / 200) + 2; // One obstacle per 200 units

    for (let i = 0; i < numObstacles; i++) {
      const position = (i + 1) * (this.race.track.length / (numObstacles + 1));
      const obstacleTypes = ['SLOW', 'MEDIUM', 'HARD'];
      const type = obstacleTypes[Math.floor(Math.random() * obstacleTypes.length)];

      this.race.track.obstacles.push({
        id: `obstacle-${i}`,
        position: position + (Math.random() - 0.5) * 50, // Add some randomness
        type,
        effect: this.PHYSICS_CONFIG.OBSTACLE_PENALTIES[type as keyof typeof this.PHYSICS_CONFIG.OBSTACLE_PENALTIES],
      });
    }

    // Generate shortcuts
    this.race.track.shortcuts = [];
    const numShortcuts = Math.floor(this.race.track.length / 400) + 1; // One shortcut per 400 units

    for (let i = 0; i < numShortcuts; i++) {
      const startPosition = (i + 1) * (this.race.track.length / (numShortcuts + 1));
      const shortcutLength = 50 + Math.random() * 100; // 50-150 units

      this.race.track.shortcuts.push({
        id: `shortcut-${i}`,
        startPosition: startPosition + (Math.random() - 0.5) * 100,
        endPosition: startPosition + shortcutLength,
        cost: 25, // GORB tokens
      });
    }
  }

  // Add player to race
  addPlayer(player: Player): boolean {
    if (!this.race || this.race.players.length >= this.race.maxPlayers) {
      return false;
    }

    // Initialize physics state for player
    this.physicsStates.set(player.id, {
      velocity: 0,
      acceleration: 0,
      friction: this.PHYSICS_CONFIG.FRICTION,
      maxSpeed: this.PHYSICS_CONFIG.MAX_SPEED,
      momentum: 0,
    });

    this.race.players.push(player);
    this.emitEvent({
      type: 'player_joined',
      playerId: player.id,
      data: { player },
      timestamp: new Date(),
    });

    return true;
  }

  // Remove player from race
  removePlayer(playerId: string): boolean {
    if (!this.race) return false;

    const playerIndex = this.race.players.findIndex(p => p.id === playerId);
    if (playerIndex === -1) return false;

    // Clean up physics state
    this.physicsStates.delete(playerId);

    this.race.players.splice(playerIndex, 1);
    this.emitEvent({
      type: 'player_left',
      playerId,
      data: {},
      timestamp: new Date(),
    });

    return true;
  }

  // Start the race
  startRace(): boolean {
    if (!this.race || this.race.players.length < 2) {
      return false;
    }

    this.race.status = RaceStatus.STARTING;
    this.race.startTime = new Date();
    this.raceStartTime = Date.now();
    this.lastUpdateTime = Date.now();

    // Initialize player positions and physics
    this.race.players.forEach(player => {
      player.position = 0;
      player.speed = 1; // Base speed

      // Reset physics state
      const physicsState = this.physicsStates.get(player.id);
      if (physicsState) {
        physicsState.velocity = 0;
        physicsState.acceleration = 0;
        physicsState.momentum = 0;
      }
    });

    // Start countdown and then begin race
    setTimeout(() => {
      if (this.race) {
        this.race.status = RaceStatus.ACTIVE;
        this.startGameLoop();
      }
    }, 3000); // 3 second countdown

    return true;
  }

  // Start the main game loop
  private startGameLoop(): void {
    this.gameLoop = setInterval(() => {
      this.updateRace();
    }, 50); // Update every 50ms for smoother physics
  }

  // Update race state with advanced physics
  private updateRace(): void {
    if (!this.race || this.race.status !== RaceStatus.ACTIVE) {
      return;
    }

    const currentTime = Date.now();
    const deltaTime = currentTime - this.lastUpdateTime;
    this.lastUpdateTime = currentTime;

    // Update each player with physics simulation
    this.race.players.forEach(player => {
      this.updatePlayerPhysics(player, deltaTime);

      // Check for race completion
      if (player.position >= this.race!.track.length) {
        this.finishRace(player);
        return;
      }

      // Emit position update
      this.emitEvent({
        type: 'position_update',
        playerId: player.id,
        data: {
          position: player.position,
          speed: player.speed,
          velocity: this.physicsStates.get(player.id)?.velocity || 0
        },
        timestamp: new Date(),
      });
    });

    // Update power-up durations
    this.updatePowerUps();
  }

  // Advanced physics simulation for player movement
  private updatePlayerPhysics(player: Player, deltaTime: number): void {
    const physicsState = this.physicsStates.get(player.id);
    if (!physicsState) return;

    // Calculate base acceleration
    let acceleration = this.PHYSICS_CONFIG.BASE_ACCELERATION;

    // Apply power-up effects to acceleration and max speed
    let speedMultiplier = 1;
    let maxSpeedBonus = 0;

    player.powerUps.forEach(powerUp => {
      const multiplier = this.PHYSICS_CONFIG.POWER_UP_MULTIPLIERS[powerUp.type];
      if (powerUp.type === PowerUpType.SPEED_BOOST) {
        speedMultiplier *= multiplier;
        maxSpeedBonus += 0.5;
      }
    });

    acceleration *= speedMultiplier;
    const currentMaxSpeed = physicsState.maxSpeed + maxSpeedBonus;

    // Apply acceleration
    physicsState.velocity += acceleration * (deltaTime / 16.67); // Normalize to 60fps

    // Apply friction
    physicsState.velocity *= physicsState.friction;

    // Cap velocity to max speed
    physicsState.velocity = Math.min(physicsState.velocity, currentMaxSpeed);

    // Check for collisions with obstacles
    const collision = this.checkCollisions(player);
    if (collision.hasCollision) {
      physicsState.velocity *= collision.speedPenalty;
      player.position += collision.positionAdjustment;

      // Emit collision event
      this.emitEvent({
        type: 'collision',
        playerId: player.id,
        data: { obstacle: collision.obstacle },
        timestamp: new Date(),
      });
    }

    // Update position based on velocity
    const movement = physicsState.velocity * (deltaTime / 16.67);
    player.position += movement;

    // Update player speed for display
    player.speed = physicsState.velocity;

    // Add momentum for more realistic physics
    physicsState.momentum = physicsState.velocity * 0.1;
  }

  // Advanced collision detection
  private checkCollisions(player: Player): CollisionResult {
    if (!this.race?.track.obstacles) {
      return { hasCollision: false, speedPenalty: 1, positionAdjustment: 0 };
    }

    // Check if player has shield power-up
    const hasShield = player.powerUps.some(p => p.type === PowerUpType.SHIELD);
    if (hasShield) {
      return { hasCollision: false, speedPenalty: 1, positionAdjustment: 0 };
    }

    // Check collision with obstacles
    for (const obstacle of this.race.track.obstacles) {
      const distance = Math.abs(player.position - obstacle.position);
      const collisionRadius = 20; // Collision detection radius

      if (distance < collisionRadius) {
        return {
          hasCollision: true,
          obstacle,
          speedPenalty: obstacle.effect,
          positionAdjustment: -10, // Push player back slightly
        };
      }
    }

    return { hasCollision: false, speedPenalty: 1, positionAdjustment: 0 };
  }

  // Check if player can use a shortcut
  private checkShortcuts(player: Player): boolean {
    if (!this.race?.track.shortcuts) return false;

    for (const shortcut of this.race.track.shortcuts) {
      const distance = Math.abs(player.position - shortcut.startPosition);
      if (distance < 30) { // Within shortcut activation range
        return true;
      }
    }

    return false;
  }

  // Apply shortcut effect
  private useShortcut(player: Player): boolean {
    if (!this.race?.track.shortcuts) return false;

    for (const shortcut of this.race.track.shortcuts) {
      const distance = Math.abs(player.position - shortcut.startPosition);
      if (distance < 30) {
        // Jump to end of shortcut
        const shortcutDistance = shortcut.endPosition - shortcut.startPosition;
        player.position = shortcut.endPosition;

        this.emitEvent({
          type: 'shortcut_used',
          playerId: player.id,
          data: { shortcut, distance: shortcutDistance },
          timestamp: new Date(),
        });

        return true;
      }
    }

    return false;
  }

  // Update power-up durations
  private updatePowerUps(): void {
    if (!this.race) return;

    this.race.players.forEach(player => {
      player.powerUps = player.powerUps.filter(powerUp => {
        powerUp.duration -= 100; // Decrease by 100ms
        return powerUp.duration > 0;
      });
    });
  }

  // Use a power-up with enhanced effects
  usePowerUp(playerId: string, powerUpType: PowerUpType): boolean {
    if (!this.race) return false;

    const player = this.race.players.find(p => p.id === playerId);
    if (!player) return false;

    // Check if player can afford the power-up
    const powerUpCost = this.getPowerUpCost(powerUpType);
    if (player.tokenBalance < powerUpCost) return false;

    // Deduct tokens
    player.tokenBalance -= powerUpCost;

    // Apply power-up with immediate effects
    const powerUp: PowerUp = {
      id: `${playerId}-${Date.now()}`,
      type: powerUpType,
      duration: this.getPowerUpDuration(powerUpType),
      cost: powerUpCost,
      effect: this.getPowerUpEffect(powerUpType),
    };

    // Apply immediate effects based on power-up type
    switch (powerUpType) {
      case PowerUpType.SPEED_BOOST:
        // Immediate velocity boost
        const physicsState = this.physicsStates.get(playerId);
        if (physicsState) {
          physicsState.velocity += 0.5;
        }
        break;

      case PowerUpType.SHORTCUT:
        // Try to use shortcut immediately
        if (this.checkShortcuts(player)) {
          this.useShortcut(player);
          // Shortcut is instant, so don't add to ongoing power-ups
          this.emitEvent({
            type: 'power_up_used',
            playerId,
            data: { powerUp },
            timestamp: new Date(),
          });
          return true;
        }
        break;

      case PowerUpType.SHIELD:
        // Shield provides collision immunity
        break;

      case PowerUpType.DOUBLE_TOKENS:
        // Double token rewards for duration
        break;
    }

    player.powerUps.push(powerUp);

    this.emitEvent({
      type: 'power_up_used',
      playerId,
      data: { powerUp },
      timestamp: new Date(),
    });

    return true;
  }

  // Get power-up cost
  private getPowerUpCost(type: PowerUpType): number {
    switch (type) {
      case PowerUpType.SPEED_BOOST:
        return 10;
      case PowerUpType.SHIELD:
        return 15;
      case PowerUpType.SHORTCUT:
        return 25;
      case PowerUpType.DOUBLE_TOKENS:
        return 20;
      default:
        return 10;
    }
  }

  // Get power-up duration
  private getPowerUpDuration(type: PowerUpType): number {
    switch (type) {
      case PowerUpType.SPEED_BOOST:
        return 5000; // 5 seconds
      case PowerUpType.SHIELD:
        return 8000; // 8 seconds
      case PowerUpType.SHORTCUT:
        return 1000; // Instant
      case PowerUpType.DOUBLE_TOKENS:
        return 10000; // 10 seconds
      default:
        return 5000;
    }
  }

  // Get power-up effect
  private getPowerUpEffect(type: PowerUpType): number {
    switch (type) {
      case PowerUpType.SPEED_BOOST:
        return 0.5; // 50% speed increase
      case PowerUpType.SHIELD:
        return 1; // Immunity to obstacles
      case PowerUpType.SHORTCUT:
        return 0.2; // Skip 20% of track
      case PowerUpType.DOUBLE_TOKENS:
        return 2; // Double token rewards
      default:
        return 0;
    }
  }

  // Finish the race
  private finishRace(winner: Player): void {
    if (!this.race) return;

    this.race.status = RaceStatus.FINISHED;
    this.race.winner = winner;

    // Stop game loop
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
      this.gameLoop = null;
    }

    // Calculate and distribute prizes
    this.distributePrizes();

    this.emitEvent({
      type: 'race_finished',
      playerId: winner.id,
      data: { winner, race: this.race },
      timestamp: new Date(),
    });
  }

  // Distribute prize tokens
  private distributePrizes(): void {
    if (!this.race || !this.race.winner) return;

    // Winner gets 70% of prize pool
    const winnerPrize = Math.floor(this.race.prizePool * 0.7);
    this.race.winner.tokenBalance += winnerPrize;

    // Second place gets 20% (if exists)
    const sortedPlayers = this.race.players.sort((a, b) => b.position - a.position);
    if (sortedPlayers.length > 1) {
      const secondPlace = sortedPlayers[1];
      const secondPrize = Math.floor(this.race.prizePool * 0.2);
      secondPlace.tokenBalance += secondPrize;
    }

    // Third place gets 10% (if exists)
    if (sortedPlayers.length > 2) {
      const thirdPlace = sortedPlayers[2];
      const thirdPrize = Math.floor(this.race.prizePool * 0.1);
      thirdPlace.tokenBalance += thirdPrize;
    }
  }

  // Add event listener
  onEvent(callback: (event: RaceEvent) => void): void {
    this.eventCallbacks.push(callback);
  }

  // Remove event listener
  offEvent(callback: (event: RaceEvent) => void): void {
    const index = this.eventCallbacks.indexOf(callback);
    if (index > -1) {
      this.eventCallbacks.splice(index, 1);
    }
  }

  // Emit event to all listeners
  private emitEvent(event: RaceEvent): void {
    this.eventCallbacks.forEach(callback => callback(event));
  }

  // Get current race state
  getRace(): Race | null {
    return this.race;
  }

  // Get player performance analytics
  getPlayerAnalytics(playerId: string): any {
    const player = this.race?.players.find(p => p.id === playerId);
    const physicsState = this.physicsStates.get(playerId);

    if (!player || !physicsState) return null;

    const raceTime = Date.now() - this.raceStartTime;
    const averageSpeed = player.position / (raceTime / 1000); // units per second
    const currentRank = this.race?.players
      .sort((a, b) => b.position - a.position)
      .findIndex(p => p.id === playerId) + 1 || 0;

    return {
      position: player.position,
      velocity: physicsState.velocity,
      averageSpeed,
      currentRank,
      totalPlayers: this.race?.players.length || 0,
      powerUpsUsed: player.powerUps.length,
      raceTime,
      progressPercentage: (player.position / (this.race?.track.length || 1000)) * 100,
    };
  }

  // Get race leaderboard
  getLeaderboard(): Player[] {
    if (!this.race) return [];

    return [...this.race.players].sort((a, b) => b.position - a.position);
  }

  // Get race statistics
  getRaceStats(): any {
    if (!this.race) return null;

    const raceTime = Date.now() - this.raceStartTime;
    const leaderboard = this.getLeaderboard();
    const leader = leaderboard[0];

    return {
      raceId: this.race.id,
      status: this.race.status,
      raceTime,
      totalPlayers: this.race.players.length,
      leader: leader ? {
        id: leader.id,
        name: leader.name,
        position: leader.position,
        progressPercentage: (leader.position / this.race.track.length) * 100,
      } : null,
      trackLength: this.race.track.length,
      obstacles: this.race.track.obstacles?.length || 0,
      shortcuts: this.race.track.shortcuts?.length || 0,
    };
  }

  // Pause/Resume race
  pauseRace(): boolean {
    if (!this.race || this.race.status !== RaceStatus.ACTIVE) return false;

    if (this.gameLoop) {
      clearInterval(this.gameLoop);
      this.gameLoop = null;
    }

    this.emitEvent({
      type: 'race_paused',
      playerId: 'system',
      data: {},
      timestamp: new Date(),
    });

    return true;
  }

  resumeRace(): boolean {
    if (!this.race || this.race.status !== RaceStatus.ACTIVE) return false;

    this.lastUpdateTime = Date.now();
    this.startGameLoop();

    this.emitEvent({
      type: 'race_resumed',
      playerId: 'system',
      data: {},
      timestamp: new Date(),
    });

    return true;
  }

  // Clean up
  destroy(): void {
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
      this.gameLoop = null;
    }
    this.eventCallbacks = [];
    this.physicsStates.clear();
    this.race = null;
  }
}
