import { EventEmitter } from 'events';
import { Player, Race, RaceStatus, PowerUpType, RaceEvent } from '@/types/game';
import { GameEngine } from '@/lib/game-engine';

export interface MultiplayerConfig {
  maxPlayersPerRace: number;
  raceCountdownTime: number;
  raceTimeout: number;
  reconnectionAttempts: number;
  heartbeatInterval: number;
}

export interface PlayerState {
  id: string;
  name: string;
  position: number;
  velocity: number;
  isReady: boolean;
  isConnected: boolean;
  lastHeartbeat: number;
  powerUpsActive: PowerUpType[];
  tokenBalance: number;
  raceStats: {
    bestLap: number;
    totalDistance: number;
    collisions: number;
    powerUpsUsed: number;
  };
}

export interface RaceSession {
  id: string;
  players: Map<string, PlayerState>;
  gameEngine: GameEngine;
  status: RaceStatus;
  startTime: number;
  endTime?: number;
  winner?: string;
  leaderboard: string[];
}

export class MultiplayerManager extends EventEmitter {
  private config: MultiplayerConfig;
  private activeSessions: Map<string, RaceSession> = new Map();
  private playerSessions: Map<string, string> = new Map(); // playerId -> sessionId
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<MultiplayerConfig> = {}) {
    super();
    
    this.config = {
      maxPlayersPerRace: 8,
      raceCountdownTime: 3000,
      raceTimeout: 300000, // 5 minutes
      reconnectionAttempts: 3,
      heartbeatInterval: 5000,
      ...config,
    };

    this.startHeartbeat();
  }

  // Create a new race session
  createRaceSession(raceId: string, raceData: Race): RaceSession {
    const gameEngine = new GameEngine();
    gameEngine.initializeRace(raceData);

    const session: RaceSession = {
      id: raceId,
      players: new Map(),
      gameEngine,
      status: RaceStatus.WAITING,
      startTime: 0,
      leaderboard: [],
    };

    // Set up game engine event handlers
    gameEngine.onEvent((event: RaceEvent) => {
      this.handleGameEngineEvent(raceId, event);
    });

    this.activeSessions.set(raceId, session);
    this.emit('sessionCreated', { sessionId: raceId, session });

    return session;
  }

  // Add player to race session
  addPlayerToSession(sessionId: string, playerData: Partial<PlayerState>): boolean {
    const session = this.activeSessions.get(sessionId);
    if (!session || session.players.size >= this.config.maxPlayersPerRace) {
      return false;
    }

    const player: PlayerState = {
      id: playerData.id || '',
      name: playerData.name || 'Anonymous',
      position: 0,
      velocity: 0,
      isReady: false,
      isConnected: true,
      lastHeartbeat: Date.now(),
      powerUpsActive: [],
      tokenBalance: playerData.tokenBalance || 1000,
      raceStats: {
        bestLap: 0,
        totalDistance: 0,
        collisions: 0,
        powerUpsUsed: 0,
      },
    };

    session.players.set(player.id, player);
    this.playerSessions.set(player.id, sessionId);

    // Add to game engine
    const gamePlayer: Player = {
      id: player.id,
      publicKey: player.id as any, // Mock for now
      name: player.name,
      position: player.position,
      speed: player.velocity,
      tokenBalance: player.tokenBalance,
      powerUps: [],
      isReady: player.isReady,
      avatar: '🏎️',
    };

    session.gameEngine.addPlayer(gamePlayer);

    this.emit('playerJoined', { sessionId, playerId: player.id, player });
    return true;
  }

  // Remove player from session
  removePlayerFromSession(playerId: string): boolean {
    const sessionId = this.playerSessions.get(playerId);
    if (!sessionId) return false;

    const session = this.activeSessions.get(sessionId);
    if (!session) return false;

    session.players.delete(playerId);
    this.playerSessions.delete(playerId);
    session.gameEngine.removePlayer(playerId);

    this.emit('playerLeft', { sessionId, playerId });

    // Clean up empty sessions
    if (session.players.size === 0) {
      this.destroySession(sessionId);
    }

    return true;
  }

  // Set player ready status
  setPlayerReady(playerId: string, ready: boolean = true): boolean {
    const sessionId = this.playerSessions.get(playerId);
    if (!sessionId) return false;

    const session = this.activeSessions.get(sessionId);
    const player = session?.players.get(playerId);
    if (!session || !player) return false;

    player.isReady = ready;

    // Check if all players are ready
    const allReady = Array.from(session.players.values()).every(p => p.isReady);
    const minPlayers = 2;

    if (allReady && session.players.size >= minPlayers && session.status === RaceStatus.WAITING) {
      this.startRaceCountdown(sessionId);
    }

    this.emit('playerReadyChanged', { sessionId, playerId, ready });
    return true;
  }

  // Start race countdown
  private startRaceCountdown(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.status = RaceStatus.STARTING;
    this.emit('raceCountdownStarted', { sessionId, countdown: this.config.raceCountdownTime });

    setTimeout(() => {
      this.startRace(sessionId);
    }, this.config.raceCountdownTime);
  }

  // Start the race
  private startRace(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.status = RaceStatus.ACTIVE;
    session.startTime = Date.now();
    session.gameEngine.startRace();

    this.emit('raceStarted', { sessionId });

    // Set race timeout
    setTimeout(() => {
      if (session.status === RaceStatus.ACTIVE) {
        this.endRace(sessionId, 'timeout');
      }
    }, this.config.raceTimeout);
  }

  // End the race
  private endRace(sessionId: string, reason: 'finished' | 'timeout' | 'cancelled'): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.status = RaceStatus.FINISHED;
    session.endTime = Date.now();

    // Calculate final leaderboard
    const players = Array.from(session.players.values());
    session.leaderboard = players
      .sort((a, b) => b.position - a.position)
      .map(p => p.id);

    if (session.leaderboard.length > 0) {
      session.winner = session.leaderboard[0];
    }

    this.emit('raceFinished', { sessionId, reason, leaderboard: session.leaderboard, winner: session.winner });

    // Clean up after delay
    setTimeout(() => {
      this.destroySession(sessionId);
    }, 30000); // 30 seconds
  }

  // Use power-up
  usePowerUp(playerId: string, powerUpType: PowerUpType): boolean {
    const sessionId = this.playerSessions.get(playerId);
    if (!sessionId) return false;

    const session = this.activeSessions.get(sessionId);
    const player = session?.players.get(playerId);
    if (!session || !player || session.status !== RaceStatus.ACTIVE) return false;

    // Use power-up in game engine
    const success = session.gameEngine.usePowerUp(playerId, powerUpType);
    
    if (success) {
      player.powerUpsActive.push(powerUpType);
      player.raceStats.powerUpsUsed++;

      // Remove power-up after duration
      setTimeout(() => {
        const index = player.powerUpsActive.indexOf(powerUpType);
        if (index > -1) {
          player.powerUpsActive.splice(index, 1);
        }
      }, this.getPowerUpDuration(powerUpType));

      this.emit('powerUpUsed', { sessionId, playerId, powerUpType });
    }

    return success;
  }

  // Handle game engine events
  private handleGameEngineEvent(sessionId: string, event: RaceEvent): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    switch (event.type) {
      case 'position_update':
        this.updatePlayerPosition(sessionId, event.playerId, event.data);
        break;
      case 'race_finished':
        this.endRace(sessionId, 'finished');
        break;
      case 'collision':
        this.handleCollision(sessionId, event.playerId, event.data);
        break;
    }

    // Emit to clients
    this.emit('gameEvent', { sessionId, event });
  }

  // Update player position
  private updatePlayerPosition(sessionId: string, playerId: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    const player = session?.players.get(playerId);
    if (!session || !player) return;

    player.position = data.position;
    player.velocity = data.velocity || data.speed;
    player.raceStats.totalDistance = Math.max(player.raceStats.totalDistance, player.position);

    this.emit('positionUpdate', { sessionId, playerId, position: player.position, velocity: player.velocity });
  }

  // Handle collision
  private handleCollision(sessionId: string, playerId: string, data: any): void {
    const session = this.activeSessions.get(sessionId);
    const player = session?.players.get(playerId);
    if (!session || !player) return;

    player.raceStats.collisions++;
    this.emit('collision', { sessionId, playerId, obstacle: data.obstacle });
  }

  // Update player heartbeat
  updatePlayerHeartbeat(playerId: string): void {
    const sessionId = this.playerSessions.get(playerId);
    const session = this.activeSessions.get(sessionId || '');
    const player = session?.players.get(playerId);
    
    if (player) {
      player.lastHeartbeat = Date.now();
      player.isConnected = true;
    }
  }

  // Start heartbeat monitoring
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = Date.now();
      const timeout = this.config.heartbeatInterval * 3; // 3x heartbeat interval

      for (const [sessionId, session] of this.activeSessions) {
        for (const [playerId, player] of session.players) {
          if (now - player.lastHeartbeat > timeout) {
            player.isConnected = false;
            this.emit('playerDisconnected', { sessionId, playerId });
            
            // Remove player after extended timeout
            if (now - player.lastHeartbeat > timeout * 2) {
              this.removePlayerFromSession(playerId);
            }
          }
        }
      }
    }, this.config.heartbeatInterval);
  }

  // Get power-up duration
  private getPowerUpDuration(powerUpType: PowerUpType): number {
    switch (powerUpType) {
      case PowerUpType.SPEED_BOOST: return 5000;
      case PowerUpType.SHIELD: return 8000;
      case PowerUpType.SHORTCUT: return 1000;
      case PowerUpType.DOUBLE_TOKENS: return 10000;
      default: return 5000;
    }
  }

  // Get session info
  getSession(sessionId: string): RaceSession | undefined {
    return this.activeSessions.get(sessionId);
  }

  // Get player session
  getPlayerSession(playerId: string): RaceSession | undefined {
    const sessionId = this.playerSessions.get(playerId);
    return sessionId ? this.activeSessions.get(sessionId) : undefined;
  }

  // Get all active sessions
  getAllSessions(): RaceSession[] {
    return Array.from(this.activeSessions.values());
  }

  // Destroy session
  private destroySession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Remove all players
    for (const playerId of session.players.keys()) {
      this.playerSessions.delete(playerId);
    }

    // Clean up game engine
    session.gameEngine.destroy();

    this.activeSessions.delete(sessionId);
    this.emit('sessionDestroyed', { sessionId });
  }

  // Clean up
  destroy(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    for (const sessionId of this.activeSessions.keys()) {
      this.destroySession(sessionId);
    }

    this.removeAllListeners();
  }
}
