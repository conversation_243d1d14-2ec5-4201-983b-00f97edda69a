import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { Player, Race, RaceStatus, RaceEvent, PowerUpType } from '@/types/game';
import { Multiplayer<PERSON>anager, PlayerState, RaceSession } from '@/lib/multiplayer-manager';

export interface ServerToClientEvents {
  // Session events
  sessionCreated: (session: RaceSession) => void;
  sessionDestroyed: (sessionId: string) => void;

  // Player events
  playerJoined: (data: { sessionId: string; playerId: string; player: PlayerState }) => void;
  playerLeft: (data: { sessionId: string; playerId: string }) => void;
  playerReadyChanged: (data: { sessionId: string; playerId: string; ready: boolean }) => void;
  playerDisconnected: (data: { sessionId: string; playerId: string }) => void;

  // Race events
  raceCountdownStarted: (data: { sessionId: string; countdown: number }) => void;
  raceStarted: (data: { sessionId: string }) => void;
  raceFinished: (data: { sessionId: string; reason: string; leaderboard: string[]; winner?: string }) => void;

  // Game events
  positionUpdate: (data: { sessionId: string; playerId: string; position: number; velocity: number }) => void;
  powerUpUsed: (data: { sessionId: string; playerId: string; powerUpType: PowerUpType }) => void;
  collision: (data: { sessionId: string; playerId: string; obstacle: any }) => void;
  gameEvent: (data: { sessionId: string; event: RaceEvent }) => void;

  // System events
  error: (message: string) => void;
  heartbeat: () => void;
  lobbyUpdate: (lobbies: RaceLobby[]) => void;
}

export interface ClientToServerEvents {
  // Session management
  createSession: (raceData: Race) => void;
  joinSession: (data: { sessionId?: string; playerData: { publicKey: string; name: string; tokenBalance?: number } }) => void;
  leaveSession: () => void;

  // Player actions
  setReady: (ready: boolean) => void;
  usePowerUp: (powerUpType: PowerUpType) => void;

  // System
  heartbeat: () => void;
  requestSessions: () => void;

  // Legacy lobby support
  joinLobby: (playerData: { publicKey: string; name: string }) => void;
  leaveLobby: () => void;
  playerReady: () => void;
  requestLobbies: () => void;
}

export interface RaceLobby {
  id: string;
  name: string;
  players: Player[];
  maxPlayers: number;
  entryFee: number;
  status: 'waiting' | 'starting' | 'racing' | 'finished';
  gameEngine?: GameEngine;
}

export class MultiplayerGameServer {
  private io: SocketIOServer<ClientToServerEvents, ServerToClientEvents>;
  private multiplayerManager: MultiplayerManager;
  private playerSockets: Map<string, string> = new Map(); // playerId -> socketId
  private socketPlayers: Map<string, string> = new Map(); // socketId -> playerId
  private lobbies: Map<string, RaceLobby> = new Map(); // Legacy lobby support

  constructor(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000'],
        methods: ['GET', 'POST'],
      },
    });

    this.multiplayerManager = new MultiplayerManager({
      maxPlayersPerRace: 8,
      raceCountdownTime: 3000,
      raceTimeout: 300000,
      heartbeatInterval: 5000,
    });

    this.setupEventHandlers();
    this.setupMultiplayerManagerEvents();
    this.createDefaultLobbies();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`Player connected: ${socket.id}`);

      // Send current lobbies to new player
      socket.emit('lobbyUpdate', Array.from(this.lobbies.values()));

      // Enhanced session management
      socket.on('createSession', (raceData) => {
        this.handleCreateSession(socket.id, raceData);
      });

      socket.on('joinSession', (data) => {
        this.handleJoinSession(socket.id, data);
      });

      socket.on('leaveSession', () => {
        this.handleLeaveSession(socket.id);
      });

      socket.on('setReady', (ready) => {
        this.handleSetReady(socket.id, ready);
      });

      socket.on('usePowerUp', (powerUpType) => {
        this.handleUsePowerUp(socket.id, powerUpType);
      });

      socket.on('heartbeat', () => {
        this.handleHeartbeat(socket.id);
      });

      socket.on('requestSessions', () => {
        this.handleRequestSessions(socket.id);
      });

      // Legacy lobby support
      socket.on('joinLobby', (playerData) => {
        this.handleJoinLobby(socket.id, playerData);
      });

      socket.on('leaveLobby', () => {
        this.handleLeaveLobby(socket.id);
      });

      socket.on('playerReady', () => {
        this.handlePlayerReady(socket.id);
      });

      socket.on('requestLobbies', () => {
        socket.emit('lobbyUpdate', Array.from(this.lobbies.values()));
      });

      socket.on('disconnect', () => {
        console.log(`Player disconnected: ${socket.id}`);
        this.handleDisconnect(socket.id);
      });
    });
  }

  // Set up multiplayer manager event handlers
  private setupMultiplayerManagerEvents() {
    this.multiplayerManager.on('sessionCreated', (data) => {
      this.io.emit('sessionCreated', data.session);
    });

    this.multiplayerManager.on('sessionDestroyed', (data) => {
      this.io.emit('sessionDestroyed', data.sessionId);
    });

    this.multiplayerManager.on('playerJoined', (data) => {
      this.io.to(data.sessionId).emit('playerJoined', data);
    });

    this.multiplayerManager.on('playerLeft', (data) => {
      this.io.to(data.sessionId).emit('playerLeft', data);
    });

    this.multiplayerManager.on('playerReadyChanged', (data) => {
      this.io.to(data.sessionId).emit('playerReadyChanged', data);
    });

    this.multiplayerManager.on('playerDisconnected', (data) => {
      this.io.to(data.sessionId).emit('playerDisconnected', data);
    });

    this.multiplayerManager.on('raceCountdownStarted', (data) => {
      this.io.to(data.sessionId).emit('raceCountdownStarted', data);
    });

    this.multiplayerManager.on('raceStarted', (data) => {
      this.io.to(data.sessionId).emit('raceStarted', data);
    });

    this.multiplayerManager.on('raceFinished', (data) => {
      this.io.to(data.sessionId).emit('raceFinished', data);
    });

    this.multiplayerManager.on('positionUpdate', (data) => {
      this.io.to(data.sessionId).emit('positionUpdate', data);
    });

    this.multiplayerManager.on('powerUpUsed', (data) => {
      this.io.to(data.sessionId).emit('powerUpUsed', data);
    });

    this.multiplayerManager.on('collision', (data) => {
      this.io.to(data.sessionId).emit('collision', data);
    });

    this.multiplayerManager.on('gameEvent', (data) => {
      this.io.to(data.sessionId).emit('gameEvent', data);
    });
  }

  // Enhanced session handlers
  private handleCreateSession(socketId: string, raceData: Race): void {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const session = this.multiplayerManager.createRaceSession(sessionId, raceData);

    // Join socket to session room
    this.io.sockets.sockets.get(socketId)?.join(sessionId);

    console.log(`Session created: ${sessionId}`);
  }

  private handleJoinSession(socketId: string, data: { sessionId?: string; playerData: any }): void {
    let sessionId = data.sessionId;

    // If no specific session, find available one
    if (!sessionId) {
      const availableSessions = this.multiplayerManager.getAllSessions()
        .filter(s => s.status === RaceStatus.WAITING && s.players.size < 8);

      if (availableSessions.length > 0) {
        sessionId = availableSessions[0].id;
      } else {
        // Create new session
        const defaultRace: Race = this.createDefaultRace();
        sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        this.multiplayerManager.createRaceSession(sessionId, defaultRace);
      }
    }

    if (!sessionId) {
      this.io.to(socketId).emit('error', 'No available sessions');
      return;
    }

    const success = this.multiplayerManager.addPlayerToSession(sessionId, {
      id: data.playerData.publicKey,
      name: data.playerData.name,
      tokenBalance: data.playerData.tokenBalance || 1000,
    });

    if (success) {
      this.playerSockets.set(data.playerData.publicKey, socketId);
      this.socketPlayers.set(socketId, data.playerData.publicKey);

      // Join socket to session room
      this.io.sockets.sockets.get(socketId)?.join(sessionId);

      console.log(`Player ${data.playerData.name} joined session ${sessionId}`);
    } else {
      this.io.to(socketId).emit('error', 'Failed to join session');
    }
  }

  private handleLeaveSession(socketId: string): void {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    const session = this.multiplayerManager.getPlayerSession(playerId);
    if (session) {
      // Leave socket room
      this.io.sockets.sockets.get(socketId)?.leave(session.id);
    }

    this.multiplayerManager.removePlayerFromSession(playerId);
    this.playerSockets.delete(playerId);
    this.socketPlayers.delete(socketId);
  }

  private handleSetReady(socketId: string, ready: boolean): void {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    this.multiplayerManager.setPlayerReady(playerId, ready);
  }

  private handleUsePowerUp(socketId: string, powerUpType: PowerUpType): void {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    this.multiplayerManager.usePowerUp(playerId, powerUpType);
  }

  private handleHeartbeat(socketId: string): void {
    const playerId = this.socketPlayers.get(socketId);
    if (playerId) {
      this.multiplayerManager.updatePlayerHeartbeat(playerId);
    }

    this.io.to(socketId).emit('heartbeat');
  }

  private handleRequestSessions(socketId: string): void {
    const sessions = this.multiplayerManager.getAllSessions();
    // Convert to lobby format for compatibility
    const lobbies = sessions.map(session => ({
      id: session.id,
      name: `Race ${session.id.split('-')[1]}`,
      players: Array.from(session.players.values()).map(p => ({
        id: p.id,
        name: p.name,
        position: p.position,
        speed: p.velocity,
        tokenBalance: p.tokenBalance,
        powerUps: [],
        isReady: p.isReady,
        avatar: '🏎️',
        publicKey: p.id as any,
      })),
      maxPlayers: 8,
      entryFee: 50,
      status: session.status === RaceStatus.WAITING ? 'waiting' :
              session.status === RaceStatus.STARTING ? 'starting' :
              session.status === RaceStatus.ACTIVE ? 'racing' : 'finished',
    }));

    this.io.to(socketId).emit('lobbyUpdate', lobbies);
  }

  private handleDisconnect(socketId: string): void {
    this.handleLeaveSession(socketId);
    this.handleLeaveLobby(socketId); // Legacy support
  }

  // Create default race configuration
  private createDefaultRace(): Race {
    return {
      id: `race-${Date.now()}`,
      name: 'Gorbagana Grand Prix',
      players: [],
      maxPlayers: 8,
      entryFee: 50,
      prizePool: 0,
      status: RaceStatus.WAITING,
      startTime: new Date(),
      duration: 300000, // 5 minutes
      track: {
        id: 'track-1',
        name: 'Gorbagana Circuit',
        length: 1000,
        difficulty: 2,
        obstacles: [],
        shortcuts: [],
      },
    };
  }

  private createDefaultLobbies() {
    // Create a few default lobbies for legacy support
    const lobbies = [
      {
        id: 'lobby-1',
        name: 'Beginner Track',
        maxPlayers: 4,
        entryFee: 50,
      },
      {
        id: 'lobby-2',
        name: 'Pro Circuit',
        maxPlayers: 6,
        entryFee: 100,
      },
      {
        id: 'lobby-3',
        name: 'Championship',
        maxPlayers: 8,
        entryFee: 200,
      },
    ];

    lobbies.forEach((lobbyData) => {
      const lobby: RaceLobby = {
        ...lobbyData,
        players: [],
        status: 'waiting',
      };
      this.lobbies.set(lobby.id, lobby);
    });
  }

  private handleJoinLobby(socketId: string, playerData: { publicKey: string; name: string }) {
    // Find an available lobby
    const availableLobby = Array.from(this.lobbies.values()).find(
      (lobby) => lobby.status === 'waiting' && lobby.players.length < lobby.maxPlayers
    );

    if (!availableLobby) {
      this.io.to(socketId).emit('error', 'No available lobbies');
      return;
    }

    // Create player object
    const player: Player = {
      id: playerData.publicKey,
      publicKey: playerData.publicKey as any, // Type conversion for demo
      name: playerData.name,
      position: 0,
      speed: 1,
      tokenBalance: 1000, // Mock balance
      powerUps: [],
      isReady: false,
      avatar: '🏎️',
    };

    // Add player to lobby
    availableLobby.players.push(player);
    this.playerSockets.set(player.id, socketId);
    this.socketPlayers.set(socketId, player.id);

    // Join socket room
    this.io.sockets.sockets.get(socketId)?.join(availableLobby.id);

    // Notify all players in lobby
    this.io.to(availableLobby.id).emit('playerJoined', player);
    this.io.emit('lobbyUpdate', Array.from(this.lobbies.values()));

    console.log(`Player ${player.name} joined lobby ${availableLobby.name}`);
  }

  private handleLeaveLobby(socketId: string) {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    // Find lobby containing this player
    const lobby = Array.from(this.lobbies.values()).find((l) =>
      l.players.some((p) => p.id === playerId)
    );

    if (lobby) {
      // Remove player from lobby
      lobby.players = lobby.players.filter((p) => p.id !== playerId);

      // Leave socket room
      this.io.sockets.sockets.get(socketId)?.leave(lobby.id);

      // Notify remaining players
      this.io.to(lobby.id).emit('playerLeft', playerId);
      this.io.emit('lobbyUpdate', Array.from(this.lobbies.values()));

      // If race was in progress and no players left, reset lobby
      if (lobby.players.length === 0 && lobby.status !== 'waiting') {
        lobby.status = 'waiting';
        if (lobby.gameEngine) {
          lobby.gameEngine.destroy();
          lobby.gameEngine = undefined;
        }
      }

      console.log(`Player ${playerId} left lobby ${lobby.name}`);
    }

    // Clean up mappings
    this.playerSockets.delete(playerId);
    this.socketPlayers.delete(socketId);
  }

  private handleUsePowerUp(socketId: string, powerUpType: PowerUpType) {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    const lobby = Array.from(this.lobbies.values()).find((l) =>
      l.players.some((p) => p.id === playerId)
    );

    if (lobby && lobby.gameEngine && lobby.status === 'racing') {
      const success = lobby.gameEngine.usePowerUp(playerId, powerUpType);
      if (success) {
        this.io.to(lobby.id).emit('powerUpUsed', playerId, powerUpType);
      }
    }
  }

  private handlePlayerReady(socketId: string) {
    const playerId = this.socketPlayers.get(socketId);
    if (!playerId) return;

    const lobby = Array.from(this.lobbies.values()).find((l) =>
      l.players.some((p) => p.id === playerId)
    );

    if (lobby) {
      const player = lobby.players.find((p) => p.id === playerId);
      if (player) {
        player.isReady = true;

        // Check if all players are ready and we have at least 2 players
        const allReady = lobby.players.length >= 2 && lobby.players.every((p) => p.isReady);

        if (allReady && lobby.status === 'waiting') {
          this.startRace(lobby);
        }

        this.io.to(lobby.id).emit('raceUpdate', this.createRaceFromLobby(lobby));
      }
    }
  }

  private startRace(lobby: RaceLobby) {
    lobby.status = 'starting';

    // Create race object
    const race = this.createRaceFromLobby(lobby);

    // Initialize game engine
    lobby.gameEngine = new GameEngine();
    lobby.gameEngine.initializeRace(race);

    // Set up game engine event handlers
    lobby.gameEngine.onEvent((event: RaceEvent) => {
      switch (event.type) {
        case 'position_update':
          this.io.to(lobby.id).emit('positionUpdate', event.playerId, event.data.position, event.data.speed);
          break;
        case 'race_finished':
          lobby.status = 'finished';
          this.io.to(lobby.id).emit('raceFinished', event.data.race);
          
          // Reset lobby after 10 seconds
          setTimeout(() => {
            this.resetLobby(lobby);
          }, 10000);
          break;
      }
    });

    // Start the race after a 3-second countdown
    this.io.to(lobby.id).emit('raceStarted', race);
    
    setTimeout(() => {
      if (lobby.gameEngine) {
        lobby.status = 'racing';
        lobby.gameEngine.startRace();
      }
    }, 3000);

    console.log(`Race started in lobby ${lobby.name}`);
  }

  private resetLobby(lobby: RaceLobby) {
    lobby.status = 'waiting';
    lobby.players.forEach((player) => {
      player.isReady = false;
      player.position = 0;
      player.speed = 1;
      player.powerUps = [];
    });

    if (lobby.gameEngine) {
      lobby.gameEngine.destroy();
      lobby.gameEngine = undefined;
    }

    this.io.to(lobby.id).emit('raceUpdate', this.createRaceFromLobby(lobby));
    this.io.emit('lobbyUpdate', Array.from(this.lobbies.values()));

    console.log(`Lobby ${lobby.name} reset`);
  }

  private createRaceFromLobby(lobby: RaceLobby): Race {
    return {
      id: lobby.id,
      name: lobby.name,
      players: lobby.players,
      maxPlayers: lobby.maxPlayers,
      entryFee: lobby.entryFee,
      prizePool: lobby.entryFee * lobby.players.length,
      status: lobby.status as RaceStatus,
      startTime: new Date(),
      duration: 60000, // 1 minute race
      track: {
        id: 'track-1',
        name: 'Gorbagana Circuit',
        length: 1000,
        difficulty: 1,
        obstacles: [],
        shortcuts: [],
      },
    };
  }

  public getServer() {
    return this.io;
  }
}
