import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { BackpackWalletAdapter } from '@solana/wallet-adapter-wallets';
import { Connection, clusterApiUrl } from '@solana/web3.js';
import { useMemo } from 'react';

// Gorbagana testnet configuration
export const GORBAGANA_RPC_URL = 'https://rpc.gorbagana.com'; // Replace with actual Gorbagana RPC URL
export const GORBAGANA_NETWORK = WalletAdapterNetwork.Devnet; // Use devnet for testnet

export function useWalletConfig() {
  // Configure supported wallets
  const wallets = useMemo(
    () => [
      new BackpackWalletAdapter(),
      // Add other wallet adapters as needed
    ],
    []
  );

  // Configure connection to Gorbagana testnet
  const endpoint = useMemo(() => {
    // For now, use Solana devnet until we have the actual Gorbagana RPC
    // TODO: Replace with actual Gorbagana testnet RPC URL
    return clusterApiUrl(GORBAGANA_NETWORK);
  }, []);

  return { wallets, endpoint };
}

export function createConnection() {
  // Create connection to Gorbagana testnet
  return new Connection(GORBAGANA_RPC_URL, 'confirmed');
}
