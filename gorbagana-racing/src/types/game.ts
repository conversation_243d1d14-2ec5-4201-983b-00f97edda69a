import { PublicKey } from '@solana/web3.js';

export interface Player {
  id: string;
  publicKey: PublicKey;
  name: string;
  position: number;
  speed: number;
  tokenBalance: number;
  powerUps: PowerUp[];
  isReady: boolean;
  avatar: string;
}

export interface PowerUp {
  id: string;
  type: PowerUpType;
  duration: number;
  cost: number;
  effect: number;
}

export enum PowerUpType {
  SPEED_BOOST = 'speed_boost',
  SHIELD = 'shield',
  SHORTCUT = 'shortcut',
  DOUBLE_TOKENS = 'double_tokens',
}

export interface Race {
  id: string;
  name: string;
  players: Player[];
  maxPlayers: number;
  entryFee: number;
  prizePool: number;
  status: RaceStatus;
  startTime: Date;
  duration: number;
  track: Track;
  winner?: Player;
}

export enum RaceStatus {
  WAITING = 'waiting',
  STARTING = 'starting',
  ACTIVE = 'active',
  FINISHED = 'finished',
  CANCELLED = 'cancelled',
}

export interface Track {
  id: string;
  name: string;
  length: number;
  difficulty: number;
  obstacles: Obstacle[];
  shortcuts: Shortcut[];
}

export interface Obstacle {
  id: string;
  position: number;
  type: string;
  effect: number;
}

export interface Shortcut {
  id: string;
  startPosition: number;
  endPosition: number;
  cost: number;
}

export interface GameState {
  currentRace: Race | null;
  player: Player | null;
  isConnected: boolean;
  tokenBalance: number;
  leaderboard: Player[];
}

export interface RaceEvent {
  type: 'position_update' | 'power_up_used' | 'race_finished' | 'player_joined' | 'player_left' |
       'collision' | 'shortcut_used' | 'race_paused' | 'race_resumed';
  playerId: string;
  data: any;
  timestamp: Date;
}

export interface TokenTransaction {
  id: string;
  type: 'entry_fee' | 'power_up_purchase' | 'prize_payout' | 'refund';
  amount: number;
  playerId: string;
  raceId: string;
  signature: string;
  timestamp: Date;
}
