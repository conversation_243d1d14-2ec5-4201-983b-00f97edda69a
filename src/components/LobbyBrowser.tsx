'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { LobbyConfig, LobbyInstance, MatchmakingCriteria } from '@/lib/lobby-manager';

interface LobbyBrowserProps {
  onJoinLobby: (lobbyId: string, password?: string) => void;
  onQuickMatch: (criteria: MatchmakingCriteria) => void;
  onCreateCustomLobby: (config: Partial<LobbyConfig>) => void;
  availableLobbies: LobbyInstance[];
  lobbyConfigs: LobbyConfig[];
  isLoading: boolean;
}

export function LobbyBrowser({
  onJoinLobby,
  onQuickMatch,
  onCreateCustomLobby,
  availableLobbies,
  lobbyConfigs,
  isLoading
}: LobbyBrowserProps) {
  const { connected } = useWallet();
  const { balance } = useBlockchain();
  
  const [selectedTab, setSelectedTab] = useState<'browse' | 'quickmatch' | 'create'>('browse');
  const [filterDifficulty, setFilterDifficulty] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'players' | 'entry_fee' | 'created'>('players');
  const [showPasswordModal, setShowPasswordModal] = useState<string | null>(null);
  const [password, setPassword] = useState('');

  // Quick match criteria
  const [quickMatchCriteria, setQuickMatchCriteria] = useState<MatchmakingCriteria>({
    preferredDifficulty: 'Beginner',
    tokenBalance: balance,
  });

  // Custom lobby creation
  const [customLobby, setCustomLobby] = useState({
    name: '',
    description: '',
    maxPlayers: 6,
    entryFee: 50,
    difficulty: 'Intermediate' as const,
    privateMode: false,
    password: '',
  });

  // Filter and sort lobbies
  const filteredLobbies = availableLobbies
    .filter(lobby => {
      if (filterDifficulty !== 'all' && lobby.config.difficulty !== filterDifficulty) {
        return false;
      }
      if (balance < lobby.config.tokenRequirement) {
        return false;
      }
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'players':
          return b.players.size - a.players.size;
        case 'entry_fee':
          return a.config.entryFee - b.config.entryFee;
        case 'created':
          return b.createdAt - a.createdAt;
        default:
          return 0;
      }
    });

  const handleJoinLobby = (lobbyId: string, requiresPassword: boolean = false) => {
    if (requiresPassword) {
      setShowPasswordModal(lobbyId);
    } else {
      onJoinLobby(lobbyId);
    }
  };

  const handlePasswordSubmit = () => {
    if (showPasswordModal) {
      onJoinLobby(showPasswordModal, password);
      setShowPasswordModal(null);
      setPassword('');
    }
  };

  const handleQuickMatch = () => {
    onQuickMatch({
      ...quickMatchCriteria,
      tokenBalance: balance,
    });
  };

  const handleCreateCustomLobby = () => {
    if (!customLobby.name.trim()) return;

    onCreateCustomLobby({
      name: customLobby.name,
      description: customLobby.description,
      maxPlayers: customLobby.maxPlayers,
      entryFee: customLobby.entryFee,
      difficulty: customLobby.difficulty,
      minPlayers: Math.max(2, Math.floor(customLobby.maxPlayers / 2)),
      prizeMultiplier: 3.0,
      autoStart: true,
      autoStartDelay: 30000,
      skillMatchmaking: false,
      tokenRequirement: customLobby.entryFee * 2,
      track: {
        id: 'track-custom',
        name: 'Custom Track',
        length: 1000,
        difficulty: customLobby.difficulty === 'Beginner' ? 1 :
                   customLobby.difficulty === 'Intermediate' ? 2 :
                   customLobby.difficulty === 'Advanced' ? 3 : 4,
        obstacles: [],
        shortcuts: [],
      },
    });

    // Reset form
    setCustomLobby({
      name: '',
      description: '',
      maxPlayers: 6,
      entryFee: 50,
      difficulty: 'Intermediate',
      privateMode: false,
      password: '',
    });
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Race Lobby Browser</h3>
        <p className="text-white/70">Connect your wallet to browse and join race lobbies</p>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Race Lobby Browser</h3>
        <div className="text-sm text-white/70">
          Balance: {balance.toFixed(0)} GORB
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        {[
          { id: 'browse', label: 'Browse Lobbies', icon: '🏁' },
          { id: 'quickmatch', label: 'Quick Match', icon: '⚡' },
          { id: 'create', label: 'Create Lobby', icon: '➕' },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setSelectedTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              selectedTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Browse Lobbies Tab */}
      {selectedTab === 'browse' && (
        <div className="space-y-4">
          {/* Filters */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm text-white/70">Difficulty:</label>
              <select
                value={filterDifficulty}
                onChange={(e) => setFilterDifficulty(e.target.value)}
                className="bg-white/10 border border-white/20 rounded px-2 py-1 text-white text-sm"
              >
                <option value="all">All</option>
                <option value="Beginner">Beginner</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Advanced">Advanced</option>
                <option value="Expert">Expert</option>
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <label className="text-sm text-white/70">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-white/10 border border-white/20 rounded px-2 py-1 text-white text-sm"
              >
                <option value="players">Players</option>
                <option value="entry_fee">Entry Fee</option>
                <option value="created">Recently Created</option>
              </select>
            </div>
          </div>

          {/* Lobbies List */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin w-6 h-6 border-2 border-white/30 border-t-white rounded-full mx-auto mb-2"></div>
              <p className="text-white/70">Loading lobbies...</p>
            </div>
          ) : filteredLobbies.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-white/70 mb-4">No available lobbies found</p>
              <button
                onClick={() => setSelectedTab('create')}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded transition-colors"
              >
                Create New Lobby
              </button>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {filteredLobbies.map((lobby) => {
                const lobbyId = Array.from(availableLobbies.entries())
                  .find(([_, l]) => l === lobby)?.[0] || '';
                
                return (
                  <div key={lobbyId} className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="text-white font-medium flex items-center gap-2">
                          {lobby.config.name}
                          {lobby.settings.privateMode && <span className="text-yellow-400">🔒</span>}
                        </h4>
                        <p className="text-sm text-white/70">{lobby.config.description}</p>
                      </div>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        lobby.config.difficulty === 'Beginner' ? 'bg-green-600 text-green-100' :
                        lobby.config.difficulty === 'Intermediate' ? 'bg-yellow-600 text-yellow-100' :
                        lobby.config.difficulty === 'Advanced' ? 'bg-orange-600 text-orange-100' :
                        'bg-red-600 text-red-100'
                      }`}>
                        {lobby.config.difficulty}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-white/70">Players:</span>
                        <span className="text-white ml-1">
                          {lobby.players.size}/{lobby.config.maxPlayers}
                        </span>
                      </div>
                      <div>
                        <span className="text-white/70">Entry:</span>
                        <span className="text-white ml-1">{lobby.config.entryFee} GORB</span>
                      </div>
                      <div>
                        <span className="text-white/70">Prize:</span>
                        <span className="text-green-400 ml-1">
                          {(lobby.config.entryFee * lobby.players.size * lobby.config.prizeMultiplier).toFixed(0)} GORB
                        </span>
                      </div>
                      <div>
                        <span className="text-white/70">Track:</span>
                        <span className="text-white ml-1">{lobby.config.track.name}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {Array.from(lobby.players.values()).slice(0, 4).map((player, index) => (
                          <div
                            key={player.id}
                            className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-xs"
                            title={player.name}
                          >
                            {player.name.charAt(0).toUpperCase()}
                          </div>
                        ))}
                        {lobby.players.size > 4 && (
                          <span className="text-white/70 text-xs">+{lobby.players.size - 4}</span>
                        )}
                      </div>
                      
                      <button
                        onClick={() => handleJoinLobby(lobbyId, lobby.settings.privateMode)}
                        disabled={
                          lobby.players.size >= lobby.config.maxPlayers ||
                          balance < lobby.config.entryFee
                        }
                        className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
                          lobby.players.size >= lobby.config.maxPlayers
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : balance < lobby.config.entryFee
                            ? 'bg-red-600 text-red-200 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                      >
                        {lobby.players.size >= lobby.config.maxPlayers
                          ? 'Full'
                          : balance < lobby.config.entryFee
                          ? 'Insufficient Funds'
                          : 'Join Race'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}

      {/* Quick Match Tab */}
      {selectedTab === 'quickmatch' && (
        <div className="space-y-4">
          <div className="bg-white/5 rounded-lg p-4">
            <h4 className="text-white font-medium mb-4">Quick Match Preferences</h4>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-white/70 mb-1">Preferred Difficulty</label>
                <select
                  value={quickMatchCriteria.preferredDifficulty || 'Beginner'}
                  onChange={(e) => setQuickMatchCriteria(prev => ({
                    ...prev,
                    preferredDifficulty: e.target.value
                  }))}
                  className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white"
                >
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                  <option value="Expert">Expert</option>
                </select>
              </div>
            </div>
          </div>

          <button
            onClick={handleQuickMatch}
            className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all transform hover:scale-105"
          >
            ⚡ Find Quick Match
          </button>

          <div className="text-center text-sm text-white/60">
            <p>We'll find the best available lobby for you based on your preferences</p>
          </div>
        </div>
      )}

      {/* Create Lobby Tab */}
      {selectedTab === 'create' && (
        <div className="space-y-4">
          <div className="bg-white/5 rounded-lg p-4 space-y-4">
            <h4 className="text-white font-medium">Create Custom Lobby</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-white/70 mb-1">Lobby Name</label>
                <input
                  type="text"
                  value={customLobby.name}
                  onChange={(e) => setCustomLobby(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="My Awesome Race"
                  className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-white/50"
                  maxLength={30}
                />
              </div>
              
              <div>
                <label className="block text-sm text-white/70 mb-1">Max Players</label>
                <select
                  value={customLobby.maxPlayers}
                  onChange={(e) => setCustomLobby(prev => ({ ...prev, maxPlayers: parseInt(e.target.value) }))}
                  className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white"
                >
                  <option value={2}>2 Players</option>
                  <option value={4}>4 Players</option>
                  <option value={6}>6 Players</option>
                  <option value={8}>8 Players</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm text-white/70 mb-1">Entry Fee (GORB)</label>
                <input
                  type="number"
                  value={customLobby.entryFee}
                  onChange={(e) => setCustomLobby(prev => ({ ...prev, entryFee: parseInt(e.target.value) || 0 }))}
                  min="10"
                  max="500"
                  className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm text-white/70 mb-1">Difficulty</label>
                <select
                  value={customLobby.difficulty}
                  onChange={(e) => setCustomLobby(prev => ({ ...prev, difficulty: e.target.value as any }))}
                  className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white"
                >
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                  <option value="Expert">Expert</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm text-white/70 mb-1">Description</label>
              <textarea
                value={customLobby.description}
                onChange={(e) => setCustomLobby(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe your race..."
                className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-white/50 h-20 resize-none"
                maxLength={100}
              />
            </div>
          </div>

          <button
            onClick={handleCreateCustomLobby}
            disabled={!customLobby.name.trim() || balance < customLobby.entryFee * 2}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors"
          >
            Create Lobby
          </button>

          {balance < customLobby.entryFee * 2 && (
            <p className="text-red-400 text-sm text-center">
              Insufficient balance. Need {customLobby.entryFee * 2} GORB to create this lobby.
            </p>
          )}
        </div>
      )}

      {/* Password Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-80">
            <h4 className="text-white font-medium mb-4">Enter Lobby Password</h4>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              className="w-full bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-white/50 mb-4"
              onKeyPress={(e) => e.key === 'Enter' && handlePasswordSubmit()}
              autoFocus
            />
            <div className="flex gap-2">
              <button
                onClick={handlePasswordSubmit}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
              >
                Join
              </button>
              <button
                onClick={() => {
                  setShowPasswordModal(null);
                  setPassword('');
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
