'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { ChatMessage } from '@/lib/lobby-manager';

interface LobbyChatProps {
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
  isEnabled: boolean;
  playerCount: number;
}

export function LobbyChat({ messages, onSendMessage, isEnabled, playerCount }: LobbyChatProps) {
  const { publicKey } = useWallet();
  const [newMessage, setNewMessage] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isExpanded]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !isEnabled) return;

    onSendMessage(newMessage.trim());
    setNewMessage('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getMessageStyle = (message: ChatMessage) => {
    switch (message.type) {
      case 'system':
        return 'text-blue-300 italic';
      case 'announcement':
        return 'text-yellow-300 font-medium';
      default:
        return 'text-white';
    }
  };

  const getPlayerColor = (playerId: string) => {
    // Generate consistent color for each player
    const colors = [
      'text-red-400',
      'text-green-400',
      'text-blue-400',
      'text-yellow-400',
      'text-purple-400',
      'text-pink-400',
      'text-indigo-400',
      'text-orange-400',
    ];
    
    const hash = playerId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  if (!isExpanded) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-lg">💬</span>
            <span className="text-white font-medium text-sm">Chat</span>
            {messages.length > 0 && (
              <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
                {messages.length}
              </span>
            )}
          </div>
          
          <button
            onClick={() => setIsExpanded(true)}
            className="text-white/70 hover:text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Recent message preview */}
        {messages.length > 0 && (
          <div className="mt-2 text-xs text-white/60 truncate">
            {messages[messages.length - 1].playerName}: {messages[messages.length - 1].message}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 space-y-3">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-lg">💬</span>
          <span className="text-white font-medium">Lobby Chat</span>
          <span className="text-white/60 text-sm">({playerCount} players)</span>
        </div>
        
        <button
          onClick={() => setIsExpanded(false)}
          className="text-white/70 hover:text-white transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        </button>
      </div>

      {/* Messages */}
      <div className="bg-white/5 rounded-lg p-3 h-48 overflow-y-auto space-y-2">
        {messages.length === 0 ? (
          <div className="text-center text-white/50 text-sm py-8">
            <p>No messages yet</p>
            <p>Say hello to other racers!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="text-sm">
              {message.type === 'chat' ? (
                <div className="flex items-start gap-2">
                  <span className="text-white/40 text-xs mt-0.5">
                    {formatTimestamp(message.timestamp)}
                  </span>
                  <div className="flex-1">
                    <span className={`font-medium ${getPlayerColor(message.playerId)}`}>
                      {message.playerName}
                      {message.playerId === publicKey?.toString() && (
                        <span className="text-yellow-400 ml-1">(You)</span>
                      )}
                    </span>
                    <span className="text-white/80">: {message.message}</span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="text-white/40 text-xs">
                    {formatTimestamp(message.timestamp)}
                  </span>
                  <span className={getMessageStyle(message)}>
                    {message.type === 'system' && '🤖 '}
                    {message.type === 'announcement' && '📢 '}
                    {message.message}
                  </span>
                </div>
              )}
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      {isEnabled ? (
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="flex-1 bg-white/10 border border-white/20 rounded px-3 py-2 text-white placeholder-white/50 text-sm focus:outline-none focus:border-purple-400"
            maxLength={200}
          />
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
          >
            Send
          </button>
        </div>
      ) : (
        <div className="text-center text-white/50 text-sm py-2">
          Chat is disabled in this lobby
        </div>
      )}

      {/* Quick reactions */}
      {isEnabled && (
        <div className="flex gap-1">
          {['👍', '😄', '🔥', '💪', '🏁', '⚡'].map((emoji) => (
            <button
              key={emoji}
              onClick={() => onSendMessage(emoji)}
              className="bg-white/10 hover:bg-white/20 text-white px-2 py-1 rounded text-sm transition-colors"
            >
              {emoji}
            </button>
          ))}
        </div>
      )}

      {/* Chat rules */}
      <div className="text-xs text-white/40 border-t border-white/10 pt-2">
        <p>Be respectful • No spam • Have fun racing! 🏎️</p>
      </div>
    </div>
  );
}
