'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useMultiplayer } from '@/hooks/useMultiplayer';

export function MultiplayerLobby() {
  const { connected: walletConnected } = useWallet();
  const {
    connected,
    currentLobby,
    currentRace,
    lobbies,
    players,
    isInRace,
    raceStatus,
    playerPosition,
    error,
    joinLobby,
    leaveLobby,
    setPlayerReady,
    refreshLobbies,
  } = useMultiplayer();

  const [playerName, setPlayerName] = useState('');
  const [showNameInput, setShowNameInput] = useState(false);

  const handleJoinLobby = () => {
    if (!playerName.trim()) {
      setShowNameInput(true);
      return;
    }
    joinLobby(playerName.trim());
    setShowNameInput(false);
  };

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (playerName.trim()) {
      joinLobby(playerName.trim());
      setShowNameInput(false);
    }
  };

  if (!walletConnected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Multiplayer Racing</h3>
        <p className="text-white/70">Connect your wallet to join multiplayer races</p>
      </div>
    );
  }

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Multiplayer Racing</h3>
        <div className="flex items-center gap-2">
          <div className="animate-spin w-4 h-4 border-2 border-white/30 border-t-white rounded-full"></div>
          <p className="text-white/70">Connecting to multiplayer server...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Multiplayer Racing</h3>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span className="text-sm text-white/70">Connected</span>
        </div>
      </div>

      {error && (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      {/* Name Input Modal */}
      {showNameInput && (
        <div className="bg-white/5 border border-white/20 rounded-lg p-4">
          <form onSubmit={handleNameSubmit} className="space-y-3">
            <label className="block text-white text-sm font-medium">
              Enter your racing name:
            </label>
            <input
              type="text"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              placeholder="e.g., SpeedRacer"
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded text-white placeholder-white/50 focus:outline-none focus:border-purple-400"
              autoFocus
              maxLength={20}
            />
            <div className="flex gap-2">
              <button
                type="submit"
                disabled={!playerName.trim()}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white py-2 px-4 rounded transition-colors"
              >
                Join Race
              </button>
              <button
                type="button"
                onClick={() => setShowNameInput(false)}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Current Lobby/Race */}
      {currentLobby && (
        <div className="bg-white/5 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium">{currentLobby.name}</h4>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              raceStatus === 'waiting' ? 'bg-yellow-600 text-yellow-100' :
              raceStatus === 'starting' ? 'bg-orange-600 text-orange-100' :
              raceStatus === 'racing' ? 'bg-green-600 text-green-100' :
              'bg-blue-600 text-blue-100'
            }`}>
              {raceStatus.toUpperCase()}
            </span>
          </div>

          <div className="text-sm text-white/70">
            <p>Entry Fee: {currentLobby.entryFee} GORB</p>
            <p>Players: {players.length}/{currentLobby.maxPlayers}</p>
            {currentRace && <p>Prize Pool: {currentRace.prizePool} GORB</p>}
          </div>

          {/* Players List */}
          <div className="space-y-2">
            <h5 className="text-white font-medium text-sm">Players:</h5>
            {players.map((player) => (
              <div key={player.id} className="flex items-center justify-between bg-white/5 rounded p-2">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{player.avatar}</span>
                  <span className="text-white text-sm">{player.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  {isInRace && (
                    <span className="text-xs text-white/70">
                      {player.position.toFixed(1)}%
                    </span>
                  )}
                  {player.isReady && !isInRace && (
                    <span className="text-xs bg-green-600 text-green-100 px-2 py-1 rounded">
                      Ready
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Race Progress */}
          {isInRace && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white text-sm">Your Progress</span>
                <span className="text-white text-sm">{playerPosition.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-green-400 to-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(playerPosition, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            {raceStatus === 'waiting' && !players.find(p => p.id === currentLobby.players[0]?.id)?.isReady && (
              <button
                onClick={setPlayerReady}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
              >
                Ready to Race
              </button>
            )}
            
            {raceStatus === 'waiting' && (
              <button
                onClick={leaveLobby}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
              >
                Leave
              </button>
            )}

            {raceStatus === 'finished' && (
              <button
                onClick={leaveLobby}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
              >
                Return to Lobby
              </button>
            )}
          </div>
        </div>
      )}

      {/* Available Lobbies */}
      {!currentLobby && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium">Available Races</h4>
            <button
              onClick={refreshLobbies}
              className="text-white/70 hover:text-white transition-colors"
            >
              🔄 Refresh
            </button>
          </div>

          {lobbies.length === 0 ? (
            <p className="text-white/70 text-center py-4">No lobbies available</p>
          ) : (
            <div className="space-y-2">
              {lobbies.map((lobby) => (
                <div key={lobby.id} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="text-white font-medium">{lobby.name}</h5>
                      <p className="text-sm text-white/70">
                        {lobby.players.length}/{lobby.maxPlayers} players • {lobby.entryFee} GORB
                      </p>
                    </div>
                    <button
                      onClick={handleJoinLobby}
                      disabled={lobby.players.length >= lobby.maxPlayers || lobby.status !== 'waiting'}
                      className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
                        lobby.players.length >= lobby.maxPlayers || lobby.status !== 'waiting'
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : 'bg-purple-600 hover:bg-purple-700 text-white'
                      }`}
                    >
                      {lobby.players.length >= lobby.maxPlayers ? 'Full' : 'Join'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="text-xs text-white/50 border-t border-white/10 pt-3">
        <p>Real-time multiplayer racing with up to 8 players per race!</p>
      </div>
    </div>
  );
}
