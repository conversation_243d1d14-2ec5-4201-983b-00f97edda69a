'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { GAME_CONFIG } from '@/lib/blockchain';

interface RaceDemoProps {
  onRaceComplete?: (winner: string, prizeAmount: number) => void;
}

export function RaceDemo({ onRaceComplete }: RaceDemoProps) {
  const { connected, publicKey } = useWallet();
  const { balance, buyPowerUp } = useBlockchain();
  
  const [raceState, setRaceState] = useState<'waiting' | 'racing' | 'finished'>('waiting');
  const [playerPosition, setPlayerPosition] = useState(0);
  const [raceProgress, setRaceProgress] = useState(0);
  const [powerUpsUsed, setPowerUpsUsed] = useState<string[]>([]);
  const [raceResult, setRaceResult] = useState<{ place: number; prize: number } | null>(null);

  // Simulate race progress
  useEffect(() => {
    if (raceState !== 'racing') return;

    const interval = setInterval(() => {
      setRaceProgress(prev => {
        const newProgress = prev + Math.random() * 2 + 1; // Random progress
        
        if (newProgress >= 100) {
          setRaceState('finished');
          
          // Simulate race result
          const place = Math.floor(Math.random() * 3) + 1; // 1st, 2nd, or 3rd place
          let prize = 0;
          
          switch (place) {
            case 1:
              prize = 350 * GAME_CONFIG.PRIZE_DISTRIBUTION.FIRST_PLACE;
              break;
            case 2:
              prize = 350 * GAME_CONFIG.PRIZE_DISTRIBUTION.SECOND_PLACE;
              break;
            case 3:
              prize = 350 * GAME_CONFIG.PRIZE_DISTRIBUTION.THIRD_PLACE;
              break;
          }
          
          setRaceResult({ place, prize });
          onRaceComplete?.(publicKey?.toString() || 'Player', prize);
          
          return 100;
        }
        
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [raceState, publicKey, onRaceComplete]);

  const startRace = () => {
    setRaceState('racing');
    setRaceProgress(0);
    setPowerUpsUsed([]);
    setRaceResult(null);
  };

  const resetRace = () => {
    setRaceState('waiting');
    setRaceProgress(0);
    setPowerUpsUsed([]);
    setRaceResult(null);
  };

  const handlePowerUpUse = async (powerUpType: string) => {
    if (powerUpsUsed.includes(powerUpType)) return;
    
    const result = await buyPowerUp(powerUpType);
    
    if (result.success) {
      setPowerUpsUsed(prev => [...prev, powerUpType]);
      
      // Apply power-up effect
      switch (powerUpType) {
        case 'SPEED_BOOST':
          setRaceProgress(prev => Math.min(prev + 15, 100));
          break;
        case 'SHORTCUT':
          setRaceProgress(prev => Math.min(prev + 25, 100));
          break;
        default:
          setRaceProgress(prev => Math.min(prev + 10, 100));
      }
    }
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Race Demo</h3>
        <p className="text-white/70">Connect your wallet to try the race demo</p>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Race Demo</h3>
        <div className="text-sm text-white/70">
          Balance: {balance.toFixed(2)} GORB
        </div>
      </div>

      {/* Race Track */}
      <div className="bg-white/5 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-white">Race Progress</span>
          <span className="text-white">{raceProgress.toFixed(1)}%</span>
        </div>
        
        <div className="w-full bg-gray-700 rounded-full h-4 mb-4">
          <div 
            className="bg-gradient-to-r from-green-400 to-blue-500 h-4 rounded-full transition-all duration-300"
            style={{ width: `${raceProgress}%` }}
          />
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-white/70">🏁 Start</span>
          <span className="text-white/70">🏆 Finish</span>
        </div>
      </div>

      {/* Race Controls */}
      <div className="space-y-4">
        {raceState === 'waiting' && (
          <button
            onClick={startRace}
            className="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
          >
            🏁 Start Race Demo
          </button>
        )}

        {raceState === 'racing' && (
          <div className="space-y-3">
            <p className="text-white text-center">🏎️ Racing in progress...</p>
            
            {/* Power-up buttons */}
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(GAME_CONFIG.POWER_UP_COSTS).map(([powerUp, cost]) => (
                <button
                  key={powerUp}
                  onClick={() => handlePowerUpUse(powerUp)}
                  disabled={
                    powerUpsUsed.includes(powerUp) || 
                    balance < cost
                  }
                  className={`p-2 rounded text-sm font-medium transition-colors ${
                    powerUpsUsed.includes(powerUp)
                      ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      : balance >= cost
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {powerUpsUsed.includes(powerUp) ? '✓ Used' : `${powerUp.replace('_', ' ')} (${cost} GORB)`}
                </button>
              ))}
            </div>
          </div>
        )}

        {raceState === 'finished' && raceResult && (
          <div className="text-center space-y-4">
            <div className="bg-white/5 rounded-lg p-4">
              <h4 className="text-xl font-bold text-white mb-2">
                🏆 Race Finished!
              </h4>
              <p className="text-lg text-white">
                You finished in <span className="text-yellow-400 font-bold">{raceResult.place}</span>
                {raceResult.place === 1 ? 'st' : raceResult.place === 2 ? 'nd' : 'rd'} place!
              </p>
              {raceResult.prize > 0 && (
                <p className="text-green-400 font-bold">
                  Prize: {raceResult.prize.toFixed(2)} GORB
                </p>
              )}
            </div>
            
            <button
              onClick={resetRace}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
            >
              🔄 Race Again
            </button>
          </div>
        )}
      </div>

      {/* Power-ups Used */}
      {powerUpsUsed.length > 0 && (
        <div className="bg-white/5 rounded-lg p-3">
          <h5 className="text-white font-medium mb-2">Power-ups Used:</h5>
          <div className="flex flex-wrap gap-2">
            {powerUpsUsed.map((powerUp) => (
              <span 
                key={powerUp}
                className="bg-purple-600 text-white px-2 py-1 rounded text-xs"
              >
                {powerUp.replace('_', ' ')}
              </span>
            ))}
          </div>
        </div>
      )}

      <div className="text-xs text-white/50 border-t border-white/10 pt-3">
        <p>This is a demo race. In the full game, you'll race against other players in real-time!</p>
      </div>
    </div>
  );
}
