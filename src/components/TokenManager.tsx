'use client';

import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useBlockchain } from '@/hooks/useBlockchain';
import { GAME_CONFIG } from '@/lib/blockchain';

interface TokenManagerProps {
  onEntryFeePaid?: () => void;
  onPowerUpPurchased?: (powerUpType: string) => void;
}

export function TokenManager({ onEntryFeePaid, onPowerUpPurchased }: TokenManagerProps) {
  const { connected, publicKey } = useWallet();
  const {
    balance,
    loading,
    error,
    canAffordEntryFee,
    gameWalletBalance,
    refreshBalance,
    payEntryFee,
    buyPowerUp,
    airdropTokens,
    checkPowerUpAffordability,
  } = useBlockchain();

  const [processingAction, setProcessingAction] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Handle entry fee payment
  const handlePayEntryFee = async () => {
    setProcessingAction('entry_fee');
    setSuccessMessage(null);
    
    try {
      const result = await payEntryFee();
      
      if (result.success) {
        setSuccessMessage('Entry fee paid successfully! You can now join the race.');
        onEntryFeePaid?.();
      } else {
        // Error is handled by the hook
      }
    } finally {
      setProcessingAction(null);
    }
  };

  // Handle power-up purchase
  const handleBuyPowerUp = async (powerUpType: string) => {
    setProcessingAction(powerUpType);
    setSuccessMessage(null);
    
    try {
      const result = await buyPowerUp(powerUpType);
      
      if (result.success) {
        setSuccessMessage(`${powerUpType.replace('_', ' ')} purchased successfully!`);
        onPowerUpPurchased?.(powerUpType);
      } else {
        // Error is handled by the hook
      }
    } finally {
      setProcessingAction(null);
    }
  };

  // Handle airdrop request
  const handleAirdrop = async () => {
    setProcessingAction('airdrop');
    setSuccessMessage(null);
    
    try {
      const success = await airdropTokens(1000);
      
      if (success) {
        setSuccessMessage('Airdrop successful! 1000 GORB tokens added to your wallet.');
      } else {
        // Error is handled by the hook
      }
    } finally {
      setProcessingAction(null);
    }
  };

  if (!connected) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Token Manager</h3>
        <p className="text-white/70">Connect your wallet to manage GORB tokens</p>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Token Manager</h3>
        <button
          onClick={refreshBalance}
          disabled={loading}
          className="text-white/70 hover:text-white transition-colors"
        >
          {loading ? '🔄' : '↻'} Refresh
        </button>
      </div>

      {/* Balance Display */}
      <div className="bg-white/5 rounded-lg p-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-white/70">Your Balance:</span>
          <span className="text-yellow-400 font-bold text-xl">
            {balance.toFixed(2)} GORB
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-white/70">Prize Pool:</span>
          <span className="text-green-400 font-bold">
            {gameWalletBalance.toFixed(2)} GORB
          </span>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-500/20 border border-green-500/50 rounded-lg p-3">
          <p className="text-green-200 text-sm">{successMessage}</p>
        </div>
      )}

      {/* Entry Fee Section */}
      <div className="space-y-3">
        <h4 className="text-white font-medium">Race Entry</h4>
        <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
          <div>
            <p className="text-white">Entry Fee</p>
            <p className="text-white/70 text-sm">{GAME_CONFIG.ENTRY_FEE} GORB</p>
          </div>
          <button
            onClick={handlePayEntryFee}
            disabled={!canAffordEntryFee || processingAction === 'entry_fee' || loading}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              canAffordEntryFee && processingAction !== 'entry_fee' && !loading
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {processingAction === 'entry_fee' ? 'Processing...' : 'Pay Entry Fee'}
          </button>
        </div>
      </div>

      {/* Power-ups Section */}
      <div className="space-y-3">
        <h4 className="text-white font-medium">Power-ups</h4>
        <div className="grid grid-cols-1 gap-2">
          {Object.entries(GAME_CONFIG.POWER_UP_COSTS).map(([powerUp, cost]) => (
            <div key={powerUp} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
              <div>
                <p className="text-white capitalize">{powerUp.replace('_', ' ')}</p>
                <p className="text-white/70 text-sm">{cost} GORB</p>
              </div>
              <button
                onClick={() => handleBuyPowerUp(powerUp)}
                disabled={
                  balance < cost || 
                  processingAction === powerUp || 
                  loading
                }
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  balance >= cost && processingAction !== powerUp && !loading
                    ? 'bg-purple-600 hover:bg-purple-700 text-white'
                    : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                }`}
              >
                {processingAction === powerUp ? 'Buying...' : 'Buy'}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Airdrop Section (for testing) */}
      <div className="space-y-3 border-t border-white/10 pt-4">
        <h4 className="text-white font-medium">Testing</h4>
        <div className="flex items-center justify-between bg-white/5 rounded-lg p-3">
          <div>
            <p className="text-white">Get Test Tokens</p>
            <p className="text-white/70 text-sm">1000 GORB (Devnet only)</p>
          </div>
          <button
            onClick={handleAirdrop}
            disabled={processingAction === 'airdrop' || loading}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              processingAction !== 'airdrop' && !loading
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {processingAction === 'airdrop' ? 'Requesting...' : 'Airdrop'}
          </button>
        </div>
      </div>

      {/* Wallet Info */}
      <div className="text-xs text-white/50 border-t border-white/10 pt-4">
        <p>Wallet: {publicKey?.toString().slice(0, 8)}...{publicKey?.toString().slice(-8)}</p>
        <p>Network: Solana Devnet (Gorbagana Testnet simulation)</p>
      </div>
    </div>
  );
}
