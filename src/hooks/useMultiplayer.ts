import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useWallet } from '@solana/wallet-adapter-react';
import { Player, Race, PowerUpType } from '@/types/game';
import { ServerToClientEvents, ClientToServerEvents, RaceLobby } from '@/lib/socket-server';

export interface MultiplayerState {
  connected: boolean;
  currentLobby: RaceLobby | null;
  currentRace: Race | null;
  lobbies: RaceLobby[];
  players: Player[];
  isInRace: boolean;
  raceStatus: 'waiting' | 'starting' | 'racing' | 'finished';
  playerPosition: number;
  error: string | null;
}

export interface MultiplayerActions {
  connect: () => void;
  disconnect: () => void;
  joinLobby: (playerName: string) => void;
  leaveLobby: () => void;
  usePowerUp: (powerUpType: PowerUpType) => void;
  setPlayerReady: () => void;
  refreshLobbies: () => void;
}

export function useMultiplayer(): MultiplayerState & MultiplayerActions {
  const { publicKey, connected: walletConnected } = useWallet();
  const socketRef = useRef<Socket<ServerToClientEvents, ClientToServerEvents> | null>(null);
  
  const [state, setState] = useState<MultiplayerState>({
    connected: false,
    currentLobby: null,
    currentRace: null,
    lobbies: [],
    players: [],
    isInRace: false,
    raceStatus: 'waiting',
    playerPosition: 0,
    error: null,
  });

  // Connect to Socket.io server
  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    const socket = io(process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000', {
      transports: ['websocket', 'polling'],
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      setState(prev => ({ ...prev, connected: true, error: null }));
      console.log('Connected to multiplayer server');
    });

    socket.on('disconnect', () => {
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        currentLobby: null, 
        currentRace: null,
        isInRace: false,
        raceStatus: 'waiting'
      }));
      console.log('Disconnected from multiplayer server');
    });

    socket.on('lobbyUpdate', (lobbies) => {
      setState(prev => ({ ...prev, lobbies }));
    });

    socket.on('playerJoined', (player) => {
      setState(prev => {
        if (!prev.currentLobby) return prev;
        
        const updatedLobby = {
          ...prev.currentLobby,
          players: [...prev.currentLobby.players, player]
        };
        
        return {
          ...prev,
          currentLobby: updatedLobby,
          players: updatedLobby.players
        };
      });
      console.log(`Player joined: ${player.name}`);
    });

    socket.on('playerLeft', (playerId) => {
      setState(prev => {
        if (!prev.currentLobby) return prev;
        
        const updatedLobby = {
          ...prev.currentLobby,
          players: prev.currentLobby.players.filter(p => p.id !== playerId)
        };
        
        return {
          ...prev,
          currentLobby: updatedLobby,
          players: updatedLobby.players
        };
      });
      console.log(`Player left: ${playerId}`);
    });

    socket.on('raceStarted', (race) => {
      setState(prev => ({
        ...prev,
        currentRace: race,
        isInRace: true,
        raceStatus: 'starting',
        playerPosition: 0
      }));
      console.log('Race started!');
    });

    socket.on('raceUpdate', (race) => {
      setState(prev => ({
        ...prev,
        currentRace: race,
        raceStatus: race.status as any
      }));
    });

    socket.on('raceFinished', (race) => {
      setState(prev => ({
        ...prev,
        currentRace: race,
        raceStatus: 'finished'
      }));
      console.log('Race finished!', race.winner);
    });

    socket.on('positionUpdate', (playerId, position, speed) => {
      if (publicKey && playerId === publicKey.toString()) {
        setState(prev => ({ ...prev, playerPosition: position }));
      }
      
      setState(prev => {
        if (!prev.currentRace) return prev;
        
        const updatedPlayers = prev.currentRace.players.map(player => 
          player.id === playerId 
            ? { ...player, position, speed }
            : player
        );
        
        return {
          ...prev,
          currentRace: {
            ...prev.currentRace,
            players: updatedPlayers
          },
          players: updatedPlayers
        };
      });
    });

    socket.on('powerUpUsed', (playerId, powerUpType) => {
      console.log(`Player ${playerId} used power-up: ${powerUpType}`);
      // Update UI to show power-up effect
    });

    socket.on('error', (message) => {
      setState(prev => ({ ...prev, error: message }));
      console.error('Multiplayer error:', message);
    });

  }, [publicKey]);

  // Disconnect from server
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
  }, []);

  // Join a lobby
  const joinLobby = useCallback((playerName: string) => {
    if (!socketRef.current?.connected || !publicKey) {
      setState(prev => ({ ...prev, error: 'Not connected or wallet not available' }));
      return;
    }

    socketRef.current.emit('joinLobby', {
      publicKey: publicKey.toString(),
      name: playerName
    });

    // Find and set current lobby (will be updated by server events)
    const availableLobby = state.lobbies.find(
      lobby => lobby.status === 'waiting' && lobby.players.length < lobby.maxPlayers
    );
    
    if (availableLobby) {
      setState(prev => ({ ...prev, currentLobby: availableLobby }));
    }
  }, [publicKey, state.lobbies]);

  // Leave current lobby
  const leaveLobby = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('leaveLobby');
      setState(prev => ({
        ...prev,
        currentLobby: null,
        currentRace: null,
        isInRace: false,
        raceStatus: 'waiting',
        players: []
      }));
    }
  }, []);

  // Use a power-up during race
  const usePowerUp = useCallback((powerUpType: PowerUpType) => {
    if (socketRef.current?.connected && state.isInRace) {
      socketRef.current.emit('usePowerUp', powerUpType);
    }
  }, [state.isInRace]);

  // Set player as ready
  const setPlayerReady = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('playerReady');
    }
  }, []);

  // Refresh lobbies list
  const refreshLobbies = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('requestLobbies');
    }
  }, []);

  // Auto-connect when wallet is connected
  useEffect(() => {
    if (walletConnected && publicKey) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [walletConnected, publicKey, connect, disconnect]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        setState(prev => ({ ...prev, error: null }));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [state.error]);

  return {
    ...state,
    connect,
    disconnect,
    joinLobby,
    leaveLobby,
    usePowerUp,
    setPlayerReady,
    refreshLobbies,
  };
}
