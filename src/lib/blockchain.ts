import { 
  Connection, 
  <PERSON>Key, 
  Transaction, 
  SystemProgram, 
  LAMPORTS_PER_SOL,
  sendAndConfirmTransaction,
  Keypair
} from '@solana/web3.js';
import { useWallet } from '@solana/wallet-adapter-react';

// Gorbagana testnet configuration
export const GORBAGANA_RPC_URL = 'https://api.devnet.solana.com'; // Using devnet for now, will switch to Gorbagana
export const GORB_TOKEN_DECIMALS = 9;
export const GORB_TOKEN_SYMBOL = 'GORB';

// Game configuration
export const GAME_CONFIG = {
  ENTRY_FEE: 50, // GORB tokens
  POWER_UP_COSTS: {
    SPEED_BOOST: 10,
    SHIELD: 15,
    SHORTCUT: 25,
    DOUBLE_TOKENS: 20,
  },
  PRIZE_DISTRIBUTION: {
    FIRST_PLACE: 0.7,  // 70%
    SECOND_PLACE: 0.2, // 20%
    THIRD_PLACE: 0.1,  // 10%
  },
};

export class BlockchainService {
  private connection: Connection;
  private gameWallet: Keypair; // Game treasury wallet

  constructor() {
    this.connection = new Connection(GORBAGANA_RPC_URL, 'confirmed');
    // In production, this would be loaded from secure environment variables
    this.gameWallet = Keypair.generate(); // Temporary for demo
  }

  // Get player's SOL balance (representing GORB tokens for demo)
  async getPlayerBalance(playerPublicKey: PublicKey): Promise<number> {
    try {
      const balance = await this.connection.getBalance(playerPublicKey);
      // Convert lamports to SOL and treat as GORB tokens
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('Error fetching player balance:', error);
      return 0;
    }
  }

  // Check if player has sufficient balance for entry fee
  async canAffordEntryFee(playerPublicKey: PublicKey): Promise<boolean> {
    const balance = await this.getPlayerBalance(playerPublicKey);
    return balance >= GAME_CONFIG.ENTRY_FEE;
  }

  // Check if player can afford a power-up
  async canAffordPowerUp(playerPublicKey: PublicKey, powerUpType: string): Promise<boolean> {
    const balance = await this.getPlayerBalance(playerPublicKey);
    const cost = GAME_CONFIG.POWER_UP_COSTS[powerUpType as keyof typeof GAME_CONFIG.POWER_UP_COSTS];
    return balance >= cost;
  }

  // Process entry fee payment
  async processEntryFee(
    playerPublicKey: PublicKey,
    signTransaction: (transaction: Transaction) => Promise<Transaction>
  ): Promise<{ success: boolean; signature?: string; error?: string }> {
    try {
      const entryFeeInLamports = GAME_CONFIG.ENTRY_FEE * LAMPORTS_PER_SOL;
      
      // Check balance first
      const canAfford = await this.canAffordEntryFee(playerPublicKey);
      if (!canAfford) {
        return { success: false, error: 'Insufficient balance for entry fee' };
      }

      // Create transaction to transfer entry fee to game wallet
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: playerPublicKey,
          toPubkey: this.gameWallet.publicKey,
          lamports: entryFeeInLamports,
        })
      );

      // Get recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = playerPublicKey;

      // Sign transaction with player's wallet
      const signedTransaction = await signTransaction(transaction);

      // Send transaction
      const signature = await this.connection.sendRawTransaction(
        signedTransaction.serialize()
      );

      // Confirm transaction
      await this.connection.confirmTransaction(signature);

      return { success: true, signature };
    } catch (error) {
      console.error('Error processing entry fee:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Process power-up purchase
  async processPowerUpPurchase(
    playerPublicKey: PublicKey,
    powerUpType: string,
    signTransaction: (transaction: Transaction) => Promise<Transaction>
  ): Promise<{ success: boolean; signature?: string; error?: string }> {
    try {
      const cost = GAME_CONFIG.POWER_UP_COSTS[powerUpType as keyof typeof GAME_CONFIG.POWER_UP_COSTS];
      if (!cost) {
        return { success: false, error: 'Invalid power-up type' };
      }

      const costInLamports = cost * LAMPORTS_PER_SOL;
      
      // Check balance first
      const canAfford = await this.canAffordPowerUp(playerPublicKey, powerUpType);
      if (!canAfford) {
        return { success: false, error: 'Insufficient balance for power-up' };
      }

      // Create transaction to transfer cost to game wallet
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: playerPublicKey,
          toPubkey: this.gameWallet.publicKey,
          lamports: costInLamports,
        })
      );

      // Get recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = playerPublicKey;

      // Sign transaction with player's wallet
      const signedTransaction = await signTransaction(transaction);

      // Send transaction
      const signature = await this.connection.sendRawTransaction(
        signedTransaction.serialize()
      );

      // Confirm transaction
      await this.connection.confirmTransaction(signature);

      return { success: true, signature };
    } catch (error) {
      console.error('Error processing power-up purchase:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Distribute prizes to race winners
  async distributePrizes(
    winners: { publicKey: PublicKey; place: number }[],
    totalPrizePool: number
  ): Promise<{ success: boolean; signatures?: string[]; error?: string }> {
    try {
      const signatures: string[] = [];
      
      for (const winner of winners) {
        let prizeAmount = 0;
        
        switch (winner.place) {
          case 1:
            prizeAmount = totalPrizePool * GAME_CONFIG.PRIZE_DISTRIBUTION.FIRST_PLACE;
            break;
          case 2:
            prizeAmount = totalPrizePool * GAME_CONFIG.PRIZE_DISTRIBUTION.SECOND_PLACE;
            break;
          case 3:
            prizeAmount = totalPrizePool * GAME_CONFIG.PRIZE_DISTRIBUTION.THIRD_PLACE;
            break;
          default:
            continue; // No prize for other places
        }

        if (prizeAmount > 0) {
          const prizeInLamports = prizeAmount * LAMPORTS_PER_SOL;
          
          // Create transaction to transfer prize from game wallet to winner
          const transaction = new Transaction().add(
            SystemProgram.transfer({
              fromPubkey: this.gameWallet.publicKey,
              toPubkey: winner.publicKey,
              lamports: prizeInLamports,
            })
          );

          // Get recent blockhash
          const { blockhash } = await this.connection.getLatestBlockhash();
          transaction.recentBlockhash = blockhash;
          transaction.feePayer = this.gameWallet.publicKey;

          // Sign and send transaction
          const signature = await sendAndConfirmTransaction(
            this.connection,
            transaction,
            [this.gameWallet]
          );

          signatures.push(signature);
        }
      }

      return { success: true, signatures };
    } catch (error) {
      console.error('Error distributing prizes:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Get game wallet balance (total prize pool)
  async getGameWalletBalance(): Promise<number> {
    try {
      const balance = await this.connection.getBalance(this.gameWallet.publicKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('Error fetching game wallet balance:', error);
      return 0;
    }
  }

  // Airdrop tokens for testing (only on devnet)
  async airdropTokens(playerPublicKey: PublicKey, amount: number = 1000): Promise<boolean> {
    try {
      const amountInLamports = amount * LAMPORTS_PER_SOL;
      const signature = await this.connection.requestAirdrop(playerPublicKey, amountInLamports);
      await this.connection.confirmTransaction(signature);
      return true;
    } catch (error) {
      console.error('Error airdropping tokens:', error);
      return false;
    }
  }

  // Get transaction history for a player
  async getPlayerTransactionHistory(playerPublicKey: PublicKey): Promise<any[]> {
    try {
      const signatures = await this.connection.getSignaturesForAddress(playerPublicKey, { limit: 10 });
      const transactions = [];
      
      for (const sig of signatures) {
        const tx = await this.connection.getTransaction(sig.signature);
        if (tx) {
          transactions.push({
            signature: sig.signature,
            timestamp: tx.blockTime,
            slot: tx.slot,
            // Add more transaction details as needed
          });
        }
      }
      
      return transactions;
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      return [];
    }
  }
}

// Singleton instance
export const blockchainService = new BlockchainService();
