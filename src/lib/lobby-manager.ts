import { EventEmitter } from 'events';
import { Player, Race, RaceStatus, Track } from '@/types/game';

export interface LobbyConfig {
  id: string;
  name: string;
  description: string;
  maxPlayers: number;
  minPlayers: number;
  entryFee: number;
  prizeMultiplier: number;
  track: Track;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  autoStart: boolean;
  autoStartDelay: number;
  skillMatchmaking: boolean;
  tokenRequirement: number;
}

export interface LobbyInstance {
  config: LobbyConfig;
  players: Map<string, Player>;
  status: 'waiting' | 'starting' | 'full' | 'closed';
  createdAt: number;
  startTime?: number;
  readyPlayers: Set<string>;
  spectators: Set<string>;
  chatMessages: ChatMessage[];
  settings: LobbySettings;
}

export interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: number;
  type: 'chat' | 'system' | 'announcement';
}

export interface LobbySettings {
  allowSpectators: boolean;
  chatEnabled: boolean;
  privateMode: boolean;
  password?: string;
  customRules: Record<string, any>;
}

export interface MatchmakingCriteria {
  skillLevel?: number;
  preferredDifficulty?: string;
  tokenBalance?: number;
  region?: string;
  latency?: number;
}

export class LobbyManager extends EventEmitter {
  private lobbies: Map<string, LobbyInstance> = new Map();
  private playerLobbies: Map<string, string> = new Map(); // playerId -> lobbyId
  private lobbyConfigs: Map<string, LobbyConfig> = new Map();
  private matchmakingQueue: Map<string, { playerId: string; criteria: MatchmakingCriteria; timestamp: number }> = new Map();

  constructor() {
    super();
    this.initializeDefaultLobbies();
    this.startMatchmakingLoop();
  }

  // Initialize default lobby configurations
  private initializeDefaultLobbies(): void {
    const defaultLobbies: LobbyConfig[] = [
      {
        id: 'beginner-circuit',
        name: 'Beginner Circuit',
        description: 'Perfect for new racers! Learn the basics with friendly competition.',
        maxPlayers: 4,
        minPlayers: 2,
        entryFee: 25,
        prizeMultiplier: 2.5,
        difficulty: 'Beginner',
        autoStart: true,
        autoStartDelay: 30000, // 30 seconds
        skillMatchmaking: false,
        tokenRequirement: 100,
        track: {
          id: 'track-beginner',
          name: 'Rookie Track',
          length: 800,
          difficulty: 1,
          obstacles: [],
          shortcuts: [],
        },
      },
      {
        id: 'pro-championship',
        name: 'Pro Championship',
        description: 'High-stakes racing for experienced drivers. Big risks, bigger rewards!',
        maxPlayers: 8,
        minPlayers: 4,
        entryFee: 100,
        prizeMultiplier: 4.0,
        difficulty: 'Advanced',
        autoStart: true,
        autoStartDelay: 60000, // 60 seconds
        skillMatchmaking: true,
        tokenRequirement: 500,
        track: {
          id: 'track-pro',
          name: 'Championship Circuit',
          length: 1500,
          difficulty: 4,
          obstacles: [],
          shortcuts: [],
        },
      },
      {
        id: 'speed-demons',
        name: 'Speed Demons',
        description: 'Fast-paced races with maximum power-ups and chaos!',
        maxPlayers: 6,
        minPlayers: 3,
        entryFee: 75,
        prizeMultiplier: 3.5,
        difficulty: 'Intermediate',
        autoStart: true,
        autoStartDelay: 45000, // 45 seconds
        skillMatchmaking: false,
        tokenRequirement: 300,
        track: {
          id: 'track-speed',
          name: 'Velocity Valley',
          length: 1200,
          difficulty: 3,
          obstacles: [],
          shortcuts: [],
        },
      },
      {
        id: 'elite-masters',
        name: 'Elite Masters',
        description: 'The ultimate challenge for racing legends. Only the best survive.',
        maxPlayers: 8,
        minPlayers: 6,
        entryFee: 200,
        prizeMultiplier: 5.0,
        difficulty: 'Expert',
        autoStart: false, // Manual start only
        autoStartDelay: 0,
        skillMatchmaking: true,
        tokenRequirement: 1000,
        track: {
          id: 'track-elite',
          name: 'Masters Circuit',
          length: 2000,
          difficulty: 5,
          obstacles: [],
          shortcuts: [],
        },
      },
    ];

    defaultLobbies.forEach(config => {
      this.lobbyConfigs.set(config.id, config);
    });
  }

  // Create a new lobby instance
  createLobby(configId: string, customSettings?: Partial<LobbySettings>): string | null {
    const config = this.lobbyConfigs.get(configId);
    if (!config) return null;

    const lobbyId = `${configId}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
    
    const lobby: LobbyInstance = {
      config,
      players: new Map(),
      status: 'waiting',
      createdAt: Date.now(),
      readyPlayers: new Set(),
      spectators: new Set(),
      chatMessages: [],
      settings: {
        allowSpectators: true,
        chatEnabled: true,
        privateMode: false,
        customRules: {},
        ...customSettings,
      },
    };

    this.lobbies.set(lobbyId, lobby);
    this.emit('lobbyCreated', { lobbyId, lobby });

    // Add system welcome message
    this.addChatMessage(lobbyId, {
      playerId: 'system',
      playerName: 'System',
      message: `Welcome to ${config.name}! ${config.description}`,
      type: 'system',
    });

    return lobbyId;
  }

  // Join a lobby
  joinLobby(lobbyId: string, player: Player, password?: string): boolean {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby) return false;

    // Check if lobby is full
    if (lobby.players.size >= lobby.config.maxPlayers) {
      return false;
    }

    // Check password for private lobbies
    if (lobby.settings.privateMode && lobby.settings.password !== password) {
      return false;
    }

    // Check token requirements
    if (player.tokenBalance < lobby.config.tokenRequirement) {
      return false;
    }

    // Check if player is already in another lobby
    const currentLobby = this.playerLobbies.get(player.id);
    if (currentLobby) {
      this.leaveLobby(currentLobby, player.id);
    }

    // Add player to lobby
    lobby.players.set(player.id, player);
    this.playerLobbies.set(player.id, lobbyId);

    // Add join message
    this.addChatMessage(lobbyId, {
      playerId: 'system',
      playerName: 'System',
      message: `${player.name} joined the race!`,
      type: 'system',
    });

    this.emit('playerJoinedLobby', { lobbyId, player });

    // Check if lobby should auto-start
    this.checkAutoStart(lobbyId);

    return true;
  }

  // Leave a lobby
  leaveLobby(lobbyId: string, playerId: string): boolean {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby) return false;

    const player = lobby.players.get(playerId);
    if (!player) return false;

    // Remove player
    lobby.players.delete(playerId);
    lobby.readyPlayers.delete(playerId);
    lobby.spectators.delete(playerId);
    this.playerLobbies.delete(playerId);

    // Add leave message
    this.addChatMessage(lobbyId, {
      playerId: 'system',
      playerName: 'System',
      message: `${player.name} left the race.`,
      type: 'system',
    });

    this.emit('playerLeftLobby', { lobbyId, playerId });

    // Clean up empty lobbies
    if (lobby.players.size === 0) {
      this.closeLobby(lobbyId);
    }

    return true;
  }

  // Set player ready status
  setPlayerReady(lobbyId: string, playerId: string, ready: boolean): boolean {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby || !lobby.players.has(playerId)) return false;

    if (ready) {
      lobby.readyPlayers.add(playerId);
    } else {
      lobby.readyPlayers.delete(playerId);
    }

    const player = lobby.players.get(playerId);
    if (player) {
      player.isReady = ready;
    }

    this.emit('playerReadyChanged', { lobbyId, playerId, ready });

    // Check if all players are ready
    if (lobby.readyPlayers.size === lobby.players.size && 
        lobby.players.size >= lobby.config.minPlayers) {
      this.startLobby(lobbyId);
    }

    return true;
  }

  // Add chat message
  addChatMessage(lobbyId: string, messageData: Partial<ChatMessage>): boolean {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby || !lobby.settings.chatEnabled) return false;

    const message: ChatMessage = {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
      playerId: messageData.playerId || 'system',
      playerName: messageData.playerName || 'System',
      message: messageData.message || '',
      timestamp: Date.now(),
      type: messageData.type || 'chat',
    };

    lobby.chatMessages.push(message);

    // Keep only last 50 messages
    if (lobby.chatMessages.length > 50) {
      lobby.chatMessages = lobby.chatMessages.slice(-50);
    }

    this.emit('chatMessage', { lobbyId, message });
    return true;
  }

  // Quick matchmaking
  findMatch(playerId: string, criteria: MatchmakingCriteria): string | null {
    // Add to matchmaking queue
    this.matchmakingQueue.set(playerId, {
      playerId,
      criteria,
      timestamp: Date.now(),
    });

    // Try to find immediate match
    return this.processMatchmaking(playerId);
  }

  // Process matchmaking
  private processMatchmaking(playerId: string): string | null {
    const queueEntry = this.matchmakingQueue.get(playerId);
    if (!queueEntry) return null;

    const { criteria } = queueEntry;

    // Find suitable lobbies
    const suitableLobbies = Array.from(this.lobbies.values()).filter(lobby => {
      if (lobby.status !== 'waiting') return false;
      if (lobby.players.size >= lobby.config.maxPlayers) return false;
      if (lobby.settings.privateMode) return false;

      // Check difficulty preference
      if (criteria.preferredDifficulty && 
          lobby.config.difficulty !== criteria.preferredDifficulty) {
        return false;
      }

      // Check token requirements
      if (criteria.tokenBalance && 
          criteria.tokenBalance < lobby.config.tokenRequirement) {
        return false;
      }

      return true;
    });

    // Sort by preference (fewer players first for faster games)
    suitableLobbies.sort((a, b) => a.players.size - b.players.size);

    if (suitableLobbies.length > 0) {
      const targetLobby = suitableLobbies[0];
      const lobbyId = Array.from(this.lobbies.entries())
        .find(([_, lobby]) => lobby === targetLobby)?.[0];

      if (lobbyId) {
        this.matchmakingQueue.delete(playerId);
        return lobbyId;
      }
    }

    return null;
  }

  // Start matchmaking loop
  private startMatchmakingLoop(): void {
    setInterval(() => {
      // Process matchmaking queue
      for (const [playerId] of this.matchmakingQueue) {
        this.processMatchmaking(playerId);
      }

      // Clean up old queue entries (5 minutes)
      const now = Date.now();
      for (const [playerId, entry] of this.matchmakingQueue) {
        if (now - entry.timestamp > 300000) {
          this.matchmakingQueue.delete(playerId);
        }
      }
    }, 5000); // Every 5 seconds
  }

  // Check auto-start conditions
  private checkAutoStart(lobbyId: string): void {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby || !lobby.config.autoStart) return;

    if (lobby.players.size >= lobby.config.minPlayers && !lobby.startTime) {
      lobby.startTime = Date.now() + lobby.config.autoStartDelay;
      
      this.addChatMessage(lobbyId, {
        playerId: 'system',
        playerName: 'System',
        message: `Race will start in ${lobby.config.autoStartDelay / 1000} seconds!`,
        type: 'announcement',
      });

      setTimeout(() => {
        this.startLobby(lobbyId);
      }, lobby.config.autoStartDelay);
    }
  }

  // Start a lobby (begin race)
  private startLobby(lobbyId: string): void {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby || lobby.status !== 'waiting') return;

    if (lobby.players.size < lobby.config.minPlayers) return;

    lobby.status = 'starting';

    // Create race object
    const race: Race = {
      id: lobbyId,
      name: lobby.config.name,
      players: Array.from(lobby.players.values()),
      maxPlayers: lobby.config.maxPlayers,
      entryFee: lobby.config.entryFee,
      prizePool: lobby.config.entryFee * lobby.players.size * lobby.config.prizeMultiplier,
      status: RaceStatus.STARTING,
      startTime: new Date(),
      duration: 300000, // 5 minutes
      track: lobby.config.track,
    };

    this.emit('lobbyStarting', { lobbyId, race });

    // Clean up lobby after race starts
    setTimeout(() => {
      this.closeLobby(lobbyId);
    }, 10000); // 10 seconds delay
  }

  // Close a lobby
  private closeLobby(lobbyId: string): void {
    const lobby = this.lobbies.get(lobbyId);
    if (!lobby) return;

    // Remove all players from tracking
    for (const playerId of lobby.players.keys()) {
      this.playerLobbies.delete(playerId);
    }

    this.lobbies.delete(lobbyId);
    this.emit('lobbyClosed', { lobbyId });
  }

  // Get lobby info
  getLobby(lobbyId: string): LobbyInstance | undefined {
    return this.lobbies.get(lobbyId);
  }

  // Get all available lobbies
  getAvailableLobbies(): LobbyInstance[] {
    return Array.from(this.lobbies.values()).filter(
      lobby => lobby.status === 'waiting' && !lobby.settings.privateMode
    );
  }

  // Get lobby configs
  getLobbyConfigs(): LobbyConfig[] {
    return Array.from(this.lobbyConfigs.values());
  }

  // Get player's current lobby
  getPlayerLobby(playerId: string): LobbyInstance | undefined {
    const lobbyId = this.playerLobbies.get(playerId);
    return lobbyId ? this.lobbies.get(lobbyId) : undefined;
  }

  // Clean up
  destroy(): void {
    for (const lobbyId of this.lobbies.keys()) {
      this.closeLobby(lobbyId);
    }
    this.removeAllListeners();
  }
}
